import{Ab as x,Bb as P,Da as D,Fc as A,H as _,Sb as S,T as b,Wa as L,Xa as O,Ya as y,Z as k,_ as C,aa as I,c as m,da as n,g as v,ka as u,sb as E,va as g,wa as o,za as w}from"./chunk-L5P5FOLI.js";import{a as c,b as p}from"./chunk-ODN5LVDJ.js";var B=["*"],d=class{_ngZone;_pending=[];_listeners=[];_targetStream=new v(void 0);_clearListeners(){for(let r of this._listeners)r.remove();this._listeners=[]}constructor(r){this._ngZone=r}getLazyEmitter(r){return this._targetStream.pipe(b(e=>{let t=new m(a=>{if(!e){this._pending.push({observable:t,observer:a});return}let s=e.addListener(r,l=>{this._ngZone.run(()=>a.next(l))});if(!s){a.complete();return}return this._listeners.push(s),()=>s.remove()});return t}))}setTarget(r){let e=this._targetStream.value;r!==e&&(e&&(this._clearListeners(),this._pending=[]),this._targetStream.next(r),this._pending.forEach(t=>t.observable.subscribe(t.observer)),this._pending=[])}destroy(){this._clearListeners(),this._pending=[],this._targetStream.complete()}},h={center:{lat:37.421995,lng:-122.084092},zoom:17,mapTypeId:"roadmap"},R="500px",F="500px",Z=(()=>{class i{_elementRef=n(w);_ngZone=n(o);_eventManager=new d(n(o));_mapEl;_existingAuthFailureCallback;googleMap;_isBrowser;height=R;width=F;mapId;mapTypeId;set center(e){this._center=e}_center;set zoom(e){this._zoom=e}_zoom;set options(e){this._options=e||h}_options=h;mapInitialized=new g;authFailure=new g;boundsChanged=this._eventManager.getLazyEmitter("bounds_changed");centerChanged=this._eventManager.getLazyEmitter("center_changed");mapClick=this._eventManager.getLazyEmitter("click");mapDblclick=this._eventManager.getLazyEmitter("dblclick");mapDrag=this._eventManager.getLazyEmitter("drag");mapDragend=this._eventManager.getLazyEmitter("dragend");mapDragstart=this._eventManager.getLazyEmitter("dragstart");headingChanged=this._eventManager.getLazyEmitter("heading_changed");idle=this._eventManager.getLazyEmitter("idle");maptypeidChanged=this._eventManager.getLazyEmitter("maptypeid_changed");mapMousemove=this._eventManager.getLazyEmitter("mousemove");mapMouseout=this._eventManager.getLazyEmitter("mouseout");mapMouseover=this._eventManager.getLazyEmitter("mouseover");projectionChanged=this._eventManager.getLazyEmitter("projection_changed");mapRightclick=this._eventManager.getLazyEmitter("rightclick");tilesloaded=this._eventManager.getLazyEmitter("tilesloaded");tiltChanged=this._eventManager.getLazyEmitter("tilt_changed");zoomChanged=this._eventManager.getLazyEmitter("zoom_changed");constructor(){let e=n(D);if(this._isBrowser=A(e),this._isBrowser){let t=window;t.google,this._existingAuthFailureCallback=t.gm_authFailure,t.gm_authFailure=()=>{this._existingAuthFailureCallback&&this._existingAuthFailureCallback(),this.authFailure.emit()}}}ngOnChanges(e){(e.height||e.width)&&this._setSize();let t=this.googleMap;t&&(e.options&&t.setOptions(this._combineOptions()),e.center&&this._center&&t.setCenter(this._center),e.zoom&&this._zoom!=null&&t.setZoom(this._zoom),e.mapTypeId&&this.mapTypeId&&t.setMapTypeId(this.mapTypeId))}ngOnInit(){this._isBrowser&&(this._mapEl=this._elementRef.nativeElement.querySelector(".map-container"),this._setSize(),google.maps.Map?this._initialize(google.maps.Map):this._ngZone.runOutsideAngular(()=>{google.maps.importLibrary("maps").then(e=>this._initialize(e.Map))}))}_initialize(e){this._ngZone.runOutsideAngular(()=>{this.googleMap=new e(this._mapEl,this._combineOptions()),this._eventManager.setTarget(this.googleMap),this.mapInitialized.emit(this.googleMap)})}ngOnDestroy(){if(this.mapInitialized.complete(),this._eventManager.destroy(),this._isBrowser){let e=window;e.gm_authFailure=this._existingAuthFailureCallback}}fitBounds(e,t){this._assertInitialized(),this.googleMap.fitBounds(e,t)}panBy(e,t){this._assertInitialized(),this.googleMap.panBy(e,t)}panTo(e){this._assertInitialized(),this.googleMap.panTo(e)}panToBounds(e,t){this._assertInitialized(),this.googleMap.panToBounds(e,t)}getBounds(){return this._assertInitialized(),this.googleMap.getBounds()||null}getCenter(){return this._assertInitialized(),this.googleMap.getCenter()}getClickableIcons(){return this._assertInitialized(),this.googleMap.getClickableIcons()}getHeading(){return this._assertInitialized(),this.googleMap.getHeading()}getMapTypeId(){return this._assertInitialized(),this.googleMap.getMapTypeId()}getProjection(){return this._assertInitialized(),this.googleMap.getProjection()||null}getStreetView(){return this._assertInitialized(),this.googleMap.getStreetView()}getTilt(){return this._assertInitialized(),this.googleMap.getTilt()}getZoom(){return this._assertInitialized(),this.googleMap.getZoom()}get controls(){return this._assertInitialized(),this.googleMap.controls}get data(){return this._assertInitialized(),this.googleMap.data}get mapTypes(){return this._assertInitialized(),this.googleMap.mapTypes}get overlayMapTypes(){return this._assertInitialized(),this.googleMap.overlayMapTypes}_resolveMap(){return this.googleMap?Promise.resolve(this.googleMap):this.mapInitialized.pipe(_(1)).toPromise()}_setSize(){if(this._mapEl){let e=this._mapEl.style;e.height=this.height===null?"":T(this.height)||R,e.width=this.width===null?"":T(this.width)||F}}_combineOptions(){let e=this._options||{};return p(c({},e),{center:this._center||e.center||h.center,zoom:this._zoom??e.zoom??h.zoom,mapTypeId:this.mapTypeId||e.mapTypeId||h.mapTypeId,mapId:this.mapId||e.mapId})}_assertInitialized(){this.googleMap}static \u0275fac=function(t){return new(t||i)};static \u0275cmp=L({type:i,selectors:[["google-map"]],inputs:{height:"height",width:"width",mapId:"mapId",mapTypeId:"mapTypeId",center:"center",zoom:"zoom",options:"options"},outputs:{mapInitialized:"mapInitialized",authFailure:"authFailure",boundsChanged:"boundsChanged",centerChanged:"centerChanged",mapClick:"mapClick",mapDblclick:"mapDblclick",mapDrag:"mapDrag",mapDragend:"mapDragend",mapDragstart:"mapDragstart",headingChanged:"headingChanged",idle:"idle",maptypeidChanged:"maptypeidChanged",mapMousemove:"mapMousemove",mapMouseout:"mapMouseout",mapMouseover:"mapMouseover",projectionChanged:"projectionChanged",mapRightclick:"mapRightclick",tilesloaded:"tilesloaded",tiltChanged:"tiltChanged",zoomChanged:"zoomChanged"},exportAs:["googleMap"],features:[u],ngContentSelectors:B,decls:2,vars:0,consts:[[1,"map-container"]],template:function(t,a){t&1&&(x(),E(0,"div",0),P(1))},encapsulation:2,changeDetection:0})}return i})(),G=/([A-Za-z%]+)$/;function T(i){return i==null?"":G.test(i)?i:`${i}px`}var ce=(()=>{class i{_googleMap=n(Z);_ngZone=n(o);_eventManager=new d(n(o));set directions(e){this._directions=e}_directions;set options(e){this._options=e}_options;directionsChanged=this._eventManager.getLazyEmitter("directions_changed");directionsRendererInitialized=new g;directionsRenderer;constructor(){}ngOnInit(){this._googleMap._isBrowser&&(google.maps.DirectionsRenderer&&this._googleMap.googleMap?this._initialize(this._googleMap.googleMap,google.maps.DirectionsRenderer):this._ngZone.runOutsideAngular(()=>{Promise.all([this._googleMap._resolveMap(),google.maps.importLibrary("routes")]).then(([e,t])=>{this._initialize(e,t.DirectionsRenderer)})}))}_initialize(e,t){this._ngZone.runOutsideAngular(()=>{this.directionsRenderer=new t(this._combineOptions()),this._assertInitialized(),this.directionsRenderer.setMap(e),this._eventManager.setTarget(this.directionsRenderer),this.directionsRendererInitialized.emit(this.directionsRenderer)})}ngOnChanges(e){this.directionsRenderer&&(e.options&&this.directionsRenderer.setOptions(this._combineOptions()),e.directions&&this._directions!==void 0&&this.directionsRenderer.setDirections(this._directions))}ngOnDestroy(){this._eventManager.destroy(),this.directionsRenderer?.setMap(null)}getDirections(){return this._assertInitialized(),this.directionsRenderer.getDirections()}getPanel(){return this._assertInitialized(),this.directionsRenderer.getPanel()}getRouteIndex(){return this._assertInitialized(),this.directionsRenderer.getRouteIndex()}_combineOptions(){let e=this._options||{};return p(c({},e),{directions:this._directions||e.directions,map:this._googleMap.googleMap})}_assertInitialized(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=y({type:i,selectors:[["map-directions-renderer"]],inputs:{directions:"directions",options:"options"},outputs:{directionsChanged:"directionsChanged",directionsRendererInitialized:"directionsRendererInitialized"},exportAs:["mapDirectionsRenderer"],features:[u]})}return i})();var W=new I("MAP_MARKER"),j={position:{lat:37.421995,lng:-122.084092}},pe=(()=>{class i{_googleMap=n(Z);_ngZone=n(o);_eventManager=new d(n(o));set title(e){this._title=e}_title;set position(e){this._position=e}_position;set label(e){this._label=e}_label;set clickable(e){this._clickable=e}_clickable;set options(e){this._options=e}_options;set icon(e){this._icon=e}_icon;set visible(e){this._visible=e}_visible;animationChanged=this._eventManager.getLazyEmitter("animation_changed");mapClick=this._eventManager.getLazyEmitter("click");clickableChanged=this._eventManager.getLazyEmitter("clickable_changed");cursorChanged=this._eventManager.getLazyEmitter("cursor_changed");mapDblclick=this._eventManager.getLazyEmitter("dblclick");mapDrag=this._eventManager.getLazyEmitter("drag");mapDragend=this._eventManager.getLazyEmitter("dragend");draggableChanged=this._eventManager.getLazyEmitter("draggable_changed");mapDragstart=this._eventManager.getLazyEmitter("dragstart");flatChanged=this._eventManager.getLazyEmitter("flat_changed");iconChanged=this._eventManager.getLazyEmitter("icon_changed");mapMousedown=this._eventManager.getLazyEmitter("mousedown");mapMouseout=this._eventManager.getLazyEmitter("mouseout");mapMouseover=this._eventManager.getLazyEmitter("mouseover");mapMouseup=this._eventManager.getLazyEmitter("mouseup");positionChanged=this._eventManager.getLazyEmitter("position_changed");mapRightclick=this._eventManager.getLazyEmitter("rightclick");shapeChanged=this._eventManager.getLazyEmitter("shape_changed");titleChanged=this._eventManager.getLazyEmitter("title_changed");visibleChanged=this._eventManager.getLazyEmitter("visible_changed");zindexChanged=this._eventManager.getLazyEmitter("zindex_changed");markerInitialized=new g;marker;constructor(){}ngOnInit(){this._googleMap._isBrowser&&(google.maps.Marker&&this._googleMap.googleMap?this._initialize(this._googleMap.googleMap,google.maps.Marker):this._ngZone.runOutsideAngular(()=>{Promise.all([this._googleMap._resolveMap(),google.maps.importLibrary("marker")]).then(([e,t])=>{this._initialize(e,t.Marker)})}))}_initialize(e,t){this._ngZone.runOutsideAngular(()=>{this.marker=new t(this._combineOptions()),this._assertInitialized(),this.marker.setMap(e),this._eventManager.setTarget(this.marker),this.markerInitialized.next(this.marker)})}ngOnChanges(e){let{marker:t,_title:a,_position:s,_label:l,_clickable:M,_icon:f,_visible:z}=this;t&&(e.options&&t.setOptions(this._combineOptions()),e.title&&a!==void 0&&t.setTitle(a),e.position&&s&&t.setPosition(s),e.label&&l!==void 0&&t.setLabel(l),e.clickable&&M!==void 0&&t.setClickable(M),e.icon&&f&&t.setIcon(f),e.visible&&z!==void 0&&t.setVisible(z))}ngOnDestroy(){this.markerInitialized.complete(),this._eventManager.destroy(),this.marker?.setMap(null)}getAnimation(){return this._assertInitialized(),this.marker.getAnimation()||null}getClickable(){return this._assertInitialized(),this.marker.getClickable()}getCursor(){return this._assertInitialized(),this.marker.getCursor()||null}getDraggable(){return this._assertInitialized(),!!this.marker.getDraggable()}getIcon(){return this._assertInitialized(),this.marker.getIcon()||null}getLabel(){return this._assertInitialized(),this.marker.getLabel()||null}getOpacity(){return this._assertInitialized(),this.marker.getOpacity()||null}getPosition(){return this._assertInitialized(),this.marker.getPosition()||null}getShape(){return this._assertInitialized(),this.marker.getShape()||null}getTitle(){return this._assertInitialized(),this.marker.getTitle()||null}getVisible(){return this._assertInitialized(),this.marker.getVisible()}getZIndex(){return this._assertInitialized(),this.marker.getZIndex()||null}getAnchor(){return this._assertInitialized(),this.marker}_resolveMarker(){return this.marker?Promise.resolve(this.marker):this.markerInitialized.pipe(_(1)).toPromise()}_combineOptions(){let e=this._options||j;return p(c({},e),{title:this._title||e.title,position:this._position||e.position,label:this._label||e.label,clickable:this._clickable??e.clickable,map:this._googleMap.googleMap,icon:this._icon||e.icon,visible:this._visible??e.visible})}_assertInitialized(){}static \u0275fac=function(t){return new(t||i)};static \u0275dir=y({type:i,selectors:[["map-marker"]],inputs:{title:"title",position:"position",label:"label",clickable:"clickable",options:"options",icon:"icon",visible:"visible"},outputs:{animationChanged:"animationChanged",mapClick:"mapClick",clickableChanged:"clickableChanged",cursorChanged:"cursorChanged",mapDblclick:"mapDblclick",mapDrag:"mapDrag",mapDragend:"mapDragend",draggableChanged:"draggableChanged",mapDragstart:"mapDragstart",flatChanged:"flatChanged",iconChanged:"iconChanged",mapMousedown:"mapMousedown",mapMouseout:"mapMouseout",mapMouseover:"mapMouseover",mapMouseup:"mapMouseup",positionChanged:"positionChanged",mapRightclick:"mapRightclick",shapeChanged:"shapeChanged",titleChanged:"titleChanged",visibleChanged:"visibleChanged",zindexChanged:"zindexChanged",markerInitialized:"markerInitialized"},exportAs:["mapMarker"],features:[S([{provide:W,useExisting:i}]),u]})}return i})();var ue=(()=>{class i{static \u0275fac=function(t){return new(t||i)};static \u0275mod=O({type:i});static \u0275inj=C({})}return i})(),me=(()=>{class i{_ngZone=n(o);_directionsService;constructor(){}route(e){return new m(t=>{this._getService().then(a=>{a.route(e,(s,l)=>{this._ngZone.run(()=>{t.next({result:s||void 0,status:l}),t.complete()})})})})}_getService(){if(!this._directionsService)if(google.maps.DirectionsService)this._directionsService=new google.maps.DirectionsService;else return google.maps.importLibrary("routes").then(e=>(this._directionsService=new e.DirectionsService,this._directionsService));return Promise.resolve(this._directionsService)}static \u0275fac=function(t){return new(t||i)};static \u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();export{Z as a,ce as b,pe as c,ue as d,me as e};
