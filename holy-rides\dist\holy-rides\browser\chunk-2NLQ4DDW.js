import{a as y,c as w,d as F}from"./chunk-XGZOMXTW.js";import{C as Y,F as H,H as V,I as K,J as X,b as T,d as p,f as I,g as A,k as N,n as D,o as B,s as j,u as k,w as q,x as G}from"./chunk-UDNA73AU.js";import{B as v,Ec as E,Fd as L,Id as R,Kb as l,Kd as U,La as m,Lb as O,Ld as z,Mb as S,Md as $,Pa as g,Pd as J,Qd as Q,Wa as M,ab as u,hb as c,o as h,qb as o,rb as n,sb as _,vc as b,yb as P,zb as x}from"./chunk-L5P5FOLI.js";import"./chunk-X5YLR3NI.js";import{i as C}from"./chunk-ODN5LVDJ.js";function Z(t,r){t&1&&(o(0,"mat-error"),l(1,"Email is required"),n())}function ee(t,r){t&1&&(o(0,"mat-error"),l(1,"Please enter a valid email"),n())}function te(t,r){t&1&&(o(0,"mat-error"),l(1,"Password is required"),n())}function ne(t,r){t&1&&(o(0,"mat-error"),l(1,"Password must be at least 6 characters"),n())}function re(t,r){if(t&1&&(o(0,"div",11),l(1),n()),t&2){let i=x();m(),O(i.error)}}var W=class t{constructor(r,i,e,s){this.formBuilder=r;this.router=i;this.route=e;this.authService=s;this.loginForm=this.formBuilder.group({email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]]})}loginForm;error="";loading=!1;MAX_ROLE_CHECK_ATTEMPTS=3;RETRY_DELAY=1e3;onSubmit(){return C(this,null,function*(){if(!this.loginForm.invalid){this.loading=!0,this.error="";try{let{error:r}=yield this.authService.login(this.loginForm.value.email,this.loginForm.value.password);if(r){this.error=r.message;return}let i=yield h(this.authService.user$.pipe(v(a=>a!==null)));if(i&&!i.is_approved){let a=i.role==="admin"?"Your account has been deactivated. Please contact support.":"Your account is pending approval. Please wait for administrator approval.";this.error=a,yield this.authService.logout();return}let e=null,s=0;for(;!e&&s<this.MAX_ROLE_CHECK_ATTEMPTS;)console.log("Attempting to get user role..."),e=yield this.authService.getUserRole(),!e&&s<this.MAX_ROLE_CHECK_ATTEMPTS-1&&(yield new Promise(a=>setTimeout(a,this.RETRY_DELAY))),s++;if(e){let a=this.authService.getDashboardRouteForRole(e);yield this.router.navigate([a])}else this.error="Unable to determine user role. Please try logging in again.",yield this.authService.logout()}catch(r){console.error("Login error:",r),this.error="An error occurred during login. Please try again."}finally{this.loading=!1}}})}static \u0275fac=function(i){return new(i||t)(g(j),g(w),g(y),g(X))};static \u0275cmp=M({type:t,selectors:[["app-login"]],decls:26,vars:8,consts:[[1,"login-container"],[3,"ngSubmit","formGroup"],["appearance","outline"],["matInput","","type","email","formControlName","email","placeholder","Enter your email"],[4,"ngIf"],["matInput","","type","password","formControlName","password","placeholder","Enter your password"],["class","error-message",4,"ngIf"],[1,"button-container"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"links"],["routerLink","/auth/register"],[1,"error-message"]],template:function(i,e){if(i&1&&(o(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),l(4,"Login"),n()(),o(5,"mat-card-content")(6,"form",1),P("ngSubmit",function(){return e.onSubmit()}),o(7,"mat-form-field",2)(8,"mat-label"),l(9,"Email"),n(),_(10,"input",3),u(11,Z,2,0,"mat-error",4)(12,ee,2,0,"mat-error",4),n(),o(13,"mat-form-field",2)(14,"mat-label"),l(15,"Password"),n(),_(16,"input",5),u(17,te,2,0,"mat-error",4)(18,ne,2,0,"mat-error",4),n(),u(19,re,2,1,"div",6),o(20,"div",7)(21,"button",8),l(22),n()(),o(23,"div",9)(24,"a",10),l(25,"Don't have an account? Register"),n()()()()()()),i&2){let s,a,d,f;m(6),c("formGroup",e.loginForm),m(5),c("ngIf",(s=e.loginForm.get("email"))==null||s.errors==null?null:s.errors.required),m(),c("ngIf",(a=e.loginForm.get("email"))==null||a.errors==null?null:a.errors.email),m(5),c("ngIf",(d=e.loginForm.get("password"))==null||d.errors==null?null:d.errors.required),m(),c("ngIf",(f=e.loginForm.get("password"))==null||f.errors==null?null:f.errors.minlength),m(),c("ngIf",e.error),m(2),c("disabled",e.loginForm.invalid||e.loading),m(),S(" ",e.loading?"Logging in...":"Login"," ")}},dependencies:[E,b,k,N,T,I,A,D,B,H,Y,q,G,K,V,R,L,Q,U,$,J,z,F],styles:[".login-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:100vh;padding:20px}.login-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-bottom:30px}.login-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{width:100px;margin-bottom:10px}.login-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#3f51b5;margin:0}.login-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px}.login-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.login-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{margin-top:24px;display:flex;justify-content:center}.login-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;padding:8px}.login-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]{margin-top:16px;text-align:center}.login-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.login-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.login-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#f44336;text-align:center;margin:8px 0}"]})};export{W as LoginComponent};
