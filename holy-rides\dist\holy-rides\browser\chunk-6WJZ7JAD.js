import{a as v,b as P,c as O,e as y,f as F,l as dt,p as mt,s as pt}from"./chunk-FMN32A34.js";import{Dd as I,Ea as U,Eb as S,Fb as C,Fd as ct,Ga as X,Gb as T,H as x,Id as lt,Kb as D,La as u,Ma as K,Mb as E,Qc as st,U as z,Wa as A,Xa as Y,Ya as b,Z as H,_ as V,_a as $,_c as R,aa as g,ab as w,da as o,dc as nt,ed as ot,f as c,gb as G,gd as rt,jb as J,k as N,la as q,ma as W,mb as tt,mc as it,qb as k,ra as _,rb as h,sb as et,wa as Z,wb as at,yb as B,za as Q,zb as M}from"./chunk-L5P5FOLI.js";import{a as d}from"./chunk-ODN5LVDJ.js";var ut={XSmall:"(max-width: 599.98px)",Small:"(min-width: 600px) and (max-width: 959.98px)",Medium:"(min-width: 960px) and (max-width: 1279.98px)",Large:"(min-width: 1280px) and (max-width: 1919.98px)",XLarge:"(min-width: 1920px)",Handset:"(max-width: 599.98px) and (orientation: portrait), (max-width: 959.98px) and (orientation: landscape)",Tablet:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",Web:"(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)",HandsetPortrait:"(max-width: 599.98px) and (orientation: portrait)",TabletPortrait:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)",WebPortrait:"(min-width: 840px) and (orientation: portrait)",HandsetLandscape:"(max-width: 959.98px) and (orientation: landscape)",TabletLandscape:"(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",WebLandscape:"(min-width: 1280px) and (orientation: landscape)"};function kt(n,l){if(n&1){let t=at();k(0,"div",1)(1,"button",2),B("click",function(){q(t);let a=M();return W(a.action())}),D(2),h()()}if(n&2){let t=M();u(2),E(" ",t.data.action," ")}}var vt=["label"];function yt(n,l){}var xt=Math.pow(2,31)-1,f=class{_overlayRef;instance;containerInstance;_afterDismissed=new c;_afterOpened=new c;_onAction=new c;_durationTimeoutId;_dismissedByAction=!1;constructor(l,t){this._overlayRef=t,this.containerInstance=l,l._onExit.subscribe(()=>this._finishDismiss())}dismiss(){this._afterDismissed.closed||this.containerInstance.exit(),clearTimeout(this._durationTimeoutId)}dismissWithAction(){this._onAction.closed||(this._dismissedByAction=!0,this._onAction.next(),this._onAction.complete(),this.dismiss()),clearTimeout(this._durationTimeoutId)}closeWithAction(){this.dismissWithAction()}_dismissAfter(l){this._durationTimeoutId=setTimeout(()=>this.dismiss(),Math.min(l,xt))}_open(){this._afterOpened.closed||(this._afterOpened.next(),this._afterOpened.complete())}_finishDismiss(){this._overlayRef.dispose(),this._onAction.closed||this._onAction.complete(),this._afterDismissed.next({dismissedByAction:this._dismissedByAction}),this._afterDismissed.complete(),this._dismissedByAction=!1}afterDismissed(){return this._afterDismissed}afterOpened(){return this.containerInstance._onEnter}onAction(){return this._onAction}},ht=new g("MatSnackBarData"),m=class{politeness="assertive";announcementMessage="";viewContainerRef;duration=0;panelClass;direction;data=null;horizontalPosition="center";verticalPosition="bottom"},gt=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275dir=b({type:n,selectors:[["","matSnackBarLabel",""]],hostAttrs:[1,"mat-mdc-snack-bar-label","mdc-snackbar__label"]})}return n})(),At=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275dir=b({type:n,selectors:[["","matSnackBarActions",""]],hostAttrs:[1,"mat-mdc-snack-bar-actions","mdc-snackbar__actions"]})}return n})(),wt=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275dir=b({type:n,selectors:[["","matSnackBarAction",""]],hostAttrs:[1,"mat-mdc-snack-bar-action","mdc-snackbar__action"]})}return n})(),ft=(()=>{class n{snackBarRef=o(f);data=o(ht);constructor(){}action(){this.snackBarRef.dismissWithAction()}get hasAction(){return!!this.data.action}static \u0275fac=function(e){return new(e||n)};static \u0275cmp=A({type:n,selectors:[["simple-snack-bar"]],hostAttrs:[1,"mat-mdc-simple-snack-bar"],exportAs:["matSnackBar"],decls:3,vars:2,consts:[["matSnackBarLabel",""],["matSnackBarActions",""],["mat-button","","matSnackBarAction","",3,"click"]],template:function(e,a){e&1&&(k(0,"div",0),D(1),h(),w(2,kt,3,1,"div",1)),e&2&&(u(),E(" ",a.data.message,`
`),u(),tt(a.hasAction?2:-1))},dependencies:[ct,gt,At,wt],styles:[`.mat-mdc-simple-snack-bar{display:flex}
`],encapsulation:2,changeDetection:0})}return n})(),j="_mat-snack-bar-enter",L="_mat-snack-bar-exit",Bt=(()=>{class n extends O{_ngZone=o(Z);_elementRef=o(Q);_changeDetectorRef=o(nt);_platform=o(st);_rendersRef;_animationsDisabled=o(U,{optional:!0})==="NoopAnimations";snackBarConfig=o(m);_document=o(it);_trackedModals=new Set;_enterFallback;_exitFallback;_renders=new c;_announceDelay=150;_announceTimeoutId;_destroyed=!1;_portalOutlet;_onAnnounce=new c;_onExit=new c;_onEnter=new c;_animationState="void";_live;_label;_role;_liveElementId=o(rt).getId("mat-snack-bar-container-live-");constructor(){super();let t=this.snackBarConfig;t.politeness==="assertive"&&!t.announcementMessage?this._live="assertive":t.politeness==="off"?this._live="off":this._live="polite",this._platform.FIREFOX&&(this._live==="polite"&&(this._role="status"),this._live==="assertive"&&(this._role="alert")),this._rendersRef=X(()=>this._renders.next(),{manualCleanup:!0})}attachComponentPortal(t){this._assertNotAttached();let e=this._portalOutlet.attachComponentPortal(t);return this._afterPortalAttached(),e}attachTemplatePortal(t){this._assertNotAttached();let e=this._portalOutlet.attachTemplatePortal(t);return this._afterPortalAttached(),e}attachDomPortal=t=>{this._assertNotAttached();let e=this._portalOutlet.attachDomPortal(t);return this._afterPortalAttached(),e};onAnimationEnd(t){t===L?this._completeExit():t===j&&(clearTimeout(this._enterFallback),this._ngZone.run(()=>{this._onEnter.next(),this._onEnter.complete()}))}enter(){this._destroyed||(this._animationState="visible",this._changeDetectorRef.markForCheck(),this._changeDetectorRef.detectChanges(),this._screenReaderAnnounce(),this._animationsDisabled?this._renders.pipe(x(1)).subscribe(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(j)))}):(clearTimeout(this._enterFallback),this._enterFallback=setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-snack-bar-fallback-visible"),this.onAnimationEnd(j)},200)))}exit(){return this._destroyed?N(void 0):(this._ngZone.run(()=>{this._animationState="hidden",this._changeDetectorRef.markForCheck(),this._elementRef.nativeElement.setAttribute("mat-exit",""),clearTimeout(this._announceTimeoutId),this._animationsDisabled?this._renders.pipe(x(1)).subscribe(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(L)))}):(clearTimeout(this._exitFallback),this._exitFallback=setTimeout(()=>this.onAnimationEnd(L),200))}),this._onExit)}ngOnDestroy(){this._destroyed=!0,this._clearFromModals(),this._completeExit(),this._renders.complete(),this._rendersRef.destroy()}_completeExit(){clearTimeout(this._exitFallback),queueMicrotask(()=>{this._onExit.next(),this._onExit.complete()})}_afterPortalAttached(){let t=this._elementRef.nativeElement,e=this.snackBarConfig.panelClass;e&&(Array.isArray(e)?e.forEach(s=>t.classList.add(s)):t.classList.add(e)),this._exposeToModals();let a=this._label.nativeElement,i="mdc-snackbar__label";a.classList.toggle(i,!a.querySelector(`.${i}`))}_exposeToModals(){let t=this._liveElementId,e=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let a=0;a<e.length;a++){let i=e[a],s=i.getAttribute("aria-owns");this._trackedModals.add(i),s?s.indexOf(t)===-1&&i.setAttribute("aria-owns",s+" "+t):i.setAttribute("aria-owns",t)}}_clearFromModals(){this._trackedModals.forEach(t=>{let e=t.getAttribute("aria-owns");if(e){let a=e.replace(this._liveElementId,"").trim();a.length>0?t.setAttribute("aria-owns",a):t.removeAttribute("aria-owns")}}),this._trackedModals.clear()}_assertNotAttached(){this._portalOutlet.hasAttached()}_screenReaderAnnounce(){this._announceTimeoutId||this._ngZone.runOutsideAngular(()=>{this._announceTimeoutId=setTimeout(()=>{if(this._destroyed)return;let t=this._elementRef.nativeElement,e=t.querySelector("[aria-hidden]"),a=t.querySelector("[aria-live]");if(e&&a){let i=null;this._platform.isBrowser&&document.activeElement instanceof HTMLElement&&e.contains(document.activeElement)&&(i=document.activeElement),e.removeAttribute("aria-hidden"),a.appendChild(e),i?.focus(),this._onAnnounce.next(),this._onAnnounce.complete()}},this._announceDelay)})}static \u0275fac=function(e){return new(e||n)};static \u0275cmp=A({type:n,selectors:[["mat-snack-bar-container"]],viewQuery:function(e,a){if(e&1&&(S(y,7),S(vt,7)),e&2){let i;C(i=T())&&(a._portalOutlet=i.first),C(i=T())&&(a._label=i.first)}},hostAttrs:[1,"mdc-snackbar","mat-mdc-snack-bar-container"],hostVars:6,hostBindings:function(e,a){e&1&&B("animationend",function(s){return a.onAnimationEnd(s.animationName)})("animationcancel",function(s){return a.onAnimationEnd(s.animationName)}),e&2&&J("mat-snack-bar-container-enter",a._animationState==="visible")("mat-snack-bar-container-exit",a._animationState==="hidden")("mat-snack-bar-container-animations-enabled",!a._animationsDisabled)},features:[$],decls:6,vars:3,consts:[["label",""],[1,"mdc-snackbar__surface","mat-mdc-snackbar-surface"],[1,"mat-mdc-snack-bar-label"],["aria-hidden","true"],["cdkPortalOutlet",""]],template:function(e,a){e&1&&(k(0,"div",1)(1,"div",2,0)(3,"div",3),w(4,yt,0,0,"ng-template",4),h(),et(5,"div"),h()()),e&2&&(u(5),G("aria-live",a._live)("role",a._role)("id",a._liveElementId))},dependencies:[y],styles:[`@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}
`],encapsulation:2})}return n})();function Mt(){return new m}var St=new g("mat-snack-bar-default-options",{providedIn:"root",factory:Mt}),Ct=(()=>{class n{_overlay=o(mt);_live=o(ot);_injector=o(_);_breakpointObserver=o(R);_parentSnackBar=o(n,{optional:!0,skipSelf:!0});_defaultConfig=o(St);_snackBarRefAtThisLevel=null;simpleSnackBarComponent=ft;snackBarContainerComponent=Bt;handsetCssClass="mat-mdc-snack-bar-handset";get _openedSnackBarRef(){let t=this._parentSnackBar;return t?t._openedSnackBarRef:this._snackBarRefAtThisLevel}set _openedSnackBarRef(t){this._parentSnackBar?this._parentSnackBar._openedSnackBarRef=t:this._snackBarRefAtThisLevel=t}constructor(){}openFromComponent(t,e){return this._attach(t,e)}openFromTemplate(t,e){return this._attach(t,e)}open(t,e="",a){let i=d(d({},this._defaultConfig),a);return i.data={message:t,action:e},i.announcementMessage===t&&(i.announcementMessage=void 0),this.openFromComponent(this.simpleSnackBarComponent,i)}dismiss(){this._openedSnackBarRef&&this._openedSnackBarRef.dismiss()}ngOnDestroy(){this._snackBarRefAtThisLevel&&this._snackBarRefAtThisLevel.dismiss()}_attachSnackBarContainer(t,e){let a=e&&e.viewContainerRef&&e.viewContainerRef.injector,i=_.create({parent:a||this._injector,providers:[{provide:m,useValue:e}]}),s=new v(this.snackBarContainerComponent,e.viewContainerRef,i),r=t.attach(s);return r.instance.snackBarConfig=e,r.instance}_attach(t,e){let a=d(d(d({},new m),this._defaultConfig),e),i=this._createOverlay(a),s=this._attachSnackBarContainer(i,a),r=new f(s,i);if(t instanceof K){let p=new P(t,null,{$implicit:a.data,snackBarRef:r});r.instance=s.attachTemplatePortal(p)}else{let p=this._createInjector(a,r),_t=new v(t,void 0,p),bt=s.attachComponentPortal(_t);r.instance=bt.instance}return this._breakpointObserver.observe(ut.HandsetPortrait).pipe(z(i.detachments())).subscribe(p=>{i.overlayElement.classList.toggle(this.handsetCssClass,p.matches)}),a.announcementMessage&&s._onAnnounce.subscribe(()=>{this._live.announce(a.announcementMessage,a.politeness)}),this._animateSnackBar(r,a),this._openedSnackBarRef=r,this._openedSnackBarRef}_animateSnackBar(t,e){t.afterDismissed().subscribe(()=>{this._openedSnackBarRef==t&&(this._openedSnackBarRef=null),e.announcementMessage&&this._live.clear()}),e.duration&&e.duration>0&&t.afterOpened().subscribe(()=>t._dismissAfter(e.duration)),this._openedSnackBarRef?(this._openedSnackBarRef.afterDismissed().subscribe(()=>{t.containerInstance.enter()}),this._openedSnackBarRef.dismiss()):t.containerInstance.enter()}_createOverlay(t){let e=new dt;e.direction=t.direction;let a=this._overlay.position().global(),i=t.direction==="rtl",s=t.horizontalPosition==="left"||t.horizontalPosition==="start"&&!i||t.horizontalPosition==="end"&&i,r=!s&&t.horizontalPosition!=="center";return s?a.left("0"):r?a.right("0"):a.centerHorizontally(),t.verticalPosition==="top"?a.top("0"):a.bottom("0"),e.positionStrategy=a,this._overlay.create(e)}_createInjector(t,e){let a=t&&t.viewContainerRef&&t.viewContainerRef.injector;return _.create({parent:a||this._injector,providers:[{provide:f,useValue:e},{provide:ht,useValue:t.data}]})}static \u0275fac=function(e){return new(e||n)};static \u0275prov=H({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var re=(()=>{class n{static \u0275fac=function(e){return new(e||n)};static \u0275mod=Y({type:n});static \u0275inj=V({providers:[Ct],imports:[pt,F,lt,I,ft,I]})}return n})();export{Ct as a,re as b};
