import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { AuthService, UserRole } from '../../../core/services/auth.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    RouterLink
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  registerForm: FormGroup;
  error: string = '';
  loading: boolean = false;
  roles: { value: UserRole; label: string }[] = [
    { value: 'rider', label: 'Rider' },
    { value: 'driver', label: 'Driver' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private authService: AuthService
  ) {
    this.registerForm = this.formBuilder.group({
      full_name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      phone: ['', [Validators.required]],
      role: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(g: FormGroup) {
    return g.get('password')?.value === g.get('confirmPassword')?.value
      ? null
      : { mismatch: true };
  }

  async onSubmit() {
    if (this.registerForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = '';

    try {
      // Register the user
      const { error: registerError } = await this.authService.register(
        this.registerForm.value.email,
        this.registerForm.value.password,
        this.registerForm.value.role,
        this.registerForm.value.phone,
        this.registerForm.value.full_name
      );

      if (registerError) {
        this.error = registerError.message;
        return;
      }

      // Add a small delay to ensure profile is created
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Auto login after successful registration
      const { error: loginError } = await this.authService.login(
        this.registerForm.value.email,
        this.registerForm.value.password
      );

      if (loginError) {
        this.error = loginError.message;
        return;
      }

      // Navigate to profile page after successful registration
      await this.router.navigate(['/auth/profile']);

    } catch (err: any) {
      this.error = err.message;
    } finally {
      this.loading = false;
    }
  }
}
