import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { UserService } from './user.service';
import { Ride } from '../models/ride.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class SmsService {
  private supabase: SupabaseClient;

  constructor(
    private userService: UserService,
    private authService: AuthService
  ) {
    this.supabase = authService.supabase;
  }

  /**
   * Send an SMS notification to a user via Supabase Twilio lambda function
   * @param to Phone number to send the SMS to
   * @param body Message body
   * @returns Promise resolving to the message SID if successful
   */
  async sendSms(to: string, body: string): Promise<string> {
    try {
      // Format phone number if needed (ensure it has the + prefix)

      const formattedPhone = this.formatPhoneNumber(to);

      // Call the Supabase lambda function to send the SMS
      const { data, error } = await this.supabase.functions.invoke('twilio', {
        body: {
          to: formattedPhone,
          message: body,
          from: "+17272025413"
        }
      });

      if (error) {
        console.error('Error calling Twilio lambda function:', error);
        throw error;
      }

      if (!data || !data.sid) {
        throw new Error('No message SID returned from Twilio lambda function');
      }

      console.log(`SMS sent successfully to ${to}, SID: ${data.sid}`);
      return data.sid;
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }

  /**
   * Send ride assignment notifications to both rider and driver
   * @param ride The ride that was assigned
   * @param driverId The ID of the driver assigned to the ride
   */
  async sendRideAssignmentNotifications(ride: Ride, driverId: string): Promise<void> {
    try {
      // Get rider and driver information
      const [rider, driver] = await Promise.all([
        this.userService.getUserById(ride.rider_id),
        this.userService.getUserById(driverId)
      ]);

      if (!rider || !driver) {
        throw new Error('Could not find rider or driver information');
      }

      // Check if phone numbers are available
      if (!rider.phone || !driver.phone) {
        console.warn('Phone number missing for rider or driver. SMS notification skipped.');
        return;
      }

      // Format pickup time for better readability
      const pickupTime = new Date(ride.pickup_time).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      });

      // Send notification to rider with more detailed information
      const riderMessage = `Your ride has been assigned to driver ${driver.full_name || 'a driver'}. They will pick you up at ${ride.pickup_location} at ${pickupTime}. Driver phone: ${driver.phone}`;

      // Send notification to driver with more detailed information
      const driverMessage = `You have been assigned a new ride. Pick up ${rider.full_name || 'your rider'} at ${ride.pickup_location} at ${pickupTime} and drop off at ${ride.dropoff_location}. Rider phone: ${rider.phone}`;

      // Send both messages in parallel for efficiency
      const results = await Promise.allSettled([
        this.sendSmsWithRetry(rider.phone, riderMessage),
        this.sendSmsWithRetry(driver.phone, driverMessage)
      ]);

      // Check results and log any failures
      const [riderResult, driverResult] = results;

      if (riderResult.status === 'rejected') {
        console.error('Failed to send SMS to rider:', riderResult.reason);
      }

      if (driverResult.status === 'rejected') {
        console.error('Failed to send SMS to driver:', driverResult.reason);
      }

      if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {
        console.log('Ride assignment notifications sent successfully to both rider and driver');
      } else if (riderResult.status === 'fulfilled' || driverResult.status === 'fulfilled') {
        console.log('Ride assignment notifications sent partially (not to all recipients)');
      } else {
        console.error('Failed to send ride assignment notifications to any recipient');
      }
    } catch (error) {
      console.error('Error sending ride assignment notifications:', error);
      // Don't throw the error - we don't want to break the ride assignment process
      // if SMS sending fails
    }
  }

  /**
   * Send SMS with retry logic for better reliability
   * @param to Phone number to send the SMS to
   * @param body Message body
   * @param maxRetries Maximum number of retry attempts
   * @returns Promise resolving to the message SID if successful
   */
  private async sendSmsWithRetry(to: string, body: string, maxRetries = 2): Promise<string> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Wait a bit before retrying (exponential backoff)
        if (attempt > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
        }

        return await this.sendSms("+1"+to, body);
      } catch (error) {
        lastError = error;
        console.warn(`SMS sending attempt ${attempt + 1}/${maxRetries + 1} failed:`, error);
      }
    }

    // If we get here, all attempts failed
    throw lastError || new Error('Failed to send SMS after multiple attempts');
  }

  /**
   * Send ride status update notifications to both rider and driver
   * @param ride The ride that had its status updated
   * @param newStatus The new status of the ride
   */
  async sendRideStatusUpdateNotifications(ride: Ride, newStatus: string): Promise<void> {
    try {
      // Skip if the ride doesn't have both rider and driver assigned
      if (!ride.rider_id || !ride.driver_id) {
        console.warn('Ride is missing rider or driver ID. Status update notification skipped.');
        return;
      }

      // Get rider and driver information
      const [rider, driver] = await Promise.all([
        this.userService.getUserById(ride.rider_id),
        this.userService.getUserById(ride.driver_id)
      ]);

      if (!rider || !driver) {
        throw new Error('Could not find rider or driver information');
      }

      // Check if phone numbers are available
      if (!rider.phone || !driver.phone) {
        console.warn('Phone number missing for rider or driver. Status update notification skipped.');
        return;
      }

      // Create appropriate messages based on the new status
      let riderMessage = '';
      let driverMessage = '';

      switch (newStatus) {
        case 'in-progress':
          riderMessage = `Your ride has started. Your driver ${driver.full_name || 'is'} on the way to ${ride.dropoff_location}.`;
          driverMessage = `You have started the ride with ${rider.full_name || 'your rider'}. Destination: ${ride.dropoff_location}.`;
          break;
        case 'completed':
          riderMessage = `Your ride to ${ride.dropoff_location} has been completed. Thank you for using Holy Rides!`;
          driverMessage = `You have completed the ride to ${ride.dropoff_location}. Thank you for your service!`;
          break;
        case 'canceled':
          riderMessage = `Your ride has been canceled. Please contact support if you did not request this cancellation.`;
          driverMessage = `The ride to ${ride.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;
          break;
        default:
          // Don't send notifications for other status changes
          return;
      }

      // Send both messages in parallel
      const results = await Promise.allSettled([
        this.sendSmsWithRetry(rider.phone, riderMessage),
        this.sendSmsWithRetry(driver.phone, driverMessage)
      ]);

      // Check results and log any failures
      const [riderResult, driverResult] = results;

      if (riderResult.status === 'rejected') {
        console.error('Failed to send status update SMS to rider:', riderResult.reason);
      }

      if (driverResult.status === 'rejected') {
        console.error('Failed to send status update SMS to driver:', driverResult.reason);
      }

      if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {
        console.log(`Ride status update (${newStatus}) notifications sent successfully to both rider and driver`);
      }
    } catch (error) {
      console.error('Error sending ride status update notifications:', error);
      // Don't throw the error - we don't want to break the status update process
      // if SMS sending fails
    }
  }

  /**
   * Format phone number to ensure it has the international format with + prefix
   * @param phoneNumber The phone number to format
   * @returns Formatted phone number
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // If the phone number already starts with +, return it as is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }

    // If it starts with a country code without +, add the +
    if (phoneNumber.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)) {
      return `+${phoneNumber}`;
    }

    // Otherwise, assume it's a US number without country code
    // and add +1 (you can modify this based on your target country)
    return `+1${phoneNumber.replace(/\D/g, '')}`;
  }
}
