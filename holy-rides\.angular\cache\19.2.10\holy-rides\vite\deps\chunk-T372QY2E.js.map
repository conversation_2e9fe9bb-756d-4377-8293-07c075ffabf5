{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/divider.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nclass MatDivider {\n  /** Whether the divider is vertically aligned. */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  _vertical = false;\n  /** Whether the divider is an inset divider. */\n  get inset() {\n    return this._inset;\n  }\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n  _inset = false;\n  static ɵfac = function MatDivider_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDivider)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDivider,\n    selectors: [[\"mat-divider\"]],\n    hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n    hostVars: 7,\n    hostBindings: function MatDivider_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n        i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n      }\n    },\n    inputs: {\n      vertical: \"vertical\",\n      inset: \"inset\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatDivider_Template(rf, ctx) {},\n    styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\nclass MatDividerModule {\n  static ɵfac = function MatDividerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDividerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDividerModule,\n    imports: [MatCommonModule, MatDivider],\n    exports: [MatDivider, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatDivider],\n      exports: [MatDivider, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatDivider, MatDividerModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA,EAEf,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,sBAAsB,KAAK;AAAA,EAC3C;AAAA,EACA,SAAS;AAAA,EACT,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,WAAW,CAAC,QAAQ,aAAa,GAAG,aAAa;AAAA,IACjD,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,oBAAoB,IAAI,WAAW,aAAa,YAAY;AAC3E,QAAG,YAAY,wBAAwB,IAAI,QAAQ,EAAE,0BAA0B,CAAC,IAAI,QAAQ,EAAE,qBAAqB,IAAI,KAAK;AAAA,MAC9H;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAAA,IAAC;AAAA,IACjD,QAAQ,CAAC,yeAAye;AAAA,IAClf,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,2BAA2B;AAAA,QAC3B,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,6BAA6B;AAAA,QAC7B,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,yeAAye;AAAA,IACpf,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,UAAU;AAAA,IACrC,SAAS,CAAC,YAAY,eAAe;AAAA,EACvC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,UAAU;AAAA,MACrC,SAAS,CAAC,YAAY,eAAe;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}