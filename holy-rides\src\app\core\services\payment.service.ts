import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { BehaviorSubject, Observable } from 'rxjs';
import { Ride, PaymentStatus } from '../models/ride.model';
import { DriverPayout, PayoutStatus } from '../models/payout.model';
import { RidePricing } from '../models/ride-pricing.model';
import { AuthService } from './auth.service';
import { LocationService } from './location.service';
import { RidePricingService } from './ride-pricing.service';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private supabase: SupabaseClient;
  private payoutsSubject = new BehaviorSubject<DriverPayout[]>([]);
  payouts$ = this.payoutsSubject.asObservable();

  constructor(
    private authService: AuthService,
    private locationService: LocationService,
    private ridePricingService: RidePricingService
  ) {
    this.supabase = authService.supabase;
  }

  /**
   * Calculate fare for a ride based on distance and time
   * Uses pricing variables from the database
   */
  async calculateFare(distance: number, duration: number): Promise<number> {
    // Get the active pricing configuration
    const pricing = await this.ridePricingService.loadActivePricing();
    console.log(pricing)
    // Use default values if no pricing configuration is found
    const baseFare = pricing?.base_fare ?? 5.00;
    const distanceRate = pricing?.distance_rate ?? 1.50; // per mile
    const timeRate = pricing?.time_rate ?? 0.25; // per minute

    return +(baseFare + (distance * distanceRate) + (duration * timeRate));
  }

  /**
   * Estimate fare for a ride using Google Maps API to calculate distance and time
   */
// ...existing code...
  /**
   * Estimate fare for a ride using Google Maps API to calculate distance and time
   * Returns both fare and routeInfo
   */
  async estimateFare(pickup: string, dropoff: string): Promise<{ fare: number, routeInfo: any }> {
    try {
      // Use the LocationService to calculate the route
      const routeInfo = await this.locationService.calculateRoute(pickup, dropoff);
      console.log(routeInfo)
      // Calculate fare based on the actual distance and duration
      const fare = await this.calculateFare(routeInfo.distance, routeInfo.duration);
      console.log(fare)
      return { fare, routeInfo };
    } catch (error) {
      console.error('Error estimating fare:', error);

      // Fallback to a default calculation if there's an error
      const distance = 10; // Default 10 miles
      const duration = 20; // Default 20 minutes
      const fare = await this.calculateFare(distance, duration);
      return { fare, routeInfo: { distance, duration } };
    }
  }
// ...existing code...

  /**
   * Create a payment intent with Square
   * In a real app, this would call Square API
   */
  async createPaymentIntent(rideId: string, amount: number): Promise<{ clientSecret: string, paymentId: string }> {
    // Mock implementation - in a real app, you would call Square API
    // This would typically be done via a secure backend endpoint

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Generate mock client secret and payment ID
    const clientSecret = `sq_${Math.random().toString(36).substring(2, 15)}`;
    const paymentId = `pmt_${Math.random().toString(36).substring(2, 15)}`;

    // Update ride with payment ID and status
    await this.updateRidePaymentDetails(rideId, {
      payment_id: paymentId,
      payment_status: 'pending',
      amount: amount
    });

    return { clientSecret, paymentId };
  }

  /**
   * Process a payment with Square
   * In a real app, this would call Square API
   */
  async processPayment(rideId: string, paymentMethodId: string): Promise<boolean> {
    try {
      // Mock implementation - in a real app, you would call Square API
      // This would typically be done via a secure backend endpoint

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 90% success rate for demo purposes
      const success = Math.random() > 0.1;

      if (success) {
        // Update ride payment status to completed
        await this.updateRidePaymentStatus(rideId, 'completed');

        // Create driver payout record
        const ride = await this.getRide(rideId);
        if (ride && ride.driver_id && ride.amount) {
          await this.createDriverPayout(ride.driver_id, rideId, ride.amount, 'amount');
        }
      } else {
        // Update ride payment status to failed
        await this.updateRidePaymentStatus(rideId, 'failed');
      }

      return success;
    } catch (error) {
      console.error('Error processing payment:', error);
      await this.updateRidePaymentStatus(rideId, 'failed');
      return false;
    }
  }

  /**
   * Process a refund with Square
   * In a real app, this would call Square API
   */
  async processRefund(rideId: string): Promise<boolean> {
    try {
      // Mock implementation - in a real app, you would call Square API
      // This would typically be done via a secure backend endpoint

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 90% success rate for demo purposes
      const success = Math.random() > 0.1;

      if (success) {
        // Update ride payment status to refunded
        await this.updateRidePaymentStatus(rideId, 'refunded');

        // Update driver payout status to failed
        const ride = await this.getRide(rideId);
        if (ride && ride.driver_id) {
          await this.updateDriverPayoutStatus(ride.driver_id, rideId, 'failed');
        }
      }

      return success;
    } catch (error) {
      console.error('Error processing refund:', error);
      return false;
    }
  }

  /**
   * Get a ride by ID
   */
  private async getRide(rideId: string): Promise<Ride | null> {
    try {
      const { data, error } = await this.supabase
        .from('rides')
        .select('*')
        .eq('id', rideId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching ride:', error);
      return null;
    }
  }

  /**
   * Update ride payment details
   */
  private async updateRidePaymentDetails(rideId: string, details: {
    payment_id?: string;
    payment_status?: PaymentStatus;
    amount?: number;
  }): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('rides')
        .update({
          ...details,
          updated_at: new Date().toISOString()
        })
        .eq('id', rideId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating ride payment details:', error);
      return false;
    }
  }

  /**
   * Update ride payment status
   */
  async updateRidePaymentStatus(rideId: string, status: PaymentStatus): Promise<boolean> {
    return this.updateRidePaymentDetails(rideId, { payment_status: status });
  }

  /**
   * Create a driver payout record
   */
  private async createDriverPayout(
    driverId: string,
    rideId: string,
    amount: number,
    payoutType: 'amount' | 'percentage' = 'amount',
    percentage?: number
  ): Promise<boolean> {
    try {
      const payoutData: any = {
        driver_id: driverId,
        ride_id: rideId,
        fare: amount,
        status: 'pending',
        payout_type: payoutType
      };

      // Add percentage if provided and type is percentage
      if (payoutType === 'percentage' && percentage) {
        payoutData.percentage = percentage;
      }

      const { error } = await this.supabase
        .from('driver_payouts')
        .insert([payoutData]);

      if (error) throw error;

      // Refresh payouts
      await this.getDriverPayouts(driverId);
      return true;
    } catch (error) {
      console.error('Error creating driver payout:', error);
      return false;
    }
  }

  /**
   * Update driver payout status
   */
  async updateDriverPayoutStatus(driverId: string, rideId: string, status: PayoutStatus): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('driver_payouts')
        .update({
          status: status,
          updated_at: new Date().toISOString()
        })
        .eq('driver_id', driverId)
        .eq('ride_id', rideId);

      if (error) throw error;

      // Refresh payouts
      await this.getDriverPayouts(driverId);
      return true;
    } catch (error) {
      console.error('Error updating driver payout status:', error);
      return false;
    }
  }

  /**
   * Update driver payout amount
   */
  async updateDriverPayoutAmount(
    driverId: string,
    rideId: string,
    amount: number,
    payoutType: 'amount' | 'percentage' = 'amount',
    percentage?: number
  ): Promise<boolean> {
    try {
      const updateData: any = {
        amount: amount,
        payout_type: payoutType,
        updated_at: new Date().toISOString()
      };

      // Add percentage if provided and type is percentage
      if (payoutType === 'percentage' && percentage) {
        updateData.percentage = percentage;
      }

      const { error } = await this.supabase
        .from('driver_payouts')
        .update(updateData)
        .eq('driver_id', driverId)
        .eq('ride_id', rideId);

      if (error) throw error;

      // Refresh payouts
      await this.getDriverPayouts(driverId);
      return true;
    } catch (error) {
      console.error('Error updating driver payout amount:', error);
      return false;
    }
  }

  /**
   * Get driver payouts
   */
  async getDriverPayouts(driverId: string): Promise<DriverPayout[]> {
    try {
      const { data, error } = await this.supabase
        .from('driver_payouts')
        .select('*')
        .eq('driver_id', driverId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      this.payoutsSubject.next(data);
      return data;
    } catch (error) {
      console.error('Error fetching driver payouts:', error);
      return [];
    }
  }

  /**
   * Get total driver earnings
   */
  async getDriverTotalEarnings(driverId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .from('driver_payouts')
        .select('amount')
        .eq('driver_id', driverId)
        .eq('status', 'paid');

      if (error) throw error;

      return data.reduce((total, payout) => total + payout.amount, 0);
    } catch (error) {
      console.error('Error calculating driver earnings:', error);
      return 0;
    }
  }

  /**
   * Get pending driver earnings
   */
  async getDriverPendingEarnings(driverId: string): Promise<number> {
    try {
      const { data, error } = await this.supabase
        .from('driver_payouts')
        .select('amount')
        .eq('driver_id', driverId)
        .eq('status', 'pending');

      if (error) throw error;

      return data.reduce((total, payout) => total + payout.amount, 0);
    } catch (error) {
      console.error('Error calculating pending earnings:', error);
      return 0;
    }
  }
}
