import{a as De}from"./chunk-CTHHBRRU.js";import{a as we,b as Re}from"./chunk-NXDGXY44.js";import{C as be,F as ye,H as Me,I as Ce,J as h,b as ue,f as ge,g as fe,h as he,j as ve,k as _e,t as Se}from"./chunk-UDNA73AU.js";import{$b as ae,Dd as I,Ea as G,Eb as Z,Ec as le,Fb as J,Fd as me,Gb as X,Id as pe,Jb as ee,Kb as _,Kd as Ie,La as d,Lb as q,Ld as Pe,Md as ke,Ob as re,Od as xe,Pa as D,Pb as te,Pd as Ee,Qb as ie,Qd as Te,Vb as se,Wa as w,Xa as O,Z as y,_ as E,aa as V,ab as R,ca as g,da as T,g as x,gb as f,gc as $,hb as m,ib as j,jb as U,kb as Q,na as B,oa as Y,qb as c,rb as l,sb as M,sd as z,tc as oe,uc as ne,vb as A,vc as ce,yb as K,za as H,zb as C,zc as de}from"./chunk-L5P5FOLI.js";import{a as S,b,i as o}from"./chunk-ODN5LVDJ.js";var F=class s{constructor(t){this.authService=t;this.supabase=t.supabase}supabase;usersSubject=new x([]);users$=this.usersSubject.asObservable();getAllUsers(){return o(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("profiles").select("*").order("created_at",{ascending:!1});if(e)throw e;return this.usersSubject.next(t),t}catch(t){return console.error("Error fetching users:",t),[]}})}getUsersByRole(t){return o(this,null,function*(){try{let{data:e,error:r}=yield this.supabase.from("profiles").select("*").eq("role",t).order("created_at",{ascending:!1});if(r)throw r;return e}catch(e){return console.error(`Error fetching ${t}s:`,e),[]}})}approveDriver(t){return o(this,null,function*(){try{let{error:e}=yield this.supabase.from("profiles").update({is_approved:"TRUE"}).eq("id",t);if(e)throw e;let i=this.usersSubject.value.map(a=>a.id===t?b(S({},a),{is_approved:!0,is_active:!0}):a);return this.usersSubject.next(i),!0}catch(e){return console.error("Error approving driver:",e),!1}})}updateUserStatus(t,e){return o(this,null,function*(){try{let{data:r,error:i}=yield this.supabase.from("profiles").select("role, is_approved").eq("id",t).single();if(i)throw i;if(e&&r.role!=="admin"&&!r.is_approved)throw new Error("Cannot activate unapproved user");let{error:a}=yield this.supabase.from("profiles").update({is_approved:e,updated_at:new Date().toISOString()}).eq("id",t);if(a)throw a;let v=this.usersSubject.value.map(p=>p.id===t?b(S({},p),{is_approved:e}):p);return this.usersSubject.next(v),!0}catch(r){return console.error("Error updating user status:",r),!1}})}getUserById(t){return o(this,null,function*(){try{let{data:e,error:r}=yield this.supabase.from("profiles").select("*").eq("id",t).single();if(r)throw r;return e}catch(e){return console.error("Error fetching user by ID:",e),null}})}filterUsers(t,e){return t.filter(r=>{if(e.role&&r.role!==e.role||e.isApproved!==void 0&&r.role==="driver"&&r.is_approved!==e.isApproved)return!1;if(e.searchTerm){let i=e.searchTerm.toLowerCase(),a=r.full_name?.toLowerCase()||"",n=r.email.toLowerCase(),v=r.phone?.toLowerCase()||"";if(!a.includes(i)&&!n.includes(i)&&!v.includes(i))return!1}return!0})}static \u0275fac=function(e){return new(e||s)(g(h))};static \u0275prov=y({token:s,factory:s.\u0275fac,providedIn:"root"})};var N=class s{constructor(t,e){this.userService=t;this.authService=e;this.supabase=e.supabase}supabase;sendSms(t,e){return o(this,null,function*(){try{let r=this.formatPhoneNumber(t),{data:i,error:a}=yield this.supabase.functions.invoke("twilio",{body:{to:r,message:e,from:"+17272025413"}});if(a)throw console.error("Error calling Twilio lambda function:",a),a;if(!i||!i.sid)throw new Error("No message SID returned from Twilio lambda function");return console.log(`SMS sent successfully to ${t}, SID: ${i.sid}`),i.sid}catch(r){throw console.error("Error sending SMS:",r),r}})}sendRideAssignmentNotifications(t,e){return o(this,null,function*(){try{let[r,i]=yield Promise.all([this.userService.getUserById(t.rider_id),this.userService.getUserById(e)]);if(!r||!i)throw new Error("Could not find rider or driver information");if(!r.phone||!i.phone){console.warn("Phone number missing for rider or driver. SMS notification skipped.");return}let a=new Date(t.pickup_time).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),n=`Your ride has been assigned to driver ${i.full_name||"a driver"}. They will pick you up at ${t.pickup_location} at ${a}. Driver phone: ${i.phone}`,v=`You have been assigned a new ride. Pick up ${r.full_name||"your rider"} at ${t.pickup_location} at ${a} and drop off at ${t.dropoff_location}. Rider phone: ${r.phone}`,p=yield Promise.allSettled([this.sendSmsWithRetry(r.phone,n),this.sendSmsWithRetry(i.phone,v)]),[u,k]=p;u.status==="rejected"&&console.error("Failed to send SMS to rider:",u.reason),k.status==="rejected"&&console.error("Failed to send SMS to driver:",k.reason),u.status==="fulfilled"&&k.status==="fulfilled"?console.log("Ride assignment notifications sent successfully to both rider and driver"):u.status==="fulfilled"||k.status==="fulfilled"?console.log("Ride assignment notifications sent partially (not to all recipients)"):console.error("Failed to send ride assignment notifications to any recipient")}catch(r){console.error("Error sending ride assignment notifications:",r)}})}sendSmsWithRetry(t,e,r=2){return o(this,null,function*(){let i;for(let a=0;a<=r;a++)try{return a>0&&(yield new Promise(n=>setTimeout(n,1e3*Math.pow(2,a-1)))),yield this.sendSms("+1"+t,e)}catch(n){i=n,console.warn(`SMS sending attempt ${a+1}/${r+1} failed:`,n)}throw i||new Error("Failed to send SMS after multiple attempts")})}sendRideStatusUpdateNotifications(t,e){return o(this,null,function*(){try{if(!t.rider_id||!t.driver_id){console.warn("Ride is missing rider or driver ID. Status update notification skipped.");return}let[r,i]=yield Promise.all([this.userService.getUserById(t.rider_id),this.userService.getUserById(t.driver_id)]);if(!r||!i)throw new Error("Could not find rider or driver information");if(!r.phone||!i.phone){console.warn("Phone number missing for rider or driver. Status update notification skipped.");return}let a="",n="";switch(e){case"in-progress":a=`Your ride has started. Your driver ${i.full_name||"is"} on the way to ${t.dropoff_location}.`,n=`You have started the ride with ${r.full_name||"your rider"}. Destination: ${t.dropoff_location}.`;break;case"completed":a=`Your ride to ${t.dropoff_location} has been completed. Thank you for using Holy Rides!`,n=`You have completed the ride to ${t.dropoff_location}. Thank you for your service!`;break;case"canceled":a="Your ride has been canceled. Please contact support if you did not request this cancellation.",n=`The ride to ${t.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;break;default:return}let v=yield Promise.allSettled([this.sendSmsWithRetry(r.phone,a),this.sendSmsWithRetry(i.phone,n)]),[p,u]=v;p.status==="rejected"&&console.error("Failed to send status update SMS to rider:",p.reason),u.status==="rejected"&&console.error("Failed to send status update SMS to driver:",u.reason),p.status==="fulfilled"&&u.status==="fulfilled"&&console.log(`Ride status update (${e}) notifications sent successfully to both rider and driver`)}catch(r){console.error("Error sending ride status update notifications:",r)}})}formatPhoneNumber(t){return t.startsWith("+")?t:t.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)?`+${t}`:`+1${t.replace(/\D/g,"")}`}static \u0275fac=function(e){return new(e||s)(g(F),g(h))};static \u0275prov=y({token:s,factory:s.\u0275fac,providedIn:"root"})};var W=class s{constructor(t,e){this.smsService=t;this.authService=e;this._supabase=e.supabase,this.initializeRealTimeSubscription()}_supabase;ridesSubject=new x([]);rides$=this.ridesSubject.asObservable();rideSubscription=null;get supabase(){return this._supabase}initializeRealTimeSubscription(){this.rideSubscription=this._supabase.channel("rides-channel").on("postgres_changes",{event:"*",schema:"public",table:"rides"},()=>o(this,null,function*(){yield this.refreshRides()})).subscribe()}refreshRides(){return o(this,null,function*(){try{let{data:t,error:e}=yield this._supabase.from("rides").select("*").order("created_at",{ascending:!1});if(e)throw e;this.ridesSubject.next(t||[])}catch(t){console.error("Error refreshing rides:",t)}})}getAllRides(){return o(this,null,function*(){try{let{data:t,error:e}=yield this._supabase.from("rides").select("*").order("created_at",{ascending:!1});if(e)throw e;return this.ridesSubject.next(t||[]),t||[]}catch(t){return console.error("Error fetching all rides:",t),this.ridesSubject.value}})}getRidesByStatus(t){return o(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("status",t).order("created_at",{ascending:!1});if(r)throw r;return e}catch(e){return console.error(`Error fetching rides with status ${t}:`,e),[]}})}assignRideToDriver(t,e){return o(this,null,function*(){try{let r=yield this.getRide(t);if(!r||r.status!=="requested"&&r.status!=="assigned")throw new Error("Ride is no longer available for assignment");let{error:i}=yield this._supabase.from("rides").update({driver_id:e,status:"assigned",updated_at:new Date().toISOString()}).eq("id",t).in("status",["requested","assigned"]);if(i)throw i;let a=yield this.getRide(t);return a&&(setTimeout(()=>o(this,null,function*(){try{yield this.smsService.sendRideAssignmentNotifications(a,e)}catch(n){console.error("Error sending SMS notifications:",n)}}),0),console.log(`Sending ride assignment notifications for ride ${t} to rider and driver ${e}`)),!0}catch(r){throw console.error("Error assigning ride to driver:",r),r}})}updateRideStatus(t,e){return o(this,null,function*(){try{let r=yield this.getRide(t);if(!r)throw new Error("Ride not found");if(!this.isValidStatusTransition(r.status,e))throw new Error(`Invalid status transition from ${r.status} to ${e}`);let{error:i}=yield this._supabase.from("rides").update({status:e,updated_at:new Date().toISOString()}).eq("id",t);if(i)throw i;yield this.refreshRides();let a=yield this.getRide(t);return a&&["in-progress","completed","canceled"].includes(e)&&(setTimeout(()=>o(this,null,function*(){try{yield this.smsService.sendRideStatusUpdateNotifications(a,e)}catch(n){console.error("Error sending status update notifications:",n)}}),0),console.log(`Sending ride status update (${e}) notifications for ride ${t}`)),!0}catch(r){throw console.error("Error updating ride status:",r),r}})}isValidStatusTransition(t,e){return{requested:["assigned","canceled"],assigned:["in-progress","canceled"],"in-progress":["completed","canceled"],completed:[],canceled:[]}[t]?.includes(e)||!1}createRide(t){return o(this,null,function*(){try{let e=b(S({},t),{payment_status:t.payment_status||"pending"}),{data:r,error:i}=yield this._supabase.from("rides").insert([e]).select().single();if(i)throw i;let a=this.ridesSubject.value;return this.ridesSubject.next([...a,r]),r}catch(e){throw console.error("Error creating ride:",e),e}})}getUserRides(t){return o(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("rider_id",t).order("created_at",{ascending:!1});if(r)throw r;return this.ridesSubject.next(e),e}catch(e){return console.error("Error fetching user rides:",e),[]}})}getDriverRides(t){return o(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("driver_id",t).order("created_at",{ascending:!1});if(r)throw r;return e}catch(e){return console.error("Error fetching driver rides:",e),[]}})}getAvailableRides(){return o(this,null,function*(){try{let{data:t,error:e}=yield this._supabase.from("rides").select("*").eq("status","requested").order("created_at",{ascending:!1});if(e)throw e;return t}catch(t){return console.error("Error fetching available rides:",t),[]}})}acceptRide(t,e){return o(this,null,function*(){return this.assignRideToDriver(t,e)})}startRide(t){return o(this,null,function*(){return this.updateRideStatus(t,"in-progress")})}completeRide(t){return o(this,null,function*(){return this.updateRideStatus(t,"completed")})}getRide(t){return o(this,null,function*(){try{let{data:e,error:r}=yield this._supabase.from("rides").select("*").eq("id",t).single();if(r)throw r;return e}catch(e){return console.error("Error fetching ride:",e),null}})}updateRide(t,e){return o(this,null,function*(){try{let{error:r}=yield this._supabase.from("rides").update(b(S({},e),{updated_at:new Date().toISOString()})).eq("id",t);if(r)throw r;return yield this.getAllRides(),!0}catch(r){return console.error("Error updating ride:",r),!1}})}cancelRide(t){return o(this,null,function*(){return this.updateRideStatus(t,"canceled")})}filterRides(t,e){return t.filter(r=>{if(e.status&&r.status!==e.status||e.riderId&&r.rider_id!==e.riderId||e.driverId&&r.driver_id!==e.driverId)return!1;if(e.dateRange){let i=new Date(r.created_at),a=e.dateRange.start,n=e.dateRange.end;if(i<a||i>n)return!1}return!0})}static \u0275fac=function(e){return new(e||s)(g(N),g(h))};static \u0275prov=y({token:s,factory:s.\u0275fac,providedIn:"root"})};var We=["determinateSpinner"];function Be(s,t){if(s&1&&(B(),c(0,"svg",11),M(1,"circle",12),l()),s&2){let e=C();f("viewBox",e._viewBox()),d(),j("stroke-dasharray",e._strokeCircumference(),"px")("stroke-dashoffset",e._strokeCircumference()/2,"px")("stroke-width",e._circleStrokeWidth(),"%"),f("r",e._circleRadius())}}var qe=new V("mat-progress-spinner-default-options",{providedIn:"root",factory:ze});function ze(){return{diameter:Oe}}var Oe=100,Le=10,je=(()=>{class s{_elementRef=T(H);_noopAnimations;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;_defaultColor="primary";_determinateCircle;constructor(){let e=T(G,{optional:!0}),r=T(qe);this._noopAnimations=e==="NoopAnimations"&&!!r&&!r._forceAnimations,this.mode=this._elementRef.nativeElement.nodeName.toLowerCase()==="mat-spinner"?"indeterminate":"determinate",r&&(r.color&&(this.color=this._defaultColor=r.color),r.diameter&&(this.diameter=r.diameter),r.strokeWidth&&(this.strokeWidth=r.strokeWidth))}mode;get value(){return this.mode==="determinate"?this._value:0}set value(e){this._value=Math.max(0,Math.min(100,e||0))}_value=0;get diameter(){return this._diameter}set diameter(e){this._diameter=e||0}_diameter=Oe;get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(e){this._strokeWidth=e||0}_strokeWidth;_circleRadius(){return(this.diameter-Le)/2}_viewBox(){let e=this._circleRadius()*2+this.strokeWidth;return`0 0 ${e} ${e}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return this.mode==="determinate"?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static \u0275fac=function(r){return new(r||s)};static \u0275cmp=w({type:s,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(r,i){if(r&1&&Z(We,5),r&2){let a;J(a=X())&&(i._determinateCircle=a.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(r,i){r&2&&(f("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow",i.mode==="determinate"?i.value:null)("mode",i.mode),Q("mat-"+i.color),j("width",i.diameter,"px")("height",i.diameter,"px")("--mdc-circular-progress-size",i.diameter+"px")("--mdc-circular-progress-active-indicator-width",i.diameter+"px"),U("_mat-animation-noopable",i._noopAnimations)("mdc-circular-progress--indeterminate",i.mode==="indeterminate"))},inputs:{color:"color",mode:"mode",value:[2,"value","value",$],diameter:[2,"diameter","diameter",$],strokeWidth:[2,"strokeWidth","strokeWidth",$]},exportAs:["matProgressSpinner"],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(r,i){if(r&1&&(R(0,Be,2,8,"ng-template",null,0,ae),c(2,"div",2,1),B(),c(4,"svg",3),M(5,"circle",4),l()(),Y(),c(6,"div",5)(7,"div",6)(8,"div",7),A(9,8),l(),c(10,"div",9),A(11,8),l(),c(12,"div",10),A(13,8),l()()()),r&2){let a=ee(1);d(4),f("viewBox",i._viewBox()),d(),j("stroke-dasharray",i._strokeCircumference(),"px")("stroke-dashoffset",i._strokeDashOffset(),"px")("stroke-width",i._circleStrokeWidth(),"%"),f("r",i._circleRadius()),d(4),m("ngTemplateOutlet",a),d(2),m("ngTemplateOutlet",a),d(2),m("ngTemplateOutlet",a)}},dependencies:[de],styles:[`.mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}
`],encapsulation:2,changeDetection:0})}return s})();var Ue=(()=>{class s{static \u0275fac=function(r){return new(r||s)};static \u0275mod=O({type:s});static \u0275inj=E({imports:[I]})}return s})();var vr=(()=>{class s{get vertical(){return this._vertical}set vertical(e){this._vertical=z(e)}_vertical=!1;get inset(){return this._inset}set inset(e){this._inset=z(e)}_inset=!1;static \u0275fac=function(r){return new(r||s)};static \u0275cmp=w({type:s,selectors:[["mat-divider"]],hostAttrs:["role","separator",1,"mat-divider"],hostVars:7,hostBindings:function(r,i){r&2&&(f("aria-orientation",i.vertical?"vertical":"horizontal"),U("mat-divider-vertical",i.vertical)("mat-divider-horizontal",!i.vertical)("mat-divider-inset",i.inset))},inputs:{vertical:"vertical",inset:"inset"},decls:0,vars:0,template:function(r,i){},styles:[`.mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}
`],encapsulation:2,changeDetection:0})}return s})(),_r=(()=>{class s{static \u0275fac=function(r){return new(r||s)};static \u0275mod=O({type:s});static \u0275inj=E({imports:[I,I]})}return s})();var Ye=(s,t)=>({sent:s,received:t});function He(s,t){s&1&&(c(0,"div",9),M(1,"mat-spinner",10),c(2,"p"),_(3,"Loading messages..."),l()())}function Ge(s,t){s&1&&(c(0,"div",11)(1,"p"),_(2,"No messages yet. Start the conversation!"),l()())}function Qe(s,t){if(s&1&&(c(0,"div",14)(1,"div",15),_(2),l(),c(3,"div",16),_(4),l()()),s&2){let e=t.$implicit,r=C(2);m("ngClass",se(3,Ye,e.sender_id===r.currentUserId,e.sender_id!==r.currentUserId)),d(2),q(e.content),d(2),q(r.formatTime(e.created_at))}}function Ke(s,t){if(s&1&&(c(0,"div",12),R(1,Qe,5,6,"div",13),l()),s&2){let e=C();d(),m("ngForOf",e.messages)}}var Ae=class s{constructor(t,e,r){this.messageService=t;this.authService=e;this.rideService=r}threadId;rideId;messages=[];newMessage="";loading=!1;sending=!1;currentUserId="";receiverId="";ngOnInit(){return o(this,null,function*(){this.loading=!0;try{let t=yield this.authService.getCurrentUser();if(t&&(this.currentUserId=t.id),this.rideId&&!this.threadId){let e=yield this.messageService.getOrCreateThreadForRide(this.rideId);this.threadId=e.id}if(this.rideId){let e=yield this.rideService.getRide(this.rideId);e&&(this.receiverId=e.rider_id===this.currentUserId?e.driver_id:e.rider_id)}this.threadId&&(this.messages=yield this.messageService.getThreadMessages(this.threadId),yield this.messageService.markMessagesAsRead(this.threadId))}catch(t){console.error("Error initializing chat:",t)}finally{this.loading=!1}})}sendMessage(){return o(this,null,function*(){if(!(!this.newMessage||!this.threadId||!this.receiverId)){this.sending=!0;try{yield this.messageService.sendMessage(this.threadId,this.receiverId,this.newMessage),this.newMessage="",this.messages=yield this.messageService.getThreadMessages(this.threadId)}catch(t){console.error("Error sending message:",t)}finally{this.sending=!1}}})}formatTime(t){return new Date(t).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}static \u0275fac=function(e){return new(e||s)(D(De),D(h),D(W))};static \u0275cmp=w({type:s,selectors:[["app-ride-chat"]],inputs:{threadId:"threadId",rideId:"rideId"},decls:16,vars:5,consts:[[1,"chat-card"],[1,"messages-container"],["class","loading-container",4,"ngIf"],["class","no-messages",4,"ngIf"],["class","messages-list",4,"ngIf"],[1,"message-form",3,"ngSubmit"],["appearance","outline",1,"message-input"],["matInput","","name","newMessage","placeholder","Type a message...","autocomplete","off",3,"ngModelChange","ngModel"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"loading-container"],["diameter","40"],[1,"no-messages"],[1,"messages-list"],["class","message-bubble",3,"ngClass",4,"ngFor","ngForOf"],[1,"message-bubble",3,"ngClass"],[1,"message-content"],[1,"message-time"]],template:function(e,r){e&1&&(c(0,"mat-card",0)(1,"mat-card-header")(2,"mat-card-title"),_(3,"Chat"),l()(),c(4,"mat-card-content")(5,"div",1),R(6,He,4,0,"div",2)(7,Ge,3,0,"div",3)(8,Ke,2,1,"div",4),l()(),c(9,"mat-card-actions")(10,"form",5),K("ngSubmit",function(){return r.sendMessage()}),c(11,"mat-form-field",6)(12,"input",7),ie("ngModelChange",function(a){return te(r.newMessage,a)||(r.newMessage=a),a}),l()(),c(13,"button",8)(14,"mat-icon"),_(15,"send"),l()()()()()),e&2&&(d(6),m("ngIf",r.loading),d(),m("ngIf",!r.loading&&r.messages.length===0),d(),m("ngIf",!r.loading&&r.messages.length>0),d(4),re("ngModel",r.newMessage),d(),m("disabled",!r.newMessage||r.sending))},dependencies:[le,oe,ne,ce,Se,_e,ue,ge,fe,ve,he,Te,Ie,xe,ke,Ee,Pe,ye,be,Ce,Me,pe,me,Re,we,Ue,je],styles:[".chat-card[_ngcontent-%COMP%]{margin:16px;max-width:800px}.messages-container[_ngcontent-%COMP%]{height:300px;overflow-y:auto;padding:16px;background-color:#f5f5f5;border-radius:4px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.no-messages[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:100%;color:#00000080}.messages-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.message-bubble[_ngcontent-%COMP%]{max-width:80%;padding:8px 12px;border-radius:16px;position:relative}.sent[_ngcontent-%COMP%]{align-self:flex-end;background-color:#2196f3;color:#fff;border-bottom-right-radius:4px}.received[_ngcontent-%COMP%]{align-self:flex-start;background-color:#fff;border-bottom-left-radius:4px}.message-content[_ngcontent-%COMP%]{word-break:break-word}.message-time[_ngcontent-%COMP%]{font-size:.7em;opacity:.7;text-align:right;margin-top:4px}.message-form[_ngcontent-%COMP%]{display:flex;gap:8px;width:100%;padding:0 16px 16px}.message-input[_ngcontent-%COMP%]{flex:1}"]})};export{F as a,W as b,je as c,Ue as d,vr as e,_r as f,Ae as g};
