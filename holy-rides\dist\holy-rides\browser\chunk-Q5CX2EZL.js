import{a as z,b as N,c as B,d as V,e as Z}from"./chunk-JFL7URHX.js";import{a as K,b as H}from"./chunk-NXDGXY44.js";import{Ac as U,Da as G,Ec as F,Fc as T,Fd as E,Id as q,Ja as x,Jd as J,Kb as _,Kd as Q,La as p,Md as W,Pa as b,Qd as X,Wa as A,Wb as $,Xb as R,Z as w,ab as v,ca as I,g as k,hb as r,ib as O,ka as P,q as S,qb as u,rb as d,sb as C,vc as j,zb as y}from"./chunk-L5P5FOLI.js";import{i as g}from"./chunk-ODN5LVDJ.js";var f=class i{constructor(e){this.platformId=e}isLoaded=!1;loadingPromise=null;loadGoogleMapsApi(){return T(this.platformId)?this.loadingPromise?this.loadingPromise:this.isLoaded?Promise.resolve():(this.loadingPromise=new Promise((e,o)=>{if(window.google&&window.google.maps){this.isLoaded=!0,e();return}let t=`googleMapsApiCallback_${Math.round(Math.random()*1e6)}`;window[t]=()=>{this.isLoaded=!0,e(),delete window[t]};let n=document.createElement("script");n.src=`https://maps.googleapis.com/maps/api/js?key=${J.googleMapsApiKey}&libraries=places&callback=${t}`,n.async=!0,n.defer=!0,n.onerror=a=>{o(new Error(`Failed to load Google Maps API: ${a}`)),delete window[t]},document.head.appendChild(n)}),this.loadingPromise):Promise.resolve()}isGoogleMapsLoaded(){return this.isLoaded}static \u0275fac=function(o){return new(o||i)(I(G))};static \u0275prov=w({token:i,factory:i.\u0275fac,providedIn:"root"})};var D=class i{constructor(e){this.googleMapsLoader=e}currentLocationSubject=new k(null);currentLocation$=this.currentLocationSubject.asObservable();getCurrentLocation(){return new Promise((e,o)=>{if(!navigator.geolocation){o(new Error("Geolocation is not supported by your browser"));return}navigator.geolocation.getCurrentPosition(t=>{let n={latitude:t.coords.latitude,longitude:t.coords.longitude};this.currentLocationSubject.next(n),e(n)},t=>{o(t)},{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})})}geocodeAddress(e){return g(this,null,function*(){try{return yield this.googleMapsLoader.loadGoogleMapsApi(),new Promise((o,t)=>{if(!google||!google.maps||!google.maps.Geocoder){console.warn("Google Maps Geocoder not available, using mock data");let a=40+(Math.random()*10-5),s=-74+(Math.random()*10-5);o({latitude:a,longitude:s});return}new google.maps.Geocoder().geocode({address:e},(a,s)=>{if(s===google.maps.GeocoderStatus.OK&&a&&a.length>0){let l=a[0].geometry.location;o({latitude:l.lat(),longitude:l.lng()})}else{console.warn(`Geocoding failed for address: ${e}. Status: ${s}`);let l=40+(Math.random()*10-5),c=-74+(Math.random()*10-5);o({latitude:l,longitude:c})}})})}catch(o){return console.error("Failed to load Google Maps API:",o),{latitude:40+(Math.random()*10-5),longitude:-74+(Math.random()*10-5)}}})}calculateRoute(e,o){return g(this,null,function*(){try{return yield this.googleMapsLoader.loadGoogleMapsApi(),new Promise((t,n)=>{if(!google||!google.maps||!google.maps.DirectionsService){console.warn("Google Maps DirectionsService not available, using mock data");let h=Math.floor(Math.random()*18)+2,M=Math.floor(Math.random()*55)+5;t({distance:h,duration:M,polyline:"mock_polyline_string"});return}let a=new google.maps.DirectionsService,s=typeof e=="string"?e:`${e.latitude},${e.longitude}`,l=typeof o=="string"?o:`${o.latitude},${o.longitude}`,c={origin:s,destination:l,travelMode:google.maps.TravelMode.DRIVING};a.route(c,(h,M)=>{if(M===google.maps.DirectionsStatus.OK&&h){let m=h.routes[0];if(m&&m.legs&&m.legs.length>0){let L=m.legs[0],te=L.distance?L.distance.value/1609.34:0,ie=L.duration?Math.ceil(L.duration.value/60):0,ne=m.overview_polyline?m.overview_polyline:"";t({distance:parseFloat(te.toFixed(2)),duration:ie,polyline:ne})}else console.warn("No route found"),t({distance:Math.floor(Math.random()*18)+2,duration:Math.floor(Math.random()*55)+5,polyline:"mock_polyline_string"})}else console.warn(`Directions request failed. Status: ${M}`),t({distance:Math.floor(Math.random()*18)+2,duration:Math.floor(Math.random()*55)+5,polyline:"mock_polyline_string"})})})}catch(t){return console.error("Failed to load Google Maps API:",t),{distance:Math.floor(Math.random()*18)+2,duration:Math.floor(Math.random()*55)+5,polyline:"mock_polyline_string"}}})}getGoogleMapsUrl(e){return`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(e)}`}getGoogleMapsDirectionsUrl(e,o){return`https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(e)}&destination=${encodeURIComponent(o)}&travelmode=driving`}static \u0275fac=function(o){return new(o||i)(I(f))};static \u0275prov=w({token:i,factory:i.\u0275fac,providedIn:"root"})};function ae(i,e){i&1&&(u(0,"div",5)(1,"p"),_(2,"Loading map..."),d()())}function se(i,e){if(i&1&&C(0,"map-marker",9),i&2){let o=y(2);r("position",o.originMarker)("title","Origin")("options",o.markerOptions)}}function le(i,e){if(i&1&&C(0,"map-marker",9),i&2){let o=y(2);r("position",o.destinationMarker)("title","Destination")("options",o.markerOptions)}}function pe(i,e){if(i&1&&C(0,"map-directions-renderer",10),i&2){let o=e.ngIf;r("directions",o)}}function de(i,e){if(i&1&&(u(0,"google-map",6),v(1,se,1,3,"map-marker",7)(2,le,1,3,"map-marker",7)(3,pe,1,1,"map-directions-renderer",8),$(4,"async"),d()),i&2){let o=y();r("center",o.center)("zoom",o.zoom)("options",o.options),p(),r("ngIf",o.originMarker),p(),r("ngIf",o.destinationMarker),p(),r("ngIf",R(4,6,o.directionsResults$))}}function ce(i,e){if(i&1&&(u(0,"div",11)(1,"a",12)(2,"button",13)(3,"mat-icon"),_(4,"directions"),d(),_(5," Get Directions "),d()()()),i&2){let o=y();p(),r("href",o.directionsUrl,x)}}var oe=class i{constructor(e,o,t){this.locationService=e;this.mapDirectionsService=o;this.googleMapsLoader=t}origin;destination;showDirectionsLink=!0;apiLoaded=!1;directionsUrl="";center={lat:40.7128,lng:-74.006};zoom=12;options={mapTypeId:"roadmap",zoomControl:!0,scrollwheel:!0,disableDoubleClickZoom:!1,maxZoom:20,minZoom:4};originMarker;destinationMarker;markerAnimation=null;markerOptions={};directionsResults$;ngOnInit(){this.loadGoogleMapsApi(),this.updateDirectionsUrl()}ngOnChanges(e){(e.origin||e.destination)&&this.apiLoaded&&(this.updateMap(),this.updateDirectionsUrl())}ngAfterViewInit(){this.apiLoaded&&this.initMap()}loadGoogleMapsApi(){return g(this,null,function*(){try{yield this.googleMapsLoader.loadGoogleMapsApi(),this.apiLoaded=!0,typeof google<"u"&&google.maps&&(this.markerAnimation=google.maps.Animation,this.markerOptions={animation:google.maps.Animation.DROP}),this.initMap()}catch(e){console.error("Failed to load Google Maps API:",e),this.apiLoaded=!1}})}initMap(){this.origin&&this.destination&&this.updateMap()}updateMap(){return g(this,null,function*(){if(!(!this.origin||!this.destination))try{let e=typeof this.origin=="string"?yield this.locationService.geocodeAddress(this.origin):this.origin,o=typeof this.destination=="string"?yield this.locationService.geocodeAddress(this.destination):this.destination;this.originMarker={lat:e.latitude,lng:e.longitude},this.destinationMarker={lat:o.latitude,lng:o.longitude},this.center={lat:(e.latitude+o.latitude)/2,lng:(e.longitude+o.longitude)/2};let t=this.calculateDistance(e.latitude,e.longitude,o.latitude,o.longitude);this.zoom=this.calculateZoomLevel(t),this.getDirections(this.originMarker,this.destinationMarker)}catch(e){console.error("Error updating map:",e)}})}getDirections(e,o){let t={origin:e,destination:o,travelMode:google.maps.TravelMode.DRIVING};this.directionsResults$=this.mapDirectionsService.route(t).pipe(S(n=>n.result))}calculateDistance(e,o,t,n){let s=this.deg2rad(t-e),l=this.deg2rad(n-o),c=Math.sin(s/2)*Math.sin(s/2)+Math.cos(this.deg2rad(e))*Math.cos(this.deg2rad(t))*Math.sin(l/2)*Math.sin(l/2);return 6371*(2*Math.atan2(Math.sqrt(c),Math.sqrt(1-c)))}deg2rad(e){return e*(Math.PI/180)}calculateZoomLevel(e){return e>1e3?4:e>500?5:e>200?6:e>100?7:e>50?8:e>20?9:e>10?10:e>5?11:e>2?12:e>1?13:e>.5?14:15}updateDirectionsUrl(){if(!this.origin||!this.destination)return;let e=typeof this.origin=="string"?this.origin:`${this.origin.latitude},${this.origin.longitude}`,o=typeof this.destination=="string"?this.destination:`${this.destination.latitude},${this.destination.longitude}`;this.directionsUrl=this.locationService.getGoogleMapsDirectionsUrl(e,o)}static \u0275fac=function(o){return new(o||i)(b(D),b(Z),b(f))};static \u0275cmp=A({type:i,selectors:[["app-map-display"]],inputs:{origin:"origin",destination:"destination",showDirectionsLink:"showDirectionsLink"},features:[P],decls:6,vars:5,consts:[[1,"map-card"],["class","map-placeholder",4,"ngIf"],[1,"map-container"],["height","300px","width","100%",3,"center","zoom","options",4,"ngIf"],["class","map-actions",4,"ngIf"],[1,"map-placeholder"],["height","300px","width","100%",3,"center","zoom","options"],[3,"position","title","options",4,"ngIf"],[3,"directions",4,"ngIf"],[3,"position","title","options"],[3,"directions"],[1,"map-actions"],["target","_blank",3,"href"],["mat-raised-button","","color","primary"]],template:function(o,t){o&1&&(u(0,"mat-card",0)(1,"mat-card-content"),v(2,ae,3,0,"div",1),u(3,"div",2),v(4,de,5,8,"google-map",3),d(),v(5,ce,6,1,"div",4),d()()),o&2&&(p(2),r("ngIf",!t.apiLoaded),p(),O("display",t.apiLoaded?"block":"none"),p(),r("ngIf",t.apiLoaded),p(),r("ngIf",t.showDirectionsLink&&t.origin&&t.destination))},dependencies:[F,j,U,X,Q,W,q,E,H,K,V,z,N,B],styles:[".map-card[_ngcontent-%COMP%]{margin-bottom:16px}.map-container[_ngcontent-%COMP%]{height:300px;width:100%}.map-placeholder[_ngcontent-%COMP%]{height:300px;display:flex;justify-content:center;align-items:center;background-color:#f5f5f5;color:#666}.map-actions[_ngcontent-%COMP%]{margin-top:16px;display:flex;justify-content:center}"]})};export{D as a,oe as b};
