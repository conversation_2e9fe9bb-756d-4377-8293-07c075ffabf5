import { Component, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatListModule } from '@angular/material/list';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { DriverSelectionDialogComponent } from './driver-selection-dialog/driver-selection-dialog.component';
import { AdminReportsComponent } from './admin-reports/admin-reports.component';
import { UserDetailsDialogComponent } from './user-details-dialog/user-details-dialog.component';
import { RideDetailComponent } from '../../../shared/components/ride-detail/ride-detail.component';
import { SquareSandboxComponent } from './square-sandbox/square-sandbox.component';
import { StripePaymentComponent } from './stripe-payment/stripe-payment.component';
import { RidePricingComponent } from './ride-pricing/ride-pricing.component';
import { AdminRideCreateDialogComponent } from './admin-ride-create-dialog/admin-ride-create-dialog.component';

import { User, UserRole } from '../../../core/models/user.model';
import { Ride, RideStatus } from '../../../core/models/ride.model';
import { SystemStatistics } from '../../../core/models/statistics.model';
import { RideService } from '../../../core/services/ride.service';
import { StatisticsService } from '../../../core/services/statistics.service';
import { UserService } from '../../../core/services/user.service';
import { ThemePalette } from '@angular/material/core';
import { DriverPaymentComponent } from './driver-payment/driver-payment.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-admin',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatTabsModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatBadgeModule,
    MatListModule,
    MatGridListModule,
    MatTooltipModule,
    MatDialogModule,
    AdminReportsComponent,
    DriverSelectionDialogComponent,
    UserDetailsDialogComponent,
    RideDetailComponent,
    SquareSandboxComponent,
    DriverPaymentComponent,
    StripePaymentComponent,
    RidePricingComponent,
    AdminRideCreateDialogComponent
  ],
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss']
})
export class AdminComponent implements OnInit, AfterViewInit, OnDestroy {
  // User Management
  users: User[] = [];
  userDataSource = new MatTableDataSource<User>([]);
  userDisplayedColumns: string[] = ['email', 'full_name', 'role', 'created_at', 'status', 'actions'];
  userRoleFilter: UserRole | '' = '';
  userSearchTerm: string = '';
  loadingUsers: boolean = false;

  // Ride Management
  rides: Ride[] = [];
  rideDataSource = new MatTableDataSource<Ride>([]);
  rideDisplayedColumns: string[] = [
    'rider_id',
    'driver_id',
    'pickup_location',
    'dropoff_location',
    //'fare',
    'status',
    'created_at',
    'actions'
  ];
  rideStatusFilter: RideStatus | '' = '';
  rideSearchTerm: string = '';
  loadingRides: boolean = false;
  selectedRideId: string | null = null;
  lastRideRefreshTime: Date = new Date();

  // Statistics
  statistics: SystemStatistics | null = null;
  loadingStatistics: boolean = false;

  // Cache for user names to avoid repeated lookups
  private userNameCache: { [key: string]: string } = {};
  private userNameLoadingCache: { [key: string]: Promise<void> } = {};

  // Auto-refresh interval for rides
  private refreshRidesInterval: any;

  // Add this property to the class
  private ridesSubscription: Subscription | null = null;

  @ViewChild('userSort') userSort!: MatSort;
  @ViewChild('userPaginator') userPaginator!: MatPaginator;
  @ViewChild('rideSort') rideSort!: MatSort;
  @ViewChild('ridePaginator') ridePaginator!: MatPaginator;

  constructor(
    private userService: UserService,
    private rideService: RideService,
    private statisticsService: StatisticsService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  // Flag to track when data loading is complete
  private dataLoadingComplete = false;

  async ngOnInit() {
    console.log('Admin Component ngOnInit');
    try {
      await Promise.all([
        this.loadUsers(),
        this.initialLoadRides(), // Changed from loadRides()
        this.loadStatistics()
      ]);
      this.dataLoadingComplete = true;

      // If AfterViewInit has already run, set up the paginators now
      if (this.userPaginator && this.ridePaginator) {
        this.setupPaginators();
      }

      // Subscribe to ride updates instead of using interval
      this.setupRideSubscription();
    } catch (error) {
      console.error('Error loading data:', error);
      this.snackBar.open('Failed to load data', 'Close', { duration: 3000 });
    }
  }

  // New method for initial load
  private async initialLoadRides(): Promise<void> {
    this.loadingRides = true;
    try {
      this.rides = await this.rideService.getAllRides();
      this.rideDataSource.data = this.rides;
  this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
      // Apply any existing filters
      if (this.rideStatusFilter || this.rideSearchTerm) {
        this.applyRideFilters();
      }

      this.lastRideRefreshTime = new Date();
    } catch (error) {
      console.error('Error loading rides:', error);
      throw error;
    } finally {
      this.loadingRides = false;
    }

      this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
  }

  // Replace setupRidesAutoRefresh with this
  private setupRideSubscription(): void {
    // Clean up existing subscription if any
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }

    // Subscribe to the rides$ observable from the service
    this.ridesSubscription = this.rideService.rides$.subscribe(rides => {
      // Update only the changed rows to avoid blinking
      this.rideDataSource.data = rides;

      // Make sure sort and paginator are connected
      this.rideDataSource.sort = this.rideSort;
      this.rideDataSource.paginator = this.ridePaginator;

      // Maintain filters if needed
      if (this.rideStatusFilter || this.rideSearchTerm) {
        this.applyRideFilters();
      }

      // Update refresh time when changes are detected
      if (this.hasRidesChanged(this.rides, rides)) {
        this.lastRideRefreshTime = new Date();
      }
    });
  }

  // Helper method to identify changed rides and return them
  private getChangedRides(oldRides: Ride[], newRides: Ride[]): {
    hasChanges: boolean;
    changedRides: Ride[];
    addedRides: Ride[];
    removedRideIds: string[];
  } {
    const result = {
      hasChanges: false,
      changedRides: [] as Ride[],
      addedRides: [] as Ride[],
      removedRideIds: [] as string[]
    };

    // Create maps for quick lookups
    const oldRidesMap = new Map<string, Ride>();
    const newRidesMap = new Map<string, Ride>();

    oldRides.forEach(ride => oldRidesMap.set(ride.id, ride));
    newRides.forEach(ride => newRidesMap.set(ride.id, ride));

    // Find added rides (in new but not in old)
    for (const newRide of newRides) {
      if (!oldRidesMap.has(newRide.id)) {
        result.addedRides.push(newRide);
        result.hasChanges = true;
      } else {
        // Check if existing ride was updated
        const oldRide = oldRidesMap.get(newRide.id)!;
        if (oldRide.updated_at !== newRide.updated_at) {
          result.changedRides.push(newRide);
          result.hasChanges = true;
        }
      }
    }

    // Find removed rides (in old but not in new)
    for (const oldRide of oldRides) {
      if (!newRidesMap.has(oldRide.id)) {
        result.removedRideIds.push(oldRide.id);
        result.hasChanges = true;
      }
    }

    return result;
  }

  // Helper method to check if rides data has actually changed (simplified version)
  private hasRidesChanged(oldRides: Ride[], newRides: Ride[]): boolean {
    return this.getChangedRides(oldRides, newRides).hasChanges;
  }

  // Method to update only the changed rows in the data source
  private updateChangedRidesOnly(newRides: Ride[]): void {
    const changes = this.getChangedRides(this.rides, newRides);

    if (!changes.hasChanges) {
      return; // No changes, nothing to update
    }

    // Create a copy of the current data
    const currentData = [...this.rideDataSource.data];

    // Handle removed rides
    if (changes.removedRideIds.length > 0) {
      // Filter out removed rides
      const filteredData = currentData.filter(ride =>
        !changes.removedRideIds.includes(ride.id)
      );

      // Update the entire data source if rides were removed
      this.rides = filteredData;
      this.rideDataSource.data = filteredData;
        this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
      return;
    }

    // Handle added rides
    if (changes.addedRides.length > 0) {
      // Add new rides to the beginning (assuming newest first)
      const updatedData = [...changes.addedRides, ...currentData];
      this.rides = updatedData;
      this.rideDataSource.data = updatedData;
        this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
      return;
    }

    // Handle updated rides
    if (changes.changedRides.length > 0) {
      // Create a map for quick lookup
      const dataMap = new Map<string, number>();
      currentData.forEach((ride, index) => {
        dataMap.set(ride.id, index);
      });

      // Update only the changed rides
      changes.changedRides.forEach(changedRide => {
        const index = dataMap.get(changedRide.id);
        if (index !== undefined) {
          // Update the ride in both arrays
          currentData[index] = changedRide;

          // Find and update in the original rides array too
          const ridesIndex = this.rides.findIndex(r => r.id === changedRide.id);
          if (ridesIndex !== -1) {
            this.rides[ridesIndex] = changedRide;
          }
        }
      });

      // Update the data source with the modified data
      this.rideDataSource.data = currentData;
        this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
    }
  }

  ngAfterViewInit() {
    // If data is already loaded, set up paginators immediately
    if (this.dataLoadingComplete) {
      this.setupPaginators();
    }
    // Otherwise, the setupPaginators will be called when data loading completes in ngOnInit
  }

  // Separate method to set up paginators to avoid code duplication
  private setupPaginators() {
    // Connect user data source to sort and paginator
    this.userDataSource.sort = this.userSort;
    this.userDataSource.paginator = this.userPaginator;

    // Connect ride data source to sort and paginator
    this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;

    // Custom filter predicate for user filtering
    this.userDataSource.filterPredicate = (data: User, filter: string) => {
      const searchTerms = JSON.parse(filter);
      let roleMatch = true;
      let searchMatch = true;

      if (searchTerms.role) {
        roleMatch = data.role === searchTerms.role;
      }

      if (searchTerms.searchTerm) {
        const term = searchTerms.searchTerm.toLowerCase();
        const emailMatch = data.email.toLowerCase().includes(term);
        const nameMatch = data.full_name ? data.full_name.toLowerCase().includes(term) : false;
        searchMatch = emailMatch || nameMatch;
      }

      return roleMatch && searchMatch;
    };


    // Custom sort function for users
    this.userDataSource.sortingDataAccessor = (item: User, property: string) => {
      switch (property) {
        case 'created_at':
          return new Date(item.created_at).getTime();
        case 'full_name':
          return item.full_name?.toLowerCase() || '';
        default:
          return (item as any)[property] || '';
      }
    };

    // Custom filter predicate for ride filtering
    this.rideDataSource.filterPredicate = (data: Ride, filter: string) => {
      const searchTerms = JSON.parse(filter);
      let statusMatch = true;
      let searchMatch = true;

      if (searchTerms.status) {
        statusMatch = data.status === searchTerms.status;
      }

      if (searchTerms.searchTerm) {
        const term = searchTerms.searchTerm.toLowerCase();
        const pickupMatch = data.pickup_location.toLowerCase().includes(term);
        const dropoffMatch = data.dropoff_location.toLowerCase().includes(term);
        const riderName = (this.getUserName(data.rider_id) || '').toLowerCase().includes(term);
        //const driverName = data.driver_id ? (this.getUserName(data.driver_id) || '').toLowerCase() : '';
     
        searchMatch = pickupMatch || dropoffMatch || riderName;
      }

      return statusMatch && searchMatch;
    };

    // Custom sort function for rides
    this.rideDataSource.sortingDataAccessor = (item: Ride, property: string) => {
      
      switch (property) {
        case 'rider_id':
          return this.getUserName(item.rider_id).toLowerCase();
        case 'driver_id':
          return item.driver_id ? this.getUserName(item.driver_id).toLowerCase() : 'zzz'; // Sort empty values last
        case 'created_at':
        case 'pickup_time':
          return item[property] ? new Date(item[property]).getTime() : 0;
        case 'fare':
          return item.fare || 0;
        default:
          return (item as any)[property] || '';
      }
    };

  }

  // User Management Methods
  async loadUsers() {
    this.loadingUsers = true;
    try {
      this.users = await this.userService.getAllUsers();
      this.userDataSource.data = this.users;
          this.userDataSource.sort = this.userSort;
    this.userDataSource.paginator = this.userPaginator;

      // Apply any existing filters
      if (this.userRoleFilter || this.userSearchTerm) {
        this.applyUserFilters();
      }
    } catch (error) {
      console.error('Error loading users:', error);
      this.snackBar.open('Failed to load users', 'Close', { duration: 3000 });
    } finally {
      this.loadingUsers = false;
    }
  }

  applyUserFilters() {
    const filterValue = JSON.stringify({
      role: this.userRoleFilter,
      searchTerm: this.userSearchTerm
    });

    this.userDataSource.filter = filterValue;

    // Make sure paginator exists and is properly connected before using it
    if (this.userDataSource.paginator && this.dataLoadingComplete) {
      this.userDataSource.paginator.firstPage();
    }
  }

  getRoleDisplayName(role: UserRole): string {
    return role.charAt(0).toUpperCase() + role.slice(1);
  }

  async approveDriver(userId: string) {
    try {
      const success = await this.userService.approveDriver(userId);
      if (success) {
        this.snackBar.open('Driver approved successfully', 'Close', { duration: 3000 });
      } else {
        throw new Error('Failed to approve driver');
      }
    } catch (error) {
      console.error('Error approving driver:', error);
      this.snackBar.open('Failed to approve driver', 'Close', { duration: 3000 });
    }
  }

  openUserDetails(user: User): void {
    this.dialog.open(UserDetailsDialogComponent, {
      width: '500px',
      data: user
    });
  }

  getStatusDisplay(user: any): string {

    if (user.role === 'driver') {
      return user.is_approved ? 'Approved' : 'Pending Approval';
    }
    if( user.role=== 'admin' ){
      return user.is_approved ? 'Active' : 'Inactive';
    }
    return 'Active'; // For riders and admins
  }

  // Ride Management Methods
  async loadRides() {
    this.loadingRides = true;
    try {
      // Force a refresh from the service
      const newRides = await this.rideService.getAllRides();

      // Update only the changed rows to avoid blinking
      this.updateChangedRidesOnly(newRides);

      // Apply any existing filters
      if (this.rideStatusFilter || this.rideSearchTerm) {
        this.applyRideFilters();
      }

      // If paginator is already set up, update it with the new data length
      if (this.ridePaginator && this.dataLoadingComplete) {
        this.ridePaginator.length = this.rides.length;
      }

      // Always update the last refresh time to show the user something happened
      this.lastRideRefreshTime = new Date();
    this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
    } catch (error) {
      console.error('Error loading rides:', error);
      this.snackBar.open('Failed to load rides', 'Close', { duration: 3000 });
    } finally {
      this.loadingRides = false;
    }
  }

  applyRideFilters() {
    const filterValue = JSON.stringify({
      status: this.rideStatusFilter,
      searchTerm: this.rideSearchTerm
    });

    this.rideDataSource.filter = filterValue;

    // Make sure paginator exists and is properly connected before using it
    if (this.rideDataSource.paginator && this.dataLoadingComplete) {
      this.rideDataSource.paginator.firstPage();
    }
  }

  async updateRideStatus(rideId: string, status: RideStatus) {
    try {
      const success = await this.rideService.updateRideStatus(rideId, status);
      if (success) {
        this.snackBar.open(`Ride status updated to ${this.getStatusDisplayName(status)}`, 'Close', { duration: 3000 });
     //   await this.loadRides();
      } else {
        throw new Error('Failed to update ride status');
      }
    } catch (error) {
      console.error('Error updating ride status:', error);
      this.snackBar.open('Failed to update ride status', 'Close', { duration: 3000 });
    }
  }

  openDriverSelectionDialog(rideId: string): void {
    const dialogRef = this.dialog.open(DriverSelectionDialogComponent, {
      width: '500px',
      data: { drivers: this.users.filter(user => user.role === 'driver' && user.is_approved) }
    });

    dialogRef.afterClosed().subscribe(async (driverId: string) => {
      if (driverId) {
        try {
          const success = await this.rideService.assignRideToDriver(rideId, driverId);
          if (success) {
            this.snackBar.open('Driver assigned successfully', 'Close', { duration: 3000 });
          //  await this.loadRides();
          } else {
            throw new Error('Failed to assign driver');
          }
        } catch (error) {
          console.error('Error assigning driver:', error);
          this.snackBar.open('Failed to assign driver', 'Close', { duration: 3000 });
        }
      }
    });
  }

  viewRideDetails(rideId: string): void {
    this.selectedRideId = rideId;
  }

  closeRideDetails(): void {
    this.selectedRideId = null;
  }

  onRideUpdated(updatedRide: Ride): void {
    // Update just this specific ride in the data source
    const currentData = [...this.rideDataSource.data];
    const index = currentData.findIndex(ride => ride.id === updatedRide.id);

    if (index !== -1) {
      // Update the ride in the data source
      currentData[index] = updatedRide;
      this.rideDataSource.data = currentData;
      this.rideDataSource.sort = this.rideSort;
      this.rideDataSource.paginator = this.ridePaginator;
      // Also update in the rides array
      const ridesIndex = this.rides.findIndex(r => r.id === updatedRide.id);
      if (ridesIndex !== -1) {
        this.rides[ridesIndex] = updatedRide;
      }

      // Update refresh time
      this.lastRideRefreshTime = new Date();
    } else {
      // If ride not found in current data, do a full refresh
     // this.loadRides();
    }
  }

  openCreateRideDialog(): void {
    const dialogRef = this.dialog.open(AdminRideCreateDialogComponent, {
      width: '600px'
    });

    dialogRef.afterClosed().subscribe((createdRide: Ride) => {
      if (createdRide) {
        this.snackBar.open('Ride created successfully', 'Close', { duration: 3000 });
        // The ride will be added automatically through the realtime subscription
      }
    });
  }

  // Statistics Methods
  async loadStatistics() {
    this.loadingStatistics = true;
    try {
      this.statistics = await this.statisticsService.generateSystemStatistics();
    } catch (error) {
      console.error('Error loading statistics:', error);
      this.snackBar.open('Failed to load statistics', 'Close', { duration: 3000 });
    } finally {
      this.loadingStatistics = false;
    }
  }

  // Helper Methods
  getStatusDisplayName(status: RideStatus): string {
    return status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }

  getStatusColor(status: RideStatus): ThemePalette {
    const statusColors: { [key in RideStatus]: ThemePalette } = {
      'requested': 'warn',
      'assigned': 'primary',
      'in-progress': 'accent',
      'completed': 'primary',
      'canceled': 'warn'
    };
    return statusColors[status] || 'primary';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getUserName(userId: string | undefined): string {
    if (!userId) return 'N/A';

    const user = this.users.find(x => x.id === userId);
    return user?.full_name || userId || 'N/A';
  }

  /**
   * Clean up resources when component is destroyed
   */
  ngOnDestroy(): void {
    // Clear the auto-refresh interval to prevent memory leaks
    if (this.refreshRidesInterval) {
      clearInterval(this.refreshRidesInterval);
    }

    // Clean up subscription
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
  }
}


// rider=51873bf3-4188-4d74-a54e-5d2b7ccf2e5c

// driver=b5efc0aa-e6f5-4301-9f03-b3092e0dc973


