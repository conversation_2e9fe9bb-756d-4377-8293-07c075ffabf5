import{c as pr}from"./chunk-4PASSMJB.js";import{a as Zi,b as <PERSON>,c as Ji,d as er,e as nr}from"./chunk-6ZEJENDX.js";import{A as Wi,h as Le,i as kt,j as zi,k as Bi,l as Ne,m as Ue,n as Ve,o as ue,r as $i,u as oi,v as ji,w as Hi,x as qi,y as Gi,z as Qi}from"./chunk-RV7CR6VO.js";import{a as fe,c as _e,d as ee}from"./chunk-XN7QUBD6.js";import"./chunk-TVEQ2QHA.js";import{b as mr}from"./chunk-Q62AX3L3.js";import{a as rr,b as Ke,c as or,d as sr,e as lr,f as dr,g as cr,h as Et,j as At,k as Ft,l as Ot,m as zt,n as Bt,o as Lt,p as Nt,q as Ut,r as Vt,s as $t,t as jt,u as et,v as li,w as tt,x as it}from"./chunk-WJOJBTBX.js";import{a as re,b as Se,c as we,d as Ce,e as ar,f as Je}from"./chunk-O2B7MD3G.js";import{a as tr,b as ir}from"./chunk-Q5CX2EZL.js";import"./chunk-CTHHBRRU.js";import"./chunk-JFL7URHX.js";import{a as ve,b as te}from"./chunk-NXDGXY44.js";import"./chunk-RAIBPYI3.js";import{a as j,b as pe}from"./chunk-6WJZ7JAD.js";import"./chunk-FMN32A34.js";import"./chunk-H3RPDCYZ.js";import{C as H,F as J,H as ye,I as be,J as Yi,a as Li,b as ge,c as Ni,d as z,f as X,g as $e,j as It,k as je,l as Pt,n as He,o as qe,p as Ui,q as Vi,s as Ge,t as Z,u as he,w as K,x as Qe,z as Tt}from"./chunk-UDNA73AU.js";import{$b as St,Ab as De,Ad as Be,Ba as Di,Bb as Re,Bd as Oi,Db as Ei,Dd as me,Ea as vt,Eb as oe,Ec as V,Fb as q,Fd as $,Gb as G,Id as U,Jb as Q,Jd as si,Kb as s,Kd as We,La as d,Lb as M,Ld as Ye,Mb as D,Md as Xe,Nd as Xi,Ob as ke,Pa as C,Pb as Ie,Pd as Ze,Qb as Pe,Qd as ie,Sb as bt,Sc as Ct,Tb as ni,Vc as xt,Wa as F,Xa as de,Xc as Mt,Y as Ci,Ya as yt,Z as Oe,_ as se,aa as le,ab as p,ca as gt,da as R,dc as ze,ea as ht,f as ot,fa as ft,fc as N,g as Si,gb as L,gc as Te,gd as Dt,h as ut,hb as m,jb as ce,ka as _t,kb as Ri,la as y,ma as b,mb as W,md as Ai,na as ne,nb as ki,oa as xi,ob as Ii,pb as Pi,qb as n,rb as o,sa as Mi,sb as h,tb as E,ub as A,uc as wt,va as xe,vc as Y,wb as I,xb as Ti,xd as Fi,yb as _,z as wi,za as Me,zb as g,zd as Rt}from"./chunk-L5P5FOLI.js";import"./chunk-X5YLR3NI.js";import{a as at,b as nt,i as x}from"./chunk-ODN5LVDJ.js";var Ir=["mat-sort-header",""],Pr=["*"];function Tr(r,t){r&1&&(n(0,"div",2),ne(),n(1,"svg",3),h(2,"path",4),o()())}var ur=new le("MAT_SORT_DEFAULT_OPTIONS"),di=(()=>{class r{_defaultOptions;_initializedStream=new ut(1);sortables=new Map;_stateChanges=new ot;active;start="asc";get direction(){return this._direction}set direction(e){this._direction=e}_direction="";disableClear;disabled=!1;sortChange=new xe;initialized=this._initializedStream;constructor(e){this._defaultOptions=e}register(e){this.sortables.set(e.id,e)}deregister(e){this.sortables.delete(e.id)}sort(e){this.active!=e.id?(this.active=e.id,this.direction=e.start?e.start:this.start):this.direction=this.getNextSortDirection(e),this.sortChange.emit({active:this.active,direction:this.direction})}getNextSortDirection(e){if(!e)return"";let i=e?.disableClear??this.disableClear??!!this._defaultOptions?.disableClear,a=Er(e.start||this.start,i),l=a.indexOf(this.direction)+1;return l>=a.length&&(l=0),a[l]}ngOnInit(){this._initializedStream.next()}ngOnChanges(){this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete(),this._initializedStream.complete()}static \u0275fac=function(i){return new(i||r)(C(ur,8))};static \u0275dir=yt({type:r,selectors:[["","matSort",""]],hostAttrs:[1,"mat-sort"],inputs:{active:[0,"matSortActive","active"],start:[0,"matSortStart","start"],direction:[0,"matSortDirection","direction"],disableClear:[2,"matSortDisableClear","disableClear",N],disabled:[2,"matSortDisabled","disabled",N]},outputs:{sortChange:"matSortChange"},exportAs:["matSort"],features:[_t]})}return r})();function Er(r,t){let e=["asc","desc"];return r=="desc"&&e.reverse(),t||e.push(""),e}var Ht=(()=>{class r{changes=new ot;static \u0275fac=function(i){return new(i||r)};static \u0275prov=Oe({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();function Ar(r){return r||new Ht}var Fr={provide:Ht,deps:[[new ht,new ft,Ht]],useFactory:Ar},gr=(()=>{class r{_intl=R(Ht);_sort=R(di,{optional:!0});_columnDef=R("MAT_SORT_HEADER_COLUMN_DEF",{optional:!0});_changeDetectorRef=R(ze);_focusMonitor=R(xt);_elementRef=R(Me);_ariaDescriber=R(Ai,{optional:!0});_renderChanges;_animationModule=R(vt,{optional:!0});_recentlyCleared=Di(null);_sortButton;id;arrowPosition="after";start;disabled=!1;get sortActionDescription(){return this._sortActionDescription}set sortActionDescription(e){this._updateSortActionDescription(e)}_sortActionDescription="Sort";disableClear;constructor(){R(Mt).load(Rt);let e=R(ur,{optional:!0});this._sort,e?.arrowPosition&&(this.arrowPosition=e?.arrowPosition)}ngOnInit(){!this.id&&this._columnDef&&(this.id=this._columnDef.name),this._sort.register(this),this._renderChanges=wi(this._sort._stateChanges,this._sort.sortChange).subscribe(()=>this._changeDetectorRef.markForCheck()),this._sortButton=this._elementRef.nativeElement.querySelector(".mat-sort-header-container"),this._updateSortActionDescription(this._sortActionDescription)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(()=>this._recentlyCleared.set(null))}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._sort.deregister(this),this._renderChanges?.unsubscribe(),this._sortButton&&this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription)}_toggleOnInteraction(){if(!this._isDisabled()){let e=this._isSorted(),i=this._sort.direction;this._sort.sort(this),this._recentlyCleared.set(e&&!this._isSorted()?i:null)}}_handleKeydown(e){(e.keyCode===32||e.keyCode===13)&&(e.preventDefault(),this._toggleOnInteraction())}_isSorted(){return this._sort.active==this.id&&(this._sort.direction==="asc"||this._sort.direction==="desc")}_isDisabled(){return this._sort.disabled||this.disabled}_getAriaSortAttribute(){return this._isSorted()?this._sort.direction=="asc"?"ascending":"descending":"none"}_renderArrow(){return!this._isDisabled()||this._isSorted()}_updateSortActionDescription(e){this._sortButton&&(this._ariaDescriber?.removeDescription(this._sortButton,this._sortActionDescription),this._ariaDescriber?.describe(this._sortButton,e)),this._sortActionDescription=e}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=F({type:r,selectors:[["","mat-sort-header",""]],hostAttrs:[1,"mat-sort-header"],hostVars:3,hostBindings:function(i,a){i&1&&_("click",function(){return a._toggleOnInteraction()})("keydown",function(c){return a._handleKeydown(c)})("mouseleave",function(){return a._recentlyCleared.set(null)}),i&2&&(L("aria-sort",a._getAriaSortAttribute()),ce("mat-sort-header-disabled",a._isDisabled()))},inputs:{id:[0,"mat-sort-header","id"],arrowPosition:"arrowPosition",start:"start",disabled:[2,"disabled","disabled",N],sortActionDescription:"sortActionDescription",disableClear:[2,"disableClear","disableClear",N]},exportAs:["matSortHeader"],attrs:Ir,ngContentSelectors:Pr,decls:4,vars:17,consts:[[1,"mat-sort-header-container","mat-focus-indicator"],[1,"mat-sort-header-content"],[1,"mat-sort-header-arrow"],["viewBox","0 -960 960 960","focusable","false","aria-hidden","true"],["d","M440-240v-368L296-464l-56-56 240-240 240 240-56 56-144-144v368h-80Z"]],template:function(i,a){i&1&&(De(),n(0,"div",0)(1,"div",1),Re(2),o(),p(3,Tr,3,0,"div",2),o()),i&2&&(ce("mat-sort-header-sorted",a._isSorted())("mat-sort-header-position-before",a.arrowPosition==="before")("mat-sort-header-descending",a._sort.direction==="desc")("mat-sort-header-ascending",a._sort.direction==="asc")("mat-sort-header-recently-cleared-ascending",a._recentlyCleared()==="asc")("mat-sort-header-recently-cleared-descending",a._recentlyCleared()==="desc")("mat-sort-header-animations-disabled",a._animationModule==="NoopAnimations"),L("tabindex",a._isDisabled()?null:0)("role",a._isDisabled()?null:"button"),d(3),W(a._renderArrow()?3:-1))},styles:[`.mat-sort-header{cursor:pointer}.mat-sort-header-disabled{cursor:default}.mat-sort-header-container{display:flex;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}@keyframes _mat-sort-header-recently-cleared-ascending{from{transform:translateY(0);opacity:1}to{transform:translateY(-25%);opacity:0}}@keyframes _mat-sort-header-recently-cleared-descending{from{transform:translateY(0) rotate(180deg);opacity:1}to{transform:translateY(25%) rotate(180deg);opacity:0}}.mat-sort-header-arrow{height:12px;width:12px;position:relative;transition:transform 225ms cubic-bezier(0.4, 0, 0.2, 1),opacity 225ms cubic-bezier(0.4, 0, 0.2, 1);opacity:0;overflow:visible;color:var(--mat-sort-arrow-color, var(--mat-sys-on-surface))}.mat-sort-header.cdk-keyboard-focused .mat-sort-header-arrow,.mat-sort-header.cdk-program-focused .mat-sort-header-arrow,.mat-sort-header:hover .mat-sort-header-arrow{opacity:.54}.mat-sort-header .mat-sort-header-sorted .mat-sort-header-arrow{opacity:1}.mat-sort-header-descending .mat-sort-header-arrow{transform:rotate(180deg)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transform:translateY(-25%)}.mat-sort-header-recently-cleared-ascending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-ascending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-recently-cleared-descending .mat-sort-header-arrow{transition:none;animation:_mat-sort-header-recently-cleared-descending 225ms cubic-bezier(0.4, 0, 0.2, 1) forwards}.mat-sort-header-animations-disabled .mat-sort-header-arrow{transition-duration:0ms;animation-duration:0ms}.mat-sort-header-arrow svg{width:24px;height:24px;fill:currentColor;position:absolute;top:50%;left:50%;margin:-12px 0 0 -12px;transform:translateZ(0)}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}
`],encapsulation:2,changeDetection:0})}return r})(),hr=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=de({type:r});static \u0275inj=se({providers:[Fr],imports:[me]})}return r})();function zr(r,t){if(r&1&&(n(0,"mat-option",17),s(1),o()),r&2){let e=t.$implicit;m("value",e),d(),D(" ",e," ")}}function Br(r,t){if(r&1){let e=I();n(0,"mat-form-field",14)(1,"mat-select",16,0),_("selectionChange",function(a){y(e);let l=g(2);return b(l._changePageSize(a.value))}),Ii(3,zr,2,2,"mat-option",17,ki),o(),n(5,"div",18),_("click",function(){y(e);let a=Q(2);return b(a.open())}),o()()}if(r&2){let e=g(2);m("appearance",e._formFieldAppearance)("color",e.color),d(),m("value",e.pageSize)("disabled",e.disabled)("aria-labelledby",e._pageSizeLabelId)("panelClass",e.selectConfig.panelClass||"")("disableOptionCentering",e.selectConfig.disableOptionCentering),d(2),Pi(e._displayedPageSizeOptions)}}function Lr(r,t){if(r&1&&(n(0,"div",15),s(1),o()),r&2){let e=g(2);d(),M(e.pageSize)}}function Nr(r,t){if(r&1&&(n(0,"div",3)(1,"div",13),s(2),o(),p(3,Br,6,7,"mat-form-field",14)(4,Lr,2,1,"div",15),o()),r&2){let e=g();d(),L("id",e._pageSizeLabelId),d(),D(" ",e._intl.itemsPerPageLabel," "),d(),W(e._displayedPageSizeOptions.length>1?3:-1),d(),W(e._displayedPageSizeOptions.length<=1?4:-1)}}function Ur(r,t){if(r&1){let e=I();n(0,"button",19),_("click",function(){y(e);let a=g();return b(a._buttonClicked(0,a._previousButtonsDisabled()))}),ne(),n(1,"svg",8),h(2,"path",20),o()()}if(r&2){let e=g();m("matTooltip",e._intl.firstPageLabel)("matTooltipDisabled",e._previousButtonsDisabled())("disabled",e._previousButtonsDisabled())("tabindex",e._previousButtonsDisabled()?-1:null),L("aria-label",e._intl.firstPageLabel)}}function Vr(r,t){if(r&1){let e=I();n(0,"button",21),_("click",function(){y(e);let a=g();return b(a._buttonClicked(a.getNumberOfPages()-1,a._nextButtonsDisabled()))}),ne(),n(1,"svg",8),h(2,"path",22),o()()}if(r&2){let e=g();m("matTooltip",e._intl.lastPageLabel)("matTooltipDisabled",e._nextButtonsDisabled())("disabled",e._nextButtonsDisabled())("tabindex",e._nextButtonsDisabled()?-1:null),L("aria-label",e._intl.lastPageLabel)}}var qt=(()=>{class r{changes=new ot;itemsPerPageLabel="Items per page:";nextPageLabel="Next page";previousPageLabel="Previous page";firstPageLabel="First page";lastPageLabel="Last page";getRangeLabel=(e,i,a)=>{if(a==0||i==0)return`0 of ${a}`;a=Math.max(a,0);let l=e*i,c=l<a?Math.min(l+i,a):l+i;return`${l+1} \u2013 ${c} of ${a}`};static \u0275fac=function(i){return new(i||r)};static \u0275prov=Oe({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();function $r(r){return r||new qt}var jr={provide:qt,deps:[[new ht,new ft,qt]],useFactory:$r},Hr=50;var qr=new le("MAT_PAGINATOR_DEFAULT_OPTIONS"),ci=(()=>{class r{_intl=R(qt);_changeDetectorRef=R(ze);_formFieldAppearance;_pageSizeLabelId=R(Dt).getId("mat-paginator-page-size-label-");_intlChanges;_isInitialized=!1;_initializedStream=new ut(1);color;get pageIndex(){return this._pageIndex}set pageIndex(e){this._pageIndex=Math.max(e||0,0),this._changeDetectorRef.markForCheck()}_pageIndex=0;get length(){return this._length}set length(e){this._length=e||0,this._changeDetectorRef.markForCheck()}_length=0;get pageSize(){return this._pageSize}set pageSize(e){this._pageSize=Math.max(e||0,0),this._updateDisplayedPageSizeOptions()}_pageSize;get pageSizeOptions(){return this._pageSizeOptions}set pageSizeOptions(e){this._pageSizeOptions=(e||[]).map(i=>Te(i,0)),this._updateDisplayedPageSizeOptions()}_pageSizeOptions=[];hidePageSize=!1;showFirstLastButtons=!1;selectConfig={};disabled=!1;page=new xe;_displayedPageSizeOptions;initialized=this._initializedStream;constructor(){let e=this._intl,i=R(qr,{optional:!0});if(this._intlChanges=e.changes.subscribe(()=>this._changeDetectorRef.markForCheck()),i){let{pageSize:a,pageSizeOptions:l,hidePageSize:c,showFirstLastButtons:f}=i;a!=null&&(this._pageSize=a),l!=null&&(this._pageSizeOptions=l),c!=null&&(this.hidePageSize=c),f!=null&&(this.showFirstLastButtons=f)}this._formFieldAppearance=i?.formFieldAppearance||"outline"}ngOnInit(){this._isInitialized=!0,this._updateDisplayedPageSizeOptions(),this._initializedStream.next()}ngOnDestroy(){this._initializedStream.complete(),this._intlChanges.unsubscribe()}nextPage(){this.hasNextPage()&&this._navigate(this.pageIndex+1)}previousPage(){this.hasPreviousPage()&&this._navigate(this.pageIndex-1)}firstPage(){this.hasPreviousPage()&&this._navigate(0)}lastPage(){this.hasNextPage()&&this._navigate(this.getNumberOfPages()-1)}hasPreviousPage(){return this.pageIndex>=1&&this.pageSize!=0}hasNextPage(){let e=this.getNumberOfPages()-1;return this.pageIndex<e&&this.pageSize!=0}getNumberOfPages(){return this.pageSize?Math.ceil(this.length/this.pageSize):0}_changePageSize(e){let i=this.pageIndex*this.pageSize,a=this.pageIndex;this.pageIndex=Math.floor(i/e)||0,this.pageSize=e,this._emitPageEvent(a)}_nextButtonsDisabled(){return this.disabled||!this.hasNextPage()}_previousButtonsDisabled(){return this.disabled||!this.hasPreviousPage()}_updateDisplayedPageSizeOptions(){this._isInitialized&&(this.pageSize||(this._pageSize=this.pageSizeOptions.length!=0?this.pageSizeOptions[0]:Hr),this._displayedPageSizeOptions=this.pageSizeOptions.slice(),this._displayedPageSizeOptions.indexOf(this.pageSize)===-1&&this._displayedPageSizeOptions.push(this.pageSize),this._displayedPageSizeOptions.sort((e,i)=>e-i),this._changeDetectorRef.markForCheck())}_emitPageEvent(e){this.page.emit({previousPageIndex:e,pageIndex:this.pageIndex,pageSize:this.pageSize,length:this.length})}_navigate(e){let i=this.pageIndex;e!==i&&(this.pageIndex=e,this._emitPageEvent(i))}_buttonClicked(e,i){i||this._navigate(e)}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=F({type:r,selectors:[["mat-paginator"]],hostAttrs:["role","group",1,"mat-mdc-paginator"],inputs:{color:"color",pageIndex:[2,"pageIndex","pageIndex",Te],length:[2,"length","length",Te],pageSize:[2,"pageSize","pageSize",Te],pageSizeOptions:"pageSizeOptions",hidePageSize:[2,"hidePageSize","hidePageSize",N],showFirstLastButtons:[2,"showFirstLastButtons","showFirstLastButtons",N],selectConfig:"selectConfig",disabled:[2,"disabled","disabled",N]},outputs:{page:"page"},exportAs:["matPaginator"],decls:14,vars:14,consts:[["selectRef",""],[1,"mat-mdc-paginator-outer-container"],[1,"mat-mdc-paginator-container"],[1,"mat-mdc-paginator-page-size"],[1,"mat-mdc-paginator-range-actions"],["aria-live","polite",1,"mat-mdc-paginator-range-label"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"matTooltip","matTooltipDisabled","disabled","tabindex"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-previous",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["viewBox","0 0 24 24","focusable","false","aria-hidden","true",1,"mat-mdc-paginator-icon"],["d","M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-next",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"matTooltip","matTooltipDisabled","disabled","tabindex"],[1,"mat-mdc-paginator-page-size-label"],[1,"mat-mdc-paginator-page-size-select",3,"appearance","color"],[1,"mat-mdc-paginator-page-size-value"],["hideSingleSelectionIndicator","",3,"selectionChange","value","disabled","aria-labelledby","panelClass","disableOptionCentering"],[3,"value"],[1,"mat-mdc-paginator-touch-target",3,"click"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"],["mat-icon-button","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"]],template:function(i,a){i&1&&(n(0,"div",1)(1,"div",2),p(2,Nr,5,4,"div",3),n(3,"div",4)(4,"div",5),s(5),o(),p(6,Ur,3,5,"button",6),n(7,"button",7),_("click",function(){return a._buttonClicked(a.pageIndex-1,a._previousButtonsDisabled())}),ne(),n(8,"svg",8),h(9,"path",9),o()(),xi(),n(10,"button",10),_("click",function(){return a._buttonClicked(a.pageIndex+1,a._nextButtonsDisabled())}),ne(),n(11,"svg",8),h(12,"path",11),o()(),p(13,Vr,3,5,"button",12),o()()()),i&2&&(d(2),W(a.hidePageSize?-1:2),d(3),D(" ",a._intl.getRangeLabel(a.pageIndex,a.pageSize,a.length)," "),d(),W(a.showFirstLastButtons?6:-1),d(),m("matTooltip",a._intl.previousPageLabel)("matTooltipDisabled",a._previousButtonsDisabled())("disabled",a._previousButtonsDisabled())("tabindex",a._previousButtonsDisabled()?-1:null),L("aria-label",a._intl.previousPageLabel),d(3),m("matTooltip",a._intl.nextPageLabel)("matTooltipDisabled",a._nextButtonsDisabled())("disabled",a._nextButtonsDisabled())("tabindex",a._nextButtonsDisabled()?-1:null),L("aria-label",a._intl.nextPageLabel),d(3),W(a.showFirstLastButtons?13:-1))},dependencies:[H,_e,fe,Be,tt],styles:[`.mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height:var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}
`],encapsulation:2,changeDetection:0})}return r})(),fr=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=de({type:r});static \u0275inj=se({providers:[jr],imports:[U,ee,it,ci]})}return r})();var Gt=class{tracker;columnIndex=0;rowIndex=0;get rowCount(){return this.rowIndex+1}get rowspan(){let t=Math.max(...this.tracker);return t>1?this.rowCount+t-1:this.rowCount}positions;update(t,e){this.columnIndex=0,this.rowIndex=0,this.tracker=new Array(t),this.tracker.fill(0,0,this.tracker.length),this.positions=e.map(i=>this._trackTile(i))}_trackTile(t){let e=this._findMatchingGap(t.colspan);return this._markTilePosition(e,t),this.columnIndex=e+t.colspan,new mi(this.rowIndex,e)}_findMatchingGap(t){t>this.tracker.length;let e=-1,i=-1;do{if(this.columnIndex+t>this.tracker.length){this._nextRow(),e=this.tracker.indexOf(0,this.columnIndex),i=this._findGapEndIndex(e);continue}if(e=this.tracker.indexOf(0,this.columnIndex),e==-1){this._nextRow(),e=this.tracker.indexOf(0,this.columnIndex),i=this._findGapEndIndex(e);continue}i=this._findGapEndIndex(e),this.columnIndex=e+1}while(i-e<t||i==0);return Math.max(e,0)}_nextRow(){this.columnIndex=0,this.rowIndex++;for(let t=0;t<this.tracker.length;t++)this.tracker[t]=Math.max(0,this.tracker[t]-1)}_findGapEndIndex(t){for(let e=t+1;e<this.tracker.length;e++)if(this.tracker[e]!=0)return e;return this.tracker.length}_markTilePosition(t,e){for(let i=0;i<e.colspan;i++)this.tracker[t+i]=e.rowspan}},mi=class{row;col;constructor(t,e){this.row=t,this.col=e}};var _r=["*"];var Qr=`.mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}
`,vr=new le("MAT_GRID_LIST"),hi=(()=>{class r{_element=R(Me);_gridList=R(vr,{optional:!0});_rowspan=1;_colspan=1;constructor(){}get rowspan(){return this._rowspan}set rowspan(e){this._rowspan=Math.round(Ct(e))}get colspan(){return this._colspan}set colspan(e){this._colspan=Math.round(Ct(e))}_setStyle(e,i){this._element.nativeElement.style[e]=i}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=F({type:r,selectors:[["mat-grid-tile"]],hostAttrs:[1,"mat-grid-tile"],hostVars:2,hostBindings:function(i,a){i&2&&L("rowspan",a.rowspan)("colspan",a.colspan)},inputs:{rowspan:"rowspan",colspan:"colspan"},exportAs:["matGridTile"],ngContentSelectors:_r,decls:2,vars:0,consts:[[1,"mat-grid-tile-content"]],template:function(i,a){i&1&&(De(),n(0,"div",0),Re(1),o())},styles:[`.mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}
`],encapsulation:2,changeDetection:0})}return r})();var Wr=/^-?\d+((\.\d+)?[A-Za-z%$]?)+$/,st=class{_gutterSize;_rows=0;_rowspan=0;_cols;_direction;init(t,e,i,a){this._gutterSize=yr(t),this._rows=e.rowCount,this._rowspan=e.rowspan,this._cols=i,this._direction=a}getBaseTileSize(t,e){return`(${t}% - (${this._gutterSize} * ${e}))`}getTilePosition(t,e){return e===0?"0":Ee(`(${t} + ${this._gutterSize}) * ${e}`)}getTileSize(t,e){return`(${t} * ${e}) + (${e-1} * ${this._gutterSize})`}setStyle(t,e,i){let a=100/this._cols,l=(this._cols-1)/this._cols;this.setColStyles(t,i,a,l),this.setRowStyles(t,e,a,l)}setColStyles(t,e,i,a){let l=this.getBaseTileSize(i,a),c=this._direction==="rtl"?"right":"left";t._setStyle(c,this.getTilePosition(l,e)),t._setStyle("width",Ee(this.getTileSize(l,t.colspan)))}getGutterSpan(){return`${this._gutterSize} * (${this._rowspan} - 1)`}getTileSpan(t){return`${this._rowspan} * ${this.getTileSize(t,1)}`}getComputedHeight(){return null}},pi=class extends st{fixedRowHeight;constructor(t){super(),this.fixedRowHeight=t}init(t,e,i,a){super.init(t,e,i,a),this.fixedRowHeight=yr(this.fixedRowHeight),Wr.test(this.fixedRowHeight)}setRowStyles(t,e){t._setStyle("top",this.getTilePosition(this.fixedRowHeight,e)),t._setStyle("height",Ee(this.getTileSize(this.fixedRowHeight,t.rowspan)))}getComputedHeight(){return["height",Ee(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)]}reset(t){t._setListStyle(["height",null]),t._tiles&&t._tiles.forEach(e=>{e._setStyle("top",null),e._setStyle("height",null)})}},ui=class extends st{rowHeightRatio;baseTileHeight;constructor(t){super(),this._parseRatio(t)}setRowStyles(t,e,i,a){let l=i/this.rowHeightRatio;this.baseTileHeight=this.getBaseTileSize(l,a),t._setStyle("marginTop",this.getTilePosition(this.baseTileHeight,e)),t._setStyle("paddingTop",Ee(this.getTileSize(this.baseTileHeight,t.rowspan)))}getComputedHeight(){return["paddingBottom",Ee(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)]}reset(t){t._setListStyle(["paddingBottom",null]),t._tiles.forEach(e=>{e._setStyle("marginTop",null),e._setStyle("paddingTop",null)})}_parseRatio(t){let e=t.split(":");e.length,this.rowHeightRatio=parseFloat(e[0])/parseFloat(e[1])}},gi=class extends st{setRowStyles(t,e){let i=100/this._rowspan,a=(this._rows-1)/this._rows,l=this.getBaseTileSize(i,a);t._setStyle("top",this.getTilePosition(l,e)),t._setStyle("height",Ee(this.getTileSize(l,t.rowspan)))}reset(t){t._tiles&&t._tiles.forEach(e=>{e._setStyle("top",null),e._setStyle("height",null)})}};function Ee(r){return`calc(${r})`}function yr(r){return r.match(/([A-Za-z%]+)$/)?r:`${r}px`}var Yr="fit",br=(()=>{class r{_element=R(Me);_dir=R(Oi,{optional:!0});_cols;_tileCoordinator;_rowHeight;_gutter="1px";_tileStyler;_tiles;constructor(){}get cols(){return this._cols}set cols(e){this._cols=Math.max(1,Math.round(Ct(e)))}get gutterSize(){return this._gutter}set gutterSize(e){this._gutter=`${e??""}`}get rowHeight(){return this._rowHeight}set rowHeight(e){let i=`${e??""}`;i!==this._rowHeight&&(this._rowHeight=i,this._setTileStyler(this._rowHeight))}ngOnInit(){this._checkCols(),this._checkRowHeight()}ngAfterContentChecked(){this._layoutTiles()}_checkCols(){this.cols}_checkRowHeight(){this._rowHeight||this._setTileStyler("1:1")}_setTileStyler(e){this._tileStyler&&this._tileStyler.reset(this),e===Yr?this._tileStyler=new gi:e&&e.indexOf(":")>-1?this._tileStyler=new ui(e):this._tileStyler=new pi(e)}_layoutTiles(){this._tileCoordinator||(this._tileCoordinator=new Gt);let e=this._tileCoordinator,i=this._tiles.filter(l=>!l._gridList||l._gridList===this),a=this._dir?this._dir.value:"ltr";this._tileCoordinator.update(this.cols,i),this._tileStyler.init(this.gutterSize,e,this.cols,a),i.forEach((l,c)=>{let f=e.positions[c];this._tileStyler.setStyle(l,f.row,f.col)}),this._setListStyle(this._tileStyler.getComputedHeight())}_setListStyle(e){e&&(this._element.nativeElement.style[e[0]]=e[1])}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=F({type:r,selectors:[["mat-grid-list"]],contentQueries:function(i,a,l){if(i&1&&Ei(l,hi,5),i&2){let c;q(c=G())&&(a._tiles=c)}},hostAttrs:[1,"mat-grid-list"],hostVars:1,hostBindings:function(i,a){i&2&&L("cols",a.cols)},inputs:{cols:"cols",gutterSize:"gutterSize",rowHeight:"rowHeight"},exportAs:["matGridList"],features:[bt([{provide:vr,useExisting:r}])],ngContentSelectors:_r,decls:2,vars:0,template:function(i,a){i&1&&(De(),n(0,"div"),Re(1),o())},styles:[Qr],encapsulation:2,changeDetection:0})}return r})(),Sr=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=de({type:r});static \u0275inj=se({imports:[oi,me,oi,me]})}return r})();function Zr(r,t){r&1&&(n(0,"div",7),h(1,"mat-spinner",8),n(2,"p"),s(3,"Loading available drivers..."),o()())}function Kr(r,t){if(r&1&&(n(0,"span",15),s(1),o()),r&2){let e=g().$implicit;d(),D(" (",e.rating.toFixed(1)," \u2B50) ")}}function Jr(r,t){if(r&1&&(n(0,"mat-option",13),s(1),p(2,Kr,2,1,"span",14),o()),r&2){let e=t.$implicit;m("value",e.id),d(),D(" ",e.full_name||e.email," "),d(),m("ngIf",e.rating)}}function ea(r,t){r&1&&(n(0,"p",16),s(1," No approved drivers available at this time. "),o())}function ta(r,t){if(r&1){let e=I();n(0,"div")(1,"mat-form-field",9)(2,"mat-label"),s(3,"Select Driver"),o(),n(4,"mat-select",10),Pe("ngModelChange",function(a){y(e);let l=g();return Ie(l.selectedDriverId,a)||(l.selectedDriverId=a),b(a)}),p(5,Jr,3,3,"mat-option",11),o()(),p(6,ea,2,0,"p",12),o()}if(r&2){let e=g();d(4),ke("ngModel",e.selectedDriverId),m("disabled",e.loading),d(),m("ngForOf",e.availableDrivers),d(),m("ngIf",e.availableDrivers.length===0)}}var Qt=class r{constructor(t,e,i,a,l){this.dialogRef=t;this.data=e;this.userService=i;this.ratingService=a;this.snackBar=l}availableDrivers=[];selectedDriverId="";loading=!1;ngOnInit(){this.loadAvailableDrivers()}loadAvailableDrivers(){return x(this,null,function*(){this.loading=!0;try{let t=yield this.userService.getAllUsers();this.availableDrivers=t.filter(e=>e.role==="driver"&&e.is_approved===!0),yield Promise.all(this.availableDrivers.map(e=>x(this,null,function*(){try{let i=yield this.ratingService.getUserRatingSummary(e.id);i&&(e.rating=i.averageRating)}catch(i){console.error(`Error loading rating for driver ${e.id}:`,i)}}))),this.availableDrivers.sort((e,i)=>(i.rating||0)-(e.rating||0)),this.availableDrivers.length===0&&this.snackBar.open("No available drivers found","Close",{duration:3e3})}catch(t){console.error("Error loading drivers:",t),this.snackBar.open("Failed to load available drivers","Close",{duration:3e3})}finally{this.loading=!1}})}onCancel(){this.dialogRef.close()}onAssign(){this.selectedDriverId?this.availableDrivers.some(t=>t.id===this.selectedDriverId)?this.dialogRef.close(this.selectedDriverId):this.snackBar.open("Selected driver is no longer available","Close",{duration:3e3}):this.snackBar.open("Please select a driver","Close",{duration:3e3})}static \u0275fac=function(e){return new(e||r)(C(Le),C(kt),C(re),C(dr),C(j))};static \u0275cmp=F({type:r,selectors:[["app-driver-selection-dialog"]],decls:10,vars:5,consts:[["mat-dialog-title",""],["mat-dialog-content",""],["class","loading-container",4,"ngIf"],[4,"ngIf"],["mat-dialog-actions","","align","end"],["mat-button","",3,"click","disabled"],["mat-raised-button","","color","primary",3,"click","disabled"],[1,"loading-container"],["diameter","40"],["appearance","outline",2,"width","100%"],[3,"ngModelChange","ngModel","disabled"],[3,"value",4,"ngFor","ngForOf"],["class","no-drivers-message",4,"ngIf"],[3,"value"],["class","driver-rating",4,"ngIf"],[1,"driver-rating"],[1,"no-drivers-message"]],template:function(e,i){e&1&&(n(0,"h2",0),s(1,"Assign Driver"),o(),n(2,"div",1),p(3,Zr,4,0,"div",2)(4,ta,7,4,"div",3),o(),n(5,"div",4)(6,"button",5),_("click",function(){return i.onCancel()}),s(7,"Cancel"),o(),n(8,"button",6),_("click",function(){return i.onAssign()}),s(9),o()()),e&2&&(d(3),m("ngIf",i.loading),d(),m("ngIf",!i.loading),d(2),m("disabled",i.loading),d(2),m("disabled",!i.selectedDriverId||i.loading),d(),D(" ",i.loading?"Assigning...":"Assign"," "))},dependencies:[V,wt,Y,Z,X,It,ue,Ne,Ve,Ue,J,H,K,ee,_e,fe,U,$,Ce,we],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:10px;color:#0000008a}.no-drivers-message[_ngcontent-%COMP%]{color:#f44336;font-style:italic;text-align:center;margin:16px 0}.driver-rating[_ngcontent-%COMP%]{margin-left:8px;color:#0000008a}"]})};var ia=["switch"],ra=["*"];function aa(r,t){r&1&&(n(0,"span",10),ne(),n(1,"svg",12),h(2,"path",13),o(),n(3,"svg",14),h(4,"path",15),o()())}var na=new le("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),oa={provide:Li,useExisting:Ci(()=>rt),multi:!0},Kt=class{source;checked;constructor(t,e){this.source=t,this.checked=e}},rt=(()=>{class r{_elementRef=R(Me);_focusMonitor=R(xt);_changeDetectorRef=R(ze);defaults=R(na);_onChange=e=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(e){return new Kt(this,e)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(e){this._checked=e,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new xe;toggleChange=new xe;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){R(Mt).load(Rt);let e=R(new Mi("tabindex"),{optional:!0}),i=this.defaults,a=R(vt,{optional:!0});this.tabIndex=e==null?0:parseInt(e)||0,this.color=i.color||"accent",this._noopAnimations=a==="NoopAnimations",this.id=this._uniqueId=R(Dt).getId("mat-mdc-slide-toggle-"),this.hideIcon=i.hideIcon??!1,this.disabledInteractive=i.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(e=>{e==="keyboard"||e==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):e||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(e){e.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(e){this.checked=!!e}registerOnChange(e){this._onChange=e}registerOnTouched(e){this._onTouched=e}validate(e){return this.required&&e.value!==!0?{required:!0}:null}registerOnValidatorChange(e){this._validatorOnChange=e}setDisabledState(e){this.disabled=e,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new Kt(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=F({type:r,selectors:[["mat-slide-toggle"]],viewQuery:function(i,a){if(i&1&&oe(ia,5),i&2){let l;q(l=G())&&(a._switchElement=l.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(i,a){i&2&&(Ti("id",a.id),L("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),Ri(a.color?"mat-"+a.color:""),ce("mat-mdc-slide-toggle-focused",a._focused)("mat-mdc-slide-toggle-checked",a.checked)("_mat-animation-noopable",a._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",N],color:"color",disabled:[2,"disabled","disabled",N],disableRipple:[2,"disableRipple","disableRipple",N],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:Te(e)],checked:[2,"checked","checked",N],hideIcon:[2,"hideIcon","hideIcon",N],disabledInteractive:[2,"disabledInteractive","disabledInteractive",N]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[bt([oa,{provide:Ni,useExisting:r,multi:!0}]),_t],ngContentSelectors:ra,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(i,a){if(i&1){let l=I();De(),n(0,"div",1)(1,"button",2,0),_("click",function(){return y(l),b(a._handleClick())}),h(3,"span",3),n(4,"span",4)(5,"span",5)(6,"span",6),h(7,"span",7),o(),n(8,"span",8),h(9,"span",9),o(),p(10,aa,5,0,"span",10),o()()(),n(11,"label",11),_("click",function(f){return y(l),b(f.stopPropagation())}),Re(12),o()()}if(i&2){let l=Q(2);m("labelPosition",a.labelPosition),d(),ce("mdc-switch--selected",a.checked)("mdc-switch--unselected",!a.checked)("mdc-switch--checked",a.checked)("mdc-switch--disabled",a.disabled)("mat-mdc-slide-toggle-disabled-interactive",a.disabledInteractive),m("tabIndex",a.disabled&&!a.disabledInteractive?-1:a.tabIndex)("disabled",a.disabled&&!a.disabledInteractive),L("id",a.buttonId)("name",a.name)("aria-label",a.ariaLabel)("aria-labelledby",a._getAriaLabelledBy())("aria-describedby",a.ariaDescribedby)("aria-required",a.required||null)("aria-checked",a.checked)("aria-disabled",a.disabled&&a.disabledInteractive?"true":null),d(8),m("matRippleTrigger",l)("matRippleDisabled",a.disableRipple||a.disabled)("matRippleCentered",!0),d(),W(a.hideIcon?-1:10),d(),m("for",a.buttonId),L("id",a._labelId)}},dependencies:[Fi,$i],styles:[`.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}
`],encapsulation:2,changeDetection:0})}return r})();var Jt=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=de({type:r});static \u0275inj=se({imports:[rt,me,me]})}return r})();var ei=class r{constructor(t,e,i,a){this.dialogRef=t;this.data=e;this.userService=i;this.snackBar=a}formatRole(t){return t.charAt(0).toUpperCase()+t.slice(1)}formatDate(t){return new Date(t).toLocaleString()}toggleApprovalStatus(t){return x(this,null,function*(){try{if(t)if(yield this.userService.approveDriver(this.data.id))this.data.is_approved=!0,this.snackBar.open("User approved successfully","Close",{duration:3e3});else throw new Error("Failed to approve user");else if(yield this.userService.updateUserStatus(this.data.id,!1))this.data.is_approved=!1,this.snackBar.open("User approval revoked successfully","Close",{duration:3e3});else throw new Error("Failed to revoke user approval")}catch(e){console.error("Error updating user approval status:",e),this.snackBar.open("Failed to update user approval status","Close",{duration:3e3}),this.data.is_approved=!t}})}close(){this.dialogRef.close()}static \u0275fac=function(e){return new(e||r)(C(Le),C(kt),C(re),C(j))};static \u0275cmp=F({type:r,selectors:[["app-user-details-dialog"]],decls:38,vars:8,consts:[["mat-dialog-title",""],[1,"user-details"],[1,"detail-row"],[1,"label"],[1,"value"],["color","primary","selected",""],["selected","",3,"color"],["color","primary",3,"change","checked"],["align","end"],["mat-button","",3,"click"]],template:function(e,i){e&1&&(n(0,"h2",0),s(1,"User Details"),o(),n(2,"mat-dialog-content")(3,"div",1)(4,"div",2)(5,"span",3),s(6,"Email:"),o(),n(7,"span",4),s(8),o()(),n(9,"div",2)(10,"span",3),s(11,"Full Name:"),o(),n(12,"span",4),s(13),o()(),n(14,"div",2)(15,"span",3),s(16,"Phone:"),o(),n(17,"span",4),s(18),o()(),n(19,"div",2)(20,"span",3),s(21,"Role:"),o(),n(22,"mat-chip",5),s(23),o()(),n(24,"div",2)(25,"span",3),s(26,"Status:"),o(),n(27,"mat-chip",6),s(28),o(),n(29,"mat-slide-toggle",7),_("change",function(l){return i.toggleApprovalStatus(l.checked)}),o()(),n(30,"div",2)(31,"span",3),s(32,"Registered:"),o(),n(33,"span",4),s(34),o()()()(),n(35,"mat-dialog-actions",8)(36,"button",9),_("click",function(){return i.close()}),s(37,"Close"),o()()),e&2&&(d(8),M(i.data.email),d(5),M(i.data.full_name||"Not provided"),d(5),M(i.data.phone||"Not provided"),d(5),M(i.formatRole(i.data.role)),d(4),m("color",i.data.is_approved?"primary":"warn"),d(),D(" ",i.data.is_approved?"Approved":"Pending Approval"," "),d(),m("checked",i.data.is_approved),d(5),M(i.formatDate(i.data.created_at)))},dependencies:[V,Z,ue,Ne,Ve,Ue,U,$,ie,te,Je,At,Et,Jt,rt],styles:[".user-details[_ngcontent-%COMP%]{padding:16px}.detail-row[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:16px}.label[_ngcontent-%COMP%]{font-weight:500;min-width:120px;color:#0000008a}.value[_ngcontent-%COMP%]{flex:1}mat-slide-toggle[_ngcontent-%COMP%]{margin-left:8px}"]})};var la=["cardElement"];function da(r,t){r&1&&(n(0,"div",4)(1,"p"),s(2,"Loading Stripe SDK..."),o(),h(3,"mat-spinner",5),o())}function ca(r,t){r&1&&(n(0,"mat-error"),s(1," Amount is required "),o())}function ma(r,t){r&1&&(n(0,"mat-error"),s(1," Amount must be at least $1 "),o())}function pa(r,t){if(r&1&&(n(0,"div",18),s(1),o()),r&2){let e=g(2);d(),M(e.cardError)}}function ua(r,t){if(r&1){let e=I();n(0,"div",6)(1,"div",7)(2,"h3"),s(3,"Process a Payment"),o(),n(4,"p"),s(5,"Use this form to process a one-time payment"),o(),n(6,"form",8),_("ngSubmit",function(){y(e);let a=g();return b(a.processPayment())}),n(7,"div",9)(8,"mat-form-field",10)(9,"mat-label"),s(10,"Amount (USD)"),o(),h(11,"input",11),p(12,ca,2,0,"mat-error",12)(13,ma,2,0,"mat-error",12),o()(),n(14,"div",9)(15,"mat-form-field",10)(16,"mat-label"),s(17,"Description"),o(),h(18,"input",13),o()(),n(19,"div",9),h(20,"div",14,0),p(22,pa,2,1,"div",15),o(),n(23,"div",16)(24,"button",17)(25,"mat-icon"),s(26,"payment"),o(),s(27),o()()()()()}if(r&2){let e,i,a=g();d(6),m("formGroup",a.paymentForm),d(6),m("ngIf",(e=a.paymentForm.get("amount"))==null?null:e.hasError("required")),d(),m("ngIf",(i=a.paymentForm.get("amount"))==null?null:i.hasError("min")),d(9),m("ngIf",a.cardError),d(2),m("disabled",a.paymentForm.invalid||a.processing),d(3),D(" ",a.processing?"Processing...":"Process Payment"," ")}}var ti=class r{constructor(t,e,i,a,l){this.formBuilder=t;this.snackBar=e;this.paymentService=i;this.rideService=a;this.authService=l;this.paymentForm=this.formBuilder.group({amount:[10,[z.required,z.min(1)]],description:["",z.required]})}cardElement;paymentForm;stripe;card;sdkLoaded=!1;processing=!1;cardError="";paymentResult=null;recentTransactions=[];ngOnInit(){return x(this,null,function*(){yield this.loadStripe()})}ngAfterViewInit(){this.sdkLoaded&&this.initializeCard()}loadStripe(){return x(this,null,function*(){try{let t=yield nr(si.stripePublishableKey);this.stripe=t,this.sdkLoaded=!0,setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error loading Stripe:",t),this.snackBar.open("Error loading Stripe. Please check your API keys.","Close",{duration:5e3})}})}loadStripeScript(){if(window.Stripe){this.initializeStripe();return}let t=document.createElement("script");t.src="https://js.stripe.com/v3/",t.async=!0,t.onload=()=>{this.initializeStripe()},document.body.appendChild(t)}initializeStripe(){if(!window.Stripe){this.snackBar.open("Stripe SDK not available","Close",{duration:3e3});return}try{this.stripe=window.Stripe(si.stripePublishableKey),setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error initializing Stripe:",t),this.snackBar.open("Error initializing Stripe payments. Check your credentials.","Close",{duration:5e3})}}initializeCard(){if(!this.cardElement||!this.cardElement.nativeElement||!this.stripe){setTimeout(()=>this.initializeCard(),100);return}try{let t=this.stripe.elements();this.card=t.create("card",{style:{base:{iconColor:"#666EE8",color:"#31325F",fontWeight:400,fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSize:"16px","::placeholder":{color:"#CFD7E0"}}}}),this.card.mount(this.cardElement.nativeElement),this.card.on("change",e=>{this.cardError=e.error?e.error.message:""}),this.sdkLoaded=!0}catch(t){console.error("Error initializing Stripe card:",t),this.snackBar.open("Error initializing Stripe card form","Close",{duration:5e3})}}loadRecentTransactions(){return x(this,null,function*(){try{let{data:t,error:e}=yield this.authService.supabase.functions.invoke("stripe",{body:{action:"listPaymentIntents",limit:10}});if(e){console.error("Error loading recent transactions:",e),this.snackBar.open("Error loading recent transactions","Close",{duration:3e3});return}t&&t.paymentIntents&&(this.recentTransactions=t.paymentIntents.map(i=>({id:i.id,date:new Date(i.created*1e3),amount:i.amount/100,description:i.description||"No description",status:this.formatPaymentStatus(i.status)})))}catch(t){console.error("Error loading recent transactions:",t),this.snackBar.open("Error loading recent transactions","Close",{duration:3e3})}})}formatPaymentStatus(t){return t.charAt(0).toUpperCase()+t.slice(1)}processPayment(){return x(this,null,function*(){if(this.paymentForm.invalid||!this.card||!this.stripe)return;this.processing=!0,this.paymentResult=null;let t=this.paymentForm.get("amount")?.value,e=this.paymentForm.get("description")?.value;try{let{paymentMethod:i,error:a}=yield this.stripe.createPaymentMethod({type:"card",card:this.card});if(a)throw a;let l={amount:t*100,currency:"usd",description:e,payment_method:i.id};console.log(l);let{data:c,error:f}=yield this.authService.supabase.functions.invoke("stripe",{body:l});if(f)throw console.error("Error creating payment intent:",f),new Error(`Failed to create payment intent: ${f.message}`);if(console.log("Payment intent created:",c),!c||!c.client_secret)throw new Error("No client secret returned from payment intent creation");let S=c.client_secret,{error:k,paymentIntent:O}=yield this.stripe.confirmCardPayment(S,{payment_method:i.id});if(k)throw k;this.paymentResult={success:!0,paymentIntent:O},this.snackBar.open("Payment processed successfully!","Close",{duration:3e3}),this.paymentForm.reset({amount:10}),this.card.clear()}catch(i){console.error("Error processing payment:",i),this.paymentResult={success:!1,error:{message:i.message}},this.snackBar.open(`Payment error: ${i.message}`,"Close",{duration:5e3})}finally{this.processing=!1}})}static \u0275fac=function(e){return new(e||r)(C(Ge),C(j),C(Ke),C(Se),C(Yi))};static \u0275cmp=F({type:r,selectors:[["app-stripe-payment"]],viewQuery:function(e,i){if(e&1&&oe(la,5),e&2){let a;q(a=G())&&(i.cardElement=a.first)}},decls:10,vars:2,consts:[["cardElement",""],[1,"container"],["class","sdk-status",4,"ngIf"],["class","payment-tabs",4,"ngIf"],[1,"sdk-status"],["diameter","30"],[1,"payment-tabs"],[1,"payment-section"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline"],["matInput","","type","number","formControlName","amount","min","1","step","0.01"],[4,"ngIf"],["matInput","","formControlName","description","placeholder","Payment description"],[1,"card-element"],["class","card-errors",4,"ngIf"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"card-errors"]],template:function(e,i){e&1&&(n(0,"div",1)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),s(4,"Stripe Payment Processing"),o(),n(5,"mat-card-subtitle"),s(6,"Process payments securely with Stripe"),o()(),n(7,"mat-card-content"),p(8,da,4,0,"div",2)(9,ua,28,6,"div",3),o()()()),e&2&&(d(8),m("ngIf",!i.sdkLoaded),d(),m("ngIf",i.sdkLoaded))},dependencies:[V,Y,Z,je,ge,Pt,X,$e,Ui,he,He,qe,ie,We,Xe,Ze,Xi,Ye,J,H,K,Qe,be,ye,U,$,ee,pe,Ce,we,Je,te,ve,et],styles:[".container[_ngcontent-%COMP%]{padding:20px}.form-row[_ngcontent-%COMP%]{margin-bottom:20px}mat-form-field[_ngcontent-%COMP%]{width:100%}.card-element[_ngcontent-%COMP%]{border:1px solid #ccc;padding:10px;border-radius:4px;height:40px;background-color:#fff}.card-errors[_ngcontent-%COMP%]{color:#f44336;margin-top:8px;font-size:14px}.form-actions[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.divider[_ngcontent-%COMP%]{margin:30px 0}.payment-result[_ngcontent-%COMP%]{margin-top:20px}.payment-result[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:10px;border-radius:4px;overflow-x:auto}.payment-section[_ngcontent-%COMP%]{margin-bottom:30px}.payment-tabs[_ngcontent-%COMP%]{margin-top:20px}.recent-transactions[_ngcontent-%COMP%]{margin-top:30px}table[_ngcontent-%COMP%]{width:100%}.status-succeeded[_ngcontent-%COMP%], .status-completed[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.status-pending[_ngcontent-%COMP%]{color:#ff9800;font-weight:500}.status-failed[_ngcontent-%COMP%], .status-refunded[_ngcontent-%COMP%]{color:#f44336;font-weight:500}.result-card[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;border-radius:4px;margin-top:10px}.result-card.success[_ngcontent-%COMP%]{background-color:#4caf501a}.result-card.error[_ngcontent-%COMP%]{background-color:#f443361a}.result-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:15px;font-size:24px;height:24px;width:24px}.result-card.success[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#4caf50}.result-card.error[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f44336}.result-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px}.result-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.sdk-status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;padding:40px}"]})};function fa(r,t){r&1&&(n(0,"mat-error"),s(1," Name is required "),o())}function _a(r,t){r&1&&(n(0,"mat-error"),s(1," Base fare is required "),o())}function va(r,t){r&1&&(n(0,"mat-error"),s(1," Base fare must be positive "),o())}function ya(r,t){r&1&&(n(0,"mat-error"),s(1," Distance rate is required "),o())}function ba(r,t){r&1&&(n(0,"mat-error"),s(1," Distance rate must be positive "),o())}function Sa(r,t){r&1&&(n(0,"mat-error"),s(1," Time rate is required "),o())}function wa(r,t){r&1&&(n(0,"mat-error"),s(1," Time rate must be positive "),o())}function Ca(r,t){r&1&&(n(0,"div",20),h(1,"mat-spinner",21),n(2,"p"),s(3,"Loading pricing configurations..."),o()())}function xa(r,t){r&1&&(n(0,"th",35),s(1,"Name"),o())}function Ma(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;d(),M(e.name)}}function Da(r,t){r&1&&(n(0,"th",35),s(1,"Base Fare"),o())}function Ra(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;d(),D("$",e.base_fare.toFixed(2),"")}}function ka(r,t){r&1&&(n(0,"th",35),s(1,"Distance Rate"),o())}function Ia(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;d(),D("$",e.distance_rate.toFixed(2),"/mile")}}function Pa(r,t){r&1&&(n(0,"th",35),s(1,"Time Rate"),o())}function Ta(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;d(),D("$",e.time_rate.toFixed(2),"/min")}}function Ea(r,t){r&1&&(n(0,"th",35),s(1,"Status"),o())}function Aa(r,t){if(r&1&&(n(0,"td",36)(1,"span",37),s(2),o()()),r&2){let e=t.$implicit;d(),ce("active",e.is_active),d(),D(" ",e.is_active?"Active":"Inactive"," ")}}function Fa(r,t){r&1&&(n(0,"th",35),s(1,"Actions"),o())}function Oa(r,t){if(r&1){let e=I();n(0,"td",36)(1,"button",38),_("click",function(){let a=y(e).$implicit,l=g(2);return b(l.editPricing(a))}),n(2,"mat-icon"),s(3,"edit"),o()(),n(4,"button",39),_("click",function(){let a=y(e).$implicit,l=g(2);return b(l.toggleActive(a))}),n(5,"mat-icon"),s(6),o()(),n(7,"button",40),_("click",function(){let a=y(e).$implicit,l=g(2);return b(l.deletePricing(a.id))}),n(8,"mat-icon"),s(9,"delete"),o()()()}if(r&2){let e=t.$implicit;d(4),m("matTooltip",e.is_active?"Deactivate":"Activate"),d(2),M(e.is_active?"toggle_on":"toggle_off"),d(),m("disabled",e.is_active)}}function za(r,t){r&1&&h(0,"tr",41)}function Ba(r,t){r&1&&h(0,"tr",42)}function La(r,t){r&1&&(n(0,"div",43)(1,"p"),s(2,"No pricing configurations found. Create one to get started."),o()())}function Na(r,t){if(r&1&&(n(0,"div",22)(1,"table",23),E(2,24),p(3,xa,2,0,"th",25)(4,Ma,2,1,"td",26),A(),E(5,27),p(6,Da,2,0,"th",25)(7,Ra,2,1,"td",26),A(),E(8,28),p(9,ka,2,0,"th",25)(10,Ia,2,1,"td",26),A(),E(11,29),p(12,Pa,2,0,"th",25)(13,Ta,2,1,"td",26),A(),E(14,30),p(15,Ea,2,0,"th",25)(16,Aa,3,3,"td",26),A(),E(17,31),p(18,Fa,2,0,"th",25)(19,Oa,10,3,"td",26),A(),p(20,za,1,0,"tr",32)(21,Ba,1,0,"tr",33),o(),p(22,La,3,0,"div",34),o()),r&2){let e=g();d(),m("dataSource",e.pricingConfigurations),d(19),m("matHeaderRowDef",e.displayedColumns),d(),m("matRowDefColumns",e.displayedColumns),d(),m("ngIf",e.pricingConfigurations.length===0)}}var ii=class r{constructor(t,e,i){this.fb=t;this.ridePricingService=e;this.snackBar=i;this.pricingForm=this.fb.group({name:["",[z.required]],base_fare:[5,[z.required,z.min(0)]],distance_rate:[1.5,[z.required,z.min(0)]],time_rate:[.25,[z.required,z.min(0)]],is_active:[!0]})}pricingForm;pricingConfigurations=[];displayedColumns=["name","base_fare","distance_rate","time_rate","is_active","actions"];loading=!1;activePricing=null;editMode=!1;editingId=null;ngOnInit(){this.loadPricingConfigurations()}loadPricingConfigurations(){return x(this,null,function*(){this.loading=!0;try{this.pricingConfigurations=yield this.ridePricingService.getAllPricing(),this.activePricing=yield this.ridePricingService.loadActivePricing()}catch(t){console.error("Error loading pricing configurations:",t),this.snackBar.open("Failed to load pricing configurations","Close",{duration:3e3})}finally{this.loading=!1}})}onSubmit(){return x(this,null,function*(){if(!this.pricingForm.invalid){this.loading=!0;try{let t=this.pricingForm.value;this.editMode&&this.editingId?(yield this.ridePricingService.updatePricing(this.editingId,t),this.snackBar.open("Pricing configuration updated successfully","Close",{duration:3e3})):(yield this.ridePricingService.createPricing(t),this.snackBar.open("Pricing configuration created successfully","Close",{duration:3e3})),this.resetForm(),yield this.loadPricingConfigurations()}catch(t){console.error("Error saving pricing configuration:",t),this.snackBar.open("Failed to save pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}}})}editPricing(t){this.editMode=!0,this.editingId=t.id,this.pricingForm.patchValue({name:t.name,base_fare:t.base_fare,distance_rate:t.distance_rate,time_rate:t.time_rate,is_active:t.is_active})}resetForm(){this.editMode=!1,this.editingId=null,this.pricingForm.reset({name:"",base_fare:5,distance_rate:1.5,time_rate:.25,is_active:!0})}toggleActive(t){return x(this,null,function*(){this.loading=!0;try{yield this.ridePricingService.setActiveStatus(t.id,!t.is_active),yield this.loadPricingConfigurations(),this.snackBar.open(`Pricing configuration ${t.is_active?"deactivated":"activated"} successfully`,"Close",{duration:3e3})}catch(e){console.error("Error toggling active status:",e),this.snackBar.open("Failed to update pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}})}deletePricing(t){return x(this,null,function*(){if(confirm("Are you sure you want to delete this pricing configuration?")){this.loading=!0;try{yield this.ridePricingService.deletePricing(t),yield this.loadPricingConfigurations(),this.snackBar.open("Pricing configuration deleted successfully","Close",{duration:3e3})}catch(e){console.error("Error deleting pricing configuration:",e),this.snackBar.open("Failed to delete pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}}})}calculateSampleFare(){let t=this.pricingForm.get("base_fare")?.value||0,e=this.pricingForm.get("distance_rate")?.value||0,i=this.pricingForm.get("time_rate")?.value||0;return+(t+10*e+20*i).toFixed(2)}static \u0275fac=function(e){return new(e||r)(C(Ge),C(rr),C(j))};static \u0275cmp=F({type:r,selectors:[["app-ride-pricing"]],decls:57,vars:15,consts:[[1,"pricing-container"],[1,"form-card"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline",1,"full-width"],["matInput","","formControlName","name","placeholder","e.g., Standard, Premium, etc."],[4,"ngIf"],["appearance","outline"],["matInput","","type","number","step","0.01","formControlName","base_fare"],["matInput","","type","number","step","0.01","formControlName","distance_rate"],["matInput","","type","number","step","0.01","formControlName","time_rate"],[1,"active-toggle"],["formControlName","is_active","color","primary"],[1,"sample-calculation"],[1,"form-actions"],["mat-button","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"table-card"],["class","loading-container",4,"ngIf"],["class","table-container",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"table-container"],["mat-table","",1,"mat-elevation-z2",3,"dataSource"],["matColumnDef","name"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","base_fare"],["matColumnDef","distance_rate"],["matColumnDef","time_rate"],["matColumnDef","is_active"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","no-data",4,"ngIf"],["mat-header-cell",""],["mat-cell",""],[1,"status-badge"],["mat-icon-button","","color","primary","matTooltip","Edit",3,"click"],["mat-icon-button","","color","accent",3,"click","matTooltip"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click","disabled"],["mat-header-row",""],["mat-row",""],[1,"no-data"]],template:function(e,i){if(e&1&&(n(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),s(4),o()(),n(5,"mat-card-content")(6,"form",2),_("ngSubmit",function(){return i.onSubmit()}),n(7,"div",3)(8,"mat-form-field",4)(9,"mat-label"),s(10,"Configuration Name"),o(),h(11,"input",5),p(12,fa,2,0,"mat-error",6),o()(),n(13,"div",3)(14,"mat-form-field",7)(15,"mat-label"),s(16,"Base Fare ($)"),o(),h(17,"input",8),p(18,_a,2,0,"mat-error",6)(19,va,2,0,"mat-error",6),o(),n(20,"mat-form-field",7)(21,"mat-label"),s(22,"Distance Rate ($ per mile)"),o(),h(23,"input",9),p(24,ya,2,0,"mat-error",6)(25,ba,2,0,"mat-error",6),o(),n(26,"mat-form-field",7)(27,"mat-label"),s(28,"Time Rate ($ per minute)"),o(),h(29,"input",10),p(30,Sa,2,0,"mat-error",6)(31,wa,2,0,"mat-error",6),o()(),n(32,"div",3)(33,"div",11)(34,"mat-slide-toggle",12),s(35," Set as Active Configuration "),o()()(),n(36,"div",13),h(37,"mat-divider"),n(38,"h3"),s(39,"Sample Fare Calculation"),o(),n(40,"p"),s(41,"For a 10-mile, 20-minute ride: "),n(42,"strong"),s(43),o()(),h(44,"mat-divider"),o(),n(45,"div",14)(46,"button",15),_("click",function(){return i.resetForm()}),s(47),o(),n(48,"button",16),s(49),o()()()()(),n(50,"mat-card",17)(51,"mat-card-header")(52,"mat-card-title"),s(53,"Pricing Configurations"),o()(),n(54,"mat-card-content"),p(55,Ca,4,0,"div",18)(56,Na,23,4,"div",19),o()()()),e&2){let a,l,c,f,S,k,O;d(4),M(i.editMode?"Edit Pricing Configuration":"Create New Pricing Configuration"),d(2),m("formGroup",i.pricingForm),d(6),m("ngIf",(a=i.pricingForm.get("name"))==null?null:a.hasError("required")),d(6),m("ngIf",(l=i.pricingForm.get("base_fare"))==null?null:l.hasError("required")),d(),m("ngIf",(c=i.pricingForm.get("base_fare"))==null?null:c.hasError("min")),d(5),m("ngIf",(f=i.pricingForm.get("distance_rate"))==null?null:f.hasError("required")),d(),m("ngIf",(S=i.pricingForm.get("distance_rate"))==null?null:S.hasError("min")),d(5),m("ngIf",(k=i.pricingForm.get("time_rate"))==null?null:k.hasError("required")),d(),m("ngIf",(O=i.pricingForm.get("time_rate"))==null?null:O.hasError("min")),d(12),D("$",i.calculateSampleFare(),""),d(4),D(" ",i.editMode?"Cancel":"Reset"," "),d(),m("disabled",i.pricingForm.invalid||i.loading),d(),D(" ",i.editMode?"Update":"Create"," "),d(6),m("ngIf",i.loading),d(),m("ngIf",!i.loading)}},dependencies:[V,Y,Z,je,ge,Pt,X,$e,he,He,qe,ie,We,Xe,Ze,Ye,J,H,K,Qe,be,ye,U,$,Be,te,ve,et,Ft,zt,Ut,Bt,Ot,Vt,Lt,Nt,$t,jt,pe,Ce,we,Jt,rt,it,tt,Je,ar],styles:[".pricing-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;max-width:1200px;margin:0 auto}.form-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]{width:100%}.form-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:16px;align-items:center}.full-width[_ngcontent-%COMP%]{width:100%}mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:150px}.active-toggle[_ngcontent-%COMP%]{margin:16px 0}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:8px;margin-top:16px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}table[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#0000008a}.no-data[_ngcontent-%COMP%]{text-align:center;padding:20px;color:#0000008a}.status-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500;background-color:#e0e0e0;color:#757575}.status-badge.active[_ngcontent-%COMP%]{background-color:#c8e6c9;color:#2e7d32}.sample-calculation[_ngcontent-%COMP%]{margin:20px 0;padding:10px 0}.sample-calculation[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:10px 0;font-size:16px;font-weight:500}.sample-calculation[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:10px 0}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{flex-direction:column}mat-form-field[_ngcontent-%COMP%]{width:100%}}"]})};function Ua(r,t){if(r&1&&(n(0,"mat-option",21),s(1),o()),r&2){let e=t.$implicit;m("value",e.id),d(),D(" ",e.full_name||e.email," ")}}function Va(r,t){r&1&&(n(0,"mat-error"),s(1," Rider is required "),o())}function $a(r,t){r&1&&(n(0,"mat-error"),s(1," Pickup location is required "),o())}function ja(r,t){r&1&&(n(0,"mat-error"),s(1," Dropoff location is required "),o())}function Ha(r,t){if(r&1&&(n(0,"p"),s(1),o()),r&2){let e=g(3);d(),D("Distance: ",e.estimatedDistance," miles")}}function qa(r,t){if(r&1&&(n(0,"p"),s(1),o()),r&2){let e=g(3);d(),D("Duration: ",e.estimatedDuration," minutes")}}function Ga(r,t){if(r&1&&(n(0,"div",24)(1,"p"),s(2,"Estimated fare: "),n(3,"strong"),s(4),o()(),p(5,Ha,2,1,"p",7)(6,qa,2,1,"p",7),o()),r&2){let e=g(2);d(4),M(e.estimatedFare?"$"+e.estimatedFare.toFixed(2):""),d(),m("ngIf",e.estimatedDistance),d(),m("ngIf",e.estimatedDuration)}}function Qa(r,t){if(r&1&&(n(0,"div"),h(1,"app-map-display",22),p(2,Ga,7,3,"div",23),o()),r&2){let e,i,a=g();d(),m("origin",(e=a.rideForm.get("pickup_location"))==null?null:e.value)("destination",(i=a.rideForm.get("dropoff_location"))==null?null:i.value),d(),m("ngIf",a.estimatedFare)}}function Wa(r,t){r&1&&(n(0,"mat-error"),s(1," Pickup date is required "),o())}function Ya(r,t){r&1&&(n(0,"mat-error"),s(1," Pickup time is required "),o())}var ri=class r{constructor(t,e,i,a,l,c,f){this.formBuilder=t;this.dialogRef=e;this.rideService=i;this.userService=a;this.locationService=l;this.paymentService=c;this.snackBar=f;this.rideForm=this.formBuilder.group({rider_id:["",z.required],pickup_location:["",z.required],dropoff_location:["",z.required],pickup_date:[new Date,z.required],pickup_time:["12:00 PM",z.required]})}rideForm;riders=[];loading=!1;showMap=!1;estimatedFare=null;estimatedDistance=null;estimatedDuration=null;locationCoordinates={};ngOnInit(){return x(this,null,function*(){try{let t=yield this.userService.getUsersByRole("rider");this.riders=t}catch(t){console.error("Error loading riders:",t),this.snackBar.open("Failed to load riders","Close",{duration:3e3})}this.rideForm.get("pickup_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()}),this.rideForm.get("dropoff_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()})})}updateRouteEstimates(){return x(this,null,function*(){let t=this.rideForm.get("pickup_location")?.value,e=this.rideForm.get("dropoff_location")?.value;if(t&&e){this.showMap=!0;try{let{fare:i,routeInfo:a}=yield this.paymentService.estimateFare(t,e);this.estimatedFare=i,this.estimatedDistance=a.distance,this.estimatedDuration=a.duration}catch(i){console.error("Error calculating route:",i)}}else this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null})}onSubmit(){return x(this,null,function*(){if(!this.rideForm.invalid){this.loading=!0;try{this.locationCoordinates.pickup||(this.locationCoordinates.pickup=yield this.locationService.geocodeAddress(this.rideForm.value.pickup_location)),this.locationCoordinates.dropoff||(this.locationCoordinates.dropoff=yield this.locationService.geocodeAddress(this.rideForm.value.dropoff_location));let t=yield this.locationService.calculateRoute(this.locationCoordinates.pickup,this.locationCoordinates.dropoff),e=this.rideForm.value.pickup_date,i=this.rideForm.value.pickup_time,a=new Date(e),l=i.match(/(\d+):(\d+)\s?(AM|PM)?/i);if(l){let S=parseInt(l[1],10),k=parseInt(l[2],10),O=l[3]?l[3].toUpperCase():null;O==="PM"&&S<12?S+=12:O==="AM"&&S===12&&(S=0),a.setHours(S,k,0,0)}let c=nt(at({},this.rideForm.value),{status:"requested",pickup_time:a.toISOString(),pickup_latitude:this.locationCoordinates.pickup?.latitude,pickup_longitude:this.locationCoordinates.pickup?.longitude,dropoff_latitude:this.locationCoordinates.dropoff?.latitude,dropoff_longitude:this.locationCoordinates.dropoff?.longitude,distance_miles:t.distance,duration_minutes:t.duration,fare:this.estimatedFare||(yield this.paymentService.estimateFare(this.rideForm.value.pickup_location,this.rideForm.value.dropoff_location)).fare}),f=yield this.rideService.createRide(c);this.snackBar.open("Ride created successfully!","Close",{duration:3e3}),this.dialogRef.close(f)}catch(t){console.error("Error creating ride:",t),this.snackBar.open(t.message||"Failed to create ride","Close",{duration:3e3})}finally{this.loading=!1}}})}static \u0275fac=function(e){return new(e||r)(C(Ge),C(Le),C(Se),C(re),C(tr),C(Ke),C(j))};static \u0275cmp=F({type:r,selectors:[["app-admin-ride-create-dialog"]],decls:50,vars:14,consts:[["picker",""],["timepicker",""],["mat-dialog-title",""],[3,"formGroup"],["appearance","outline",1,"full-width"],["formControlName","rider_id","required",""],[3,"value",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"location-fields"],["matInput","","formControlName","pickup_location","placeholder","Enter pickup location"],["matInput","","formControlName","dropoff_location","placeholder","Enter dropoff location"],[1,"date-time-fields"],["appearance","outline"],["matInput","","formControlName","pickup_date",3,"matDatepicker"],["matSuffix","",3,"for"],["matInput","","formControlName","pickup_time",3,"ngxMatTimepicker"],["ngxMatTimepickerToggleIcon",""],["matInput","","formControlName","notes","placeholder","Any special requirements?"],["align","end"],["mat-button","","mat-dialog-close",""],["mat-raised-button","","color","primary",3,"click","disabled"],[3,"value"],[3,"origin","destination"],["class","fare-estimate",4,"ngIf"],[1,"fare-estimate"]],template:function(e,i){if(e&1){let a=I();n(0,"h2",2),s(1,"Create Ride for User"),o(),n(2,"mat-dialog-content")(3,"form",3)(4,"mat-form-field",4)(5,"mat-label"),s(6,"Select Rider"),o(),n(7,"mat-select",5),p(8,Ua,2,2,"mat-option",6),o(),p(9,Va,2,0,"mat-error",7),o(),n(10,"div",8)(11,"mat-form-field",4)(12,"mat-label"),s(13,"Pickup Location"),o(),h(14,"input",9),p(15,$a,2,0,"mat-error",7),o(),n(16,"mat-form-field",4)(17,"mat-label"),s(18,"Dropoff Location"),o(),h(19,"input",10),p(20,ja,2,0,"mat-error",7),o()(),p(21,Qa,3,3,"div",7),n(22,"div",11)(23,"mat-form-field",12)(24,"mat-label"),s(25,"Pickup Date"),o(),h(26,"input",13)(27,"mat-datepicker-toggle",14)(28,"mat-datepicker",null,0),p(30,Wa,2,0,"mat-error",7),o(),n(31,"mat-form-field",12)(32,"mat-label"),s(33,"Pickup Time"),o(),h(34,"input",15),n(35,"ngx-mat-timepicker-toggle",14)(36,"mat-icon",16),s(37,"keyboard_arrow_down"),o()(),h(38,"ngx-mat-timepicker",null,1),p(40,Ya,2,0,"mat-error",7),o()(),n(41,"mat-form-field",4)(42,"mat-label"),s(43,"Special Notes"),o(),h(44,"textarea",17),o()()(),n(45,"mat-dialog-actions",18)(46,"button",19),s(47,"Cancel"),o(),n(48,"button",20),_("click",function(){return y(a),b(i.onSubmit())}),s(49),o()()}if(e&2){let a,l,c,f,S,k,O=Q(29),B=Q(39);d(3),m("formGroup",i.rideForm),d(5),m("ngForOf",i.riders),d(),m("ngIf",(a=i.rideForm.get("rider_id"))==null||a.errors==null?null:a.errors.required),d(6),m("ngIf",(l=i.rideForm.get("pickup_location"))==null||l.errors==null?null:l.errors.required),d(5),m("ngIf",(c=i.rideForm.get("dropoff_location"))==null||c.errors==null?null:c.errors.required),d(),m("ngIf",i.showMap&&((f=i.rideForm.get("pickup_location"))==null?null:f.value)&&((f=i.rideForm.get("dropoff_location"))==null?null:f.value)),d(5),m("matDatepicker",O),d(),m("for",O),d(3),m("ngIf",(S=i.rideForm.get("pickup_date"))==null||S.errors==null?null:S.errors.required),d(4),m("ngxMatTimepicker",B),d(),m("for",B),d(5),m("ngIf",(k=i.rideForm.get("pickup_time"))==null||k.errors==null?null:k.errors.required),d(8),m("disabled",i.rideForm.invalid||i.loading),d(),D(" ",i.loading?"Creating...":"Create Ride"," ")}},dependencies:[V,wt,Y,he,je,ge,X,$e,Vi,He,qe,ue,Bi,Ne,Ve,Ue,ie,J,H,K,Qe,Tt,be,ye,U,$,er,Zi,Ki,Ji,ji,te,ve,ee,_e,fe,pe,Wi,Hi,Gi,Qi,qi,ir],styles:[".full-width[_ngcontent-%COMP%]{width:100%}form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;min-width:500px}.location-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.date-time-fields[_ngcontent-%COMP%]{display:flex;gap:16px}textarea[_ngcontent-%COMP%]{min-height:80px}.fare-estimate[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:4px;margin-top:16px;margin-bottom:16px}.fare-estimate[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}"]})};var ai=class r{constructor(t,e,i){this.userService=t;this.rideService=e;this.paymentService=i}statisticsSubject=new Si(null);statistics$=this.statisticsSubject.asObservable();generateSystemStatistics(){return x(this,null,function*(){let t=yield this.userService.getAllUsers(),e=t.filter(P=>P.role==="rider"),i=t.filter(P=>P.role==="driver"),a=t.filter(P=>P.role==="admin"),l=yield this.rideService.getAllRides(),c=l.filter(P=>P.status==="completed"),f=l.filter(P=>P.status==="in-progress"),S=l.filter(P=>P.status==="requested"),k=l.filter(P=>P.status==="canceled"),O=[{id:"1",type:"user_registered",timestamp:new Date().toISOString(),details:{userId:"user1",role:"rider"}},{id:"2",type:"ride_requested",timestamp:new Date(Date.now()-36e5).toISOString(),details:{rideId:"ride1",riderId:"rider1"}},{id:"3",type:"ride_completed",timestamp:new Date(Date.now()-72e5).toISOString(),details:{rideId:"ride2",riderId:"rider2",driverId:"driver1"}},{id:"4",type:"driver_approved",timestamp:new Date(Date.now()-108e5).toISOString(),details:{driverId:"driver2"}}],B={totalUsers:{all:t.length,riders:e.length,drivers:i.length,admins:a.length},rides:{total:l.length,completed:c.length,inProgress:f.length,requested:S.length,canceled:k.length},recentActivity:O};return this.statisticsSubject.next(B),B})}generateRideReport(t){return x(this,null,function*(){let{dateRange:e}=t,a=(yield this.rideService.getAllRides()).filter(u=>{let v=new Date(u.created_at);return v>=e.startDate&&v<=e.endDate}),l=a.filter(u=>u.status==="requested").length,c=a.filter(u=>u.status==="assigned").length,f=a.filter(u=>u.status==="in-progress").length,S=a.filter(u=>u.status==="completed").length,k=a.filter(u=>u.status==="canceled").length,O=this.groupRidesByDay(a),B=a.filter(u=>u.status==="completed"&&u.duration_minutes&&u.distance_miles),P=B.length>0?B.reduce((u,v)=>u+(v.duration_minutes||0),0)/B.length:0,Ae=B.length>0?B.reduce((u,v)=>u+(v.distance_miles||0),0)/B.length:0;return{dateRange:e,ridesByStatus:{requested:l,assigned:c,inProgress:f,completed:S,canceled:k},ridesByDay:O,averageDuration:P,averageDistance:Ae}})}generateRevenueReport(t){return x(this,null,function*(){let{dateRange:e}=t,i=yield this.rideService.getAllRides(),a=yield this.userService.getAllUsers(),l=i.filter(u=>{let v=new Date(u.created_at);return v>=e.startDate&&v<=e.endDate}),c=l.reduce((u,v)=>u+(v.amount||v.fare||0),0),f=this.groupRevenueByDay(l),S=l.filter(u=>u.payment_status==="pending").reduce((u,v)=>u+(v.amount||v.fare||0),0),k=l.filter(u=>u.payment_status==="paid").reduce((u,v)=>u+(v.amount||v.fare||0),0),O=l.filter(u=>u.payment_status==="failed").reduce((u,v)=>u+(v.amount||v.fare||0),0),B=l.filter(u=>u.payment_status==="refunded").reduce((u,v)=>u+(v.amount||v.fare||0),0),P=new Map;l.filter(u=>u.driver_id&&(u.payment_status==="paid"||u.status==="completed")).forEach(u=>{let v=u.driver_id,Fe=u.amount||u.fare||0;if(P.has(v)){let w=P.get(v);P.set(v,{revenue:w.revenue+Fe,rideCount:w.rideCount+1})}else P.set(v,{revenue:Fe,rideCount:1})});let Ae=Array.from(P.entries()).map(([u,{revenue:v,rideCount:Fe}])=>{let w=a.find(T=>T.id===u);return{driverId:u,driverName:w?.full_name||"Unknown Driver",revenue:v,rideCount:Fe}}).sort((u,v)=>v.revenue-u.revenue).slice(0,5);return{dateRange:e,totalRevenue:c,revenueByDay:f,revenueByStatus:{pending:S,paid:k,failed:O,refunded:B},topDriversByRevenue:Ae}})}generateUserActivityReport(t){return x(this,null,function*(){let{dateRange:e,userRole:i}=t,a=yield this.userService.getAllUsers(),l=yield this.rideService.getAllRides(),c=i?a.filter(w=>w.role===i):a,f=c.filter(w=>{let T=new Date(w.created_at);return T>=e.startDate&&T<=e.endDate}),S=f.filter(w=>w.role==="rider").length,k=f.filter(w=>w.role==="driver").length,O=new Set(l.filter(w=>{let T=new Date(w.created_at);return T>=e.startDate&&T<=e.endDate}).map(w=>w.rider_id)),B=new Set(l.filter(w=>{let T=new Date(w.created_at);return w.driver_id&&T>=e.startDate&&T<=e.endDate}).map(w=>w.driver_id)),P=c.filter(w=>w.role==="rider"&&O.has(w.id)).length,Ae=c.filter(w=>w.role==="driver"&&B.has(w.id)).length,u=this.groupUsersByDay(f,l,e),v=new Map;l.filter(w=>{let T=new Date(w.created_at);return T>=e.startDate&&T<=e.endDate}).forEach(w=>{let T=w.rider_id,mt=w.amount||w.fare||0;if(v.has(T)){let pt=v.get(T);v.set(T,{rideCount:pt.rideCount+1,totalSpent:pt.totalSpent+mt})}else v.set(T,{rideCount:1,totalSpent:mt})});let Fe=Array.from(v.entries()).map(([w,{rideCount:T,totalSpent:mt}])=>{let pt=a.find(kr=>kr.id===w);return{riderId:w,riderName:pt?.full_name||"Unknown Rider",rideCount:T,totalSpent:mt}}).sort((w,T)=>T.rideCount-w.rideCount).slice(0,5);return{dateRange:e,newUsers:{total:f.length,riders:S,drivers:k},activeUsers:{total:P+Ae,riders:P,drivers:Ae},usersByDay:u,topRiders:Fe}})}groupRidesByDay(t){let e=new Map;return t.forEach(i=>{let l=new Date(i.created_at).toISOString().split("T")[0];e.has(l)?e.set(l,e.get(l)+1):e.set(l,1)}),Array.from(e.entries()).map(([i,a])=>({date:i,count:a})).sort((i,a)=>i.date.localeCompare(a.date))}groupRevenueByDay(t){let e=new Map;return t.forEach(i=>{let l=new Date(i.created_at).toISOString().split("T")[0],c=i.amount||i.fare||0;e.has(l)?e.set(l,e.get(l)+c):e.set(l,c)}),Array.from(e.entries()).map(([i,a])=>({date:i,amount:a})).sort((i,a)=>i.date.localeCompare(a.date))}groupUsersByDay(t,e,i){let a=new Map,l=new Date(i.startDate);for(;l<=i.endDate;){let c=l.toISOString().split("T")[0];a.set(c,{newUsers:0,activeUsers:0}),l.setDate(l.getDate()+1)}return t.forEach(c=>{let S=new Date(c.created_at).toISOString().split("T")[0];if(a.has(S)){let k=a.get(S);a.set(S,nt(at({},k),{newUsers:k.newUsers+1}))}}),e.forEach(c=>{let S=new Date(c.created_at).toISOString().split("T")[0];if(a.has(S)){let k=a.get(S);a.set(S,nt(at({},k),{activeUsers:k.activeUsers+1}))}}),Array.from(a.entries()).map(([c,{newUsers:f,activeUsers:S}])=>({date:c,newUsers:f,activeUsers:S})).sort((c,f)=>c.date.localeCompare(f.date))}exportReportToCsv(t,e){let i="";switch(t){case"rides":i=this.exportRideReportToCsv(e);break;case"revenue":i=this.exportRevenueReportToCsv(e);break;case"users":i=this.exportUserReportToCsv(e);break}return i}exportRideReportToCsv(t){let e=`Report Type,Ride Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}

`,e+=`Rides by Status
`,e+=`Status,Count
`,e+=`Requested,${t.ridesByStatus.requested}
`,e+=`Assigned,${t.ridesByStatus.assigned}
`,e+=`In Progress,${t.ridesByStatus.inProgress}
`,e+=`Completed,${t.ridesByStatus.completed}
`,e+=`Canceled,${t.ridesByStatus.canceled}

`,e+=`Average Metrics
`,e+=`Average Duration (minutes),${t.averageDuration.toFixed(2)}
`,e+=`Average Distance (miles),${t.averageDistance.toFixed(2)}

`,e+=`Rides by Day
`,e+=`Date,Count
`,t.ridesByDay.forEach(i=>{e+=`${i.date},${i.count}
`}),e}exportRevenueReportToCsv(t){let e=`Report Type,Revenue Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}
`,e+=`Total Revenue,$${t.totalRevenue.toFixed(2)}

`,e+=`Revenue by Payment Status
`,e+=`Status,Amount
`,e+=`Pending,$${t.revenueByStatus.pending.toFixed(2)}
`,e+=`Paid,$${t.revenueByStatus.paid.toFixed(2)}
`,e+=`Failed,$${t.revenueByStatus.failed.toFixed(2)}
`,e+=`Refunded,$${t.revenueByStatus.refunded.toFixed(2)}

`,e+=`Top Drivers by Revenue
`,e+=`Driver,Revenue,Ride Count
`,t.topDriversByRevenue.forEach(i=>{e+=`${i.driverName},$${i.revenue.toFixed(2)},${i.rideCount}
`}),e+=`
`,e+=`Revenue by Day
`,e+=`Date,Amount
`,t.revenueByDay.forEach(i=>{e+=`${i.date},$${i.amount.toFixed(2)}
`}),e}exportUserReportToCsv(t){let e=`Report Type,User Activity Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}

`,e+=`New Users
`,e+=`Type,Count
`,e+=`Total,${t.newUsers.total}
`,e+=`Riders,${t.newUsers.riders}
`,e+=`Drivers,${t.newUsers.drivers}

`,e+=`Active Users
`,e+=`Type,Count
`,e+=`Total,${t.activeUsers.total}
`,e+=`Riders,${t.activeUsers.riders}
`,e+=`Drivers,${t.activeUsers.drivers}

`,e+=`Top Riders
`,e+=`Rider,Ride Count,Total Spent
`,t.topRiders.forEach(i=>{e+=`${i.riderName},${i.rideCount},$${i.totalSpent.toFixed(2)}
`}),e+=`
`,e+=`User Activity by Day
`,e+=`Date,New Users,Active Users
`,t.usersByDay.forEach(i=>{e+=`${i.date},${i.newUsers},${i.activeUsers}
`}),e}static \u0275fac=function(e){return new(e||r)(gt(re),gt(Se),gt(Ke))};static \u0275prov=Oe({token:r,factory:r.\u0275fac,providedIn:"root"})};var Za=["userSort"],Ka=["userPaginator"],Ja=["rideSort"],en=["ridePaginator"],Rr=()=>[5,10,25,100];function tn(r,t){if(r&1&&(n(0,"div",41)(1,"mat-grid-list",42)(2,"mat-grid-tile")(3,"mat-card",43)(4,"mat-card-header")(5,"mat-card-title"),s(6,"User Statistics"),o()(),n(7,"mat-card-content")(8,"div",44)(9,"div",45)(10,"div",46),s(11),o(),n(12,"div",47),s(13,"Total Users"),o()(),n(14,"div",45)(15,"div",46),s(16),o(),n(17,"div",47),s(18,"Riders"),o()(),n(19,"div",45)(20,"div",46),s(21),o(),n(22,"div",47),s(23,"Drivers"),o()(),n(24,"div",45)(25,"div",46),s(26),o(),n(27,"div",47),s(28,"Admins"),o()()()()()(),n(29,"mat-grid-tile")(30,"mat-card",43)(31,"mat-card-header")(32,"mat-card-title"),s(33,"Ride Statistics"),o()(),n(34,"mat-card-content")(35,"div",44)(36,"div",45)(37,"div",46),s(38),o(),n(39,"div",47),s(40,"Total Rides"),o()(),n(41,"div",45)(42,"div",46),s(43),o(),n(44,"div",47),s(45,"Requested"),o()(),n(46,"div",45)(47,"div",46),s(48),o(),n(49,"div",47),s(50,"In Progress"),o()(),n(51,"div",45)(52,"div",46),s(53),o(),n(54,"div",47),s(55,"Completed"),o()()()()()()()()),r&2){let e=g();d(11),M(e.statistics.totalUsers.all),d(5),M(e.statistics.totalUsers.riders),d(5),M(e.statistics.totalUsers.drivers),d(5),M(e.statistics.totalUsers.admins),d(12),M(e.statistics.rides.total),d(5),M(e.statistics.rides.requested),d(5),M(e.statistics.rides.inProgress),d(5),M(e.statistics.rides.completed)}}function rn(r,t){r&1&&(n(0,"div",48),h(1,"mat-spinner",49),n(2,"p"),s(3,"Loading statistics..."),o()())}function an(r,t){r&1&&(n(0,"th",63),s(1,"Email"),o())}function nn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;d(),M(e.email)}}function on(r,t){r&1&&(n(0,"th",63),s(1,"Name"),o())}function sn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;d(),M(e.full_name||"N/A")}}function ln(r,t){r&1&&(n(0,"th",63),s(1,"Role"),o())}function dn(r,t){if(r&1&&(n(0,"td",64)(1,"mat-chip",65),s(2),o()()),r&2){let e=t.$implicit,i=g(2);d(),m("color",e.role==="admin"?"warn":"primary"),d(),D(" ",i.getRoleDisplayName(e.role)," ")}}function cn(r,t){r&1&&(n(0,"th",63),s(1,"Registered"),o())}function mn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);d(),M(i.formatDate(e.created_at))}}function pn(r,t){r&1&&(n(0,"th",66),s(1,"Status"),o())}function un(r,t){if(r&1&&(n(0,"td",64)(1,"mat-chip",65),s(2),o()()),r&2){let e=t.$implicit;d(),m("color",e.is_approved?"accent":"warn"),d(),D(" ",e.is_approved?"Active":e.role==="driver"?"Pending Approval":"Inactive"," ")}}function gn(r,t){r&1&&(n(0,"th",66),s(1,"Actions"),o())}function hn(r,t){if(r&1){let e=I();n(0,"button",69),_("click",function(){y(e);let a=g().$implicit,l=g(2);return b(l.approveDriver(a.id))}),n(1,"mat-icon"),s(2,"check_circle"),o()()}}function fn(r,t){if(r&1){let e=I();n(0,"td",64),p(1,hn,3,0,"button",67),n(2,"button",68),_("click",function(){let a=y(e).$implicit,l=g(2);return b(l.openUserDetails(a))}),n(3,"mat-icon"),s(4,"visibility"),o()()()}if(r&2){let e=t.$implicit;d(),m("ngIf",e.role==="driver"&&!e.is_approved)}}function _n(r,t){r&1&&h(0,"tr",70)}function vn(r,t){r&1&&h(0,"tr",71)}function yn(r,t){if(r&1&&(n(0,"div")(1,"table",50,3),E(3,51),p(4,an,2,0,"th",52)(5,nn,2,1,"td",53),A(),E(6,54),p(7,on,2,0,"th",52)(8,sn,2,1,"td",53),A(),E(9,55),p(10,ln,2,0,"th",52)(11,dn,3,2,"td",53),A(),E(12,56),p(13,cn,2,0,"th",52)(14,mn,2,1,"td",53),A(),E(15,57),p(16,pn,2,0,"th",58)(17,un,3,2,"td",53),A(),E(18,59),p(19,gn,2,0,"th",58)(20,fn,5,1,"td",53),A(),p(21,_n,1,0,"tr",60)(22,vn,1,0,"tr",61),o(),h(23,"mat-paginator",62,4),o()),r&2){let e=g();d(),m("dataSource",e.userDataSource),d(20),m("matHeaderRowDef",e.userDisplayedColumns),d(),m("matRowDefColumns",e.userDisplayedColumns),d(),m("pageSizeOptions",ni(5,Rr))("pageSize",10)}}function bn(r,t){r&1&&(n(0,"div",48),h(1,"mat-spinner",49),n(2,"p"),s(3,"Loading users..."),o()())}function Sn(r,t){r&1&&(n(0,"th",63),s(1,"Rider"),o())}function wn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);d(),M(i.getUserName(e.rider_id))}}function Cn(r,t){r&1&&(n(0,"th",63),s(1,"Driver"),o())}function xn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);d(),M(e.driver_id?i.getUserName(e.driver_id):"Not Assigned")}}function Mn(r,t){r&1&&(n(0,"th",63),s(1,"Pickup"),o())}function Dn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;d(),M(e.pickup_location)}}function Rn(r,t){r&1&&(n(0,"th",63),s(1,"Dropoff"),o())}function kn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;d(),M(e.dropoff_location)}}function In(r,t){r&1&&(n(0,"th",63),s(1,"Price"),o())}function Pn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;d(),D("$",e.fare||"N/A","")}}function Tn(r,t){r&1&&(n(0,"th",63),s(1,"Status"),o())}function En(r,t){if(r&1&&(n(0,"td",64)(1,"mat-chip",65),s(2),o()()),r&2){let e=t.$implicit,i=g(2);d(),m("color",i.getStatusColor(e.status)),d(),D(" ",i.getStatusDisplayName(e.status)," ")}}function An(r,t){r&1&&(n(0,"th",63),s(1,"Created"),o())}function Fn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);d(),M(i.formatDate(e.created_at))}}function On(r,t){r&1&&(n(0,"th",66),s(1,"Actions"),o())}function zn(r,t){if(r&1){let e=I();n(0,"button",83),_("click",function(){y(e);let a=g().$implicit,l=g(2);return b(l.openDriverSelectionDialog(a.id))}),n(1,"mat-icon"),s(2,"person_add"),o()()}}function Bn(r,t){if(r&1){let e=I();n(0,"button",84),_("click",function(){y(e);let a=g().$implicit,l=g(2);return b(l.openDriverSelectionDialog(a.id))}),n(1,"mat-icon"),s(2,"swap_horiz"),o()()}}function Ln(r,t){if(r&1){let e=I();n(0,"button",85),_("click",function(){y(e);let a=g().$implicit,l=g(2);return b(l.updateRideStatus(a.id,"canceled"))}),n(1,"mat-icon"),s(2,"cancel"),o()()}}function Nn(r,t){if(r&1){let e=I();n(0,"button",86),_("click",function(){y(e);let a=g().$implicit,l=g(2);return b(l.updateRideStatus(a.id,"in-progress"))}),n(1,"mat-icon"),s(2,"play_arrow"),o()()}}function Un(r,t){if(r&1){let e=I();n(0,"button",87),_("click",function(){y(e);let a=g().$implicit,l=g(2);return b(l.updateRideStatus(a.id,"completed"))}),n(1,"mat-icon"),s(2,"check_circle"),o()()}}function Vn(r,t){if(r&1){let e=I();n(0,"td",64),p(1,zn,3,0,"button",78)(2,Bn,3,0,"button",79)(3,Ln,3,0,"button",80)(4,Nn,3,0,"button",81)(5,Un,3,0,"button",82),n(6,"button",68),_("click",function(){let a=y(e).$implicit,l=g(2);return b(l.viewRideDetails(a.id))}),n(7,"mat-icon"),s(8,"visibility"),o()()()}if(r&2){let e=t.$implicit;d(),m("ngIf",e.status==="requested"),d(),m("ngIf",e.status==="assigned"),d(),m("ngIf",e.status==="requested"||e.status==="assigned"),d(),m("ngIf",e.status==="assigned"),d(),m("ngIf",e.status==="in-progress")}}function $n(r,t){r&1&&h(0,"tr",70)}function jn(r,t){r&1&&h(0,"tr",71)}function Hn(r,t){if(r&1&&(n(0,"div")(1,"table",50,5),E(3,72),p(4,Sn,2,0,"th",52)(5,wn,2,1,"td",53),A(),E(6,73),p(7,Cn,2,0,"th",52)(8,xn,2,1,"td",53),A(),E(9,74),p(10,Mn,2,0,"th",52)(11,Dn,2,1,"td",53),A(),E(12,75),p(13,Rn,2,0,"th",52)(14,kn,2,1,"td",53),A(),E(15,76),p(16,In,2,0,"th",52)(17,Pn,2,1,"td",53),A(),E(18,57),p(19,Tn,2,0,"th",52)(20,En,3,2,"td",53),A(),E(21,56),p(22,An,2,0,"th",52)(23,Fn,2,1,"td",53),A(),E(24,59),p(25,On,2,0,"th",58)(26,Vn,9,5,"td",53),A(),p(27,$n,1,0,"tr",60)(28,jn,1,0,"tr",61),o(),h(29,"mat-paginator",77,6),o()),r&2){let e=g();d(),m("dataSource",e.rideDataSource),d(26),m("matHeaderRowDef",e.rideDisplayedColumns),d(),m("matRowDefColumns",e.rideDisplayedColumns),d(),m("length",e.rides.length)("pageSizeOptions",ni(6,Rr))("pageSize",5)}}function qn(r,t){r&1&&(n(0,"div",48),h(1,"mat-spinner",49),n(2,"p"),s(3,"Loading rides..."),o()())}function Gn(r,t){if(r&1){let e=I();n(0,"div",88)(1,"app-ride-detail",89),_("rideUpdated",function(a){y(e);let l=g();return b(l.onRideUpdated(a))}),o()()}if(r&2){let e=g();d(),m("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}function Qn(r,t){if(r&1){let e=I();n(0,"div",90),_("click",function(){y(e);let a=g();return b(a.closeRideDetails())}),n(1,"mat-icon"),s(2,"close"),o()()}}var Dr=class r{constructor(t,e,i,a,l){this.userService=t;this.rideService=e;this.statisticsService=i;this.snackBar=a;this.dialog=l}users=[];userDataSource=new li([]);userDisplayedColumns=["email","full_name","role","created_at","status","actions"];userRoleFilter="";userSearchTerm="";loadingUsers=!1;rides=[];rideDataSource=new li([]);rideDisplayedColumns=["rider_id","driver_id","pickup_location","dropoff_location","status","created_at","actions"];rideStatusFilter="";rideSearchTerm="";loadingRides=!1;selectedRideId=null;lastRideRefreshTime=new Date;statistics=null;loadingStatistics=!1;userNameCache={};userNameLoadingCache={};refreshRidesInterval;ridesSubscription=null;userSort;userPaginator;rideSort;ridePaginator;dataLoadingComplete=!1;ngOnInit(){return x(this,null,function*(){console.log("Admin Component ngOnInit");try{yield Promise.all([this.loadUsers(),this.initialLoadRides(),this.loadStatistics()]),this.dataLoadingComplete=!0,this.userPaginator&&this.ridePaginator&&this.setupPaginators(),this.setupRideSubscription()}catch(t){console.error("Error loading data:",t),this.snackBar.open("Failed to load data","Close",{duration:3e3})}})}initialLoadRides(){return x(this,null,function*(){this.loadingRides=!0;try{this.rides=yield this.rideService.getAllRides(),this.rideDataSource.data=this.rides,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.lastRideRefreshTime=new Date}catch(t){throw console.error("Error loading rides:",t),t}finally{this.loadingRides=!1}this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator})}setupRideSubscription(){this.ridesSubscription&&this.ridesSubscription.unsubscribe(),this.ridesSubscription=this.rideService.rides$.subscribe(t=>{this.rideDataSource.data=t,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.hasRidesChanged(this.rides,t)&&(this.lastRideRefreshTime=new Date)})}getChangedRides(t,e){let i={hasChanges:!1,changedRides:[],addedRides:[],removedRideIds:[]},a=new Map,l=new Map;t.forEach(c=>a.set(c.id,c)),e.forEach(c=>l.set(c.id,c));for(let c of e)a.has(c.id)?a.get(c.id).updated_at!==c.updated_at&&(i.changedRides.push(c),i.hasChanges=!0):(i.addedRides.push(c),i.hasChanges=!0);for(let c of t)l.has(c.id)||(i.removedRideIds.push(c.id),i.hasChanges=!0);return i}hasRidesChanged(t,e){return this.getChangedRides(t,e).hasChanges}updateChangedRidesOnly(t){let e=this.getChangedRides(this.rides,t);if(!e.hasChanges)return;let i=[...this.rideDataSource.data];if(e.removedRideIds.length>0){let a=i.filter(l=>!e.removedRideIds.includes(l.id));this.rides=a,this.rideDataSource.data=a,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator;return}if(e.addedRides.length>0){let a=[...e.addedRides,...i];this.rides=a,this.rideDataSource.data=a,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator;return}if(e.changedRides.length>0){let a=new Map;i.forEach((l,c)=>{a.set(l.id,c)}),e.changedRides.forEach(l=>{let c=a.get(l.id);if(c!==void 0){i[c]=l;let f=this.rides.findIndex(S=>S.id===l.id);f!==-1&&(this.rides[f]=l)}}),this.rideDataSource.data=i,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator}}ngAfterViewInit(){this.dataLoadingComplete&&this.setupPaginators()}setupPaginators(){this.userDataSource.sort=this.userSort,this.userDataSource.paginator=this.userPaginator,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,this.userDataSource.filterPredicate=(t,e)=>{let i=JSON.parse(e),a=!0,l=!0;if(i.role&&(a=t.role===i.role),i.searchTerm){let c=i.searchTerm.toLowerCase(),f=t.email.toLowerCase().includes(c),S=t.full_name?t.full_name.toLowerCase().includes(c):!1;l=f||S}return a&&l},this.userDataSource.sortingDataAccessor=(t,e)=>{switch(e){case"created_at":return new Date(t.created_at).getTime();case"full_name":return t.full_name?.toLowerCase()||"";default:return t[e]||""}},this.rideDataSource.filterPredicate=(t,e)=>{let i=JSON.parse(e),a=!0,l=!0;if(i.status&&(a=t.status===i.status),i.searchTerm){let c=i.searchTerm.toLowerCase(),f=t.pickup_location.toLowerCase().includes(c),S=t.dropoff_location.toLowerCase().includes(c),k=(this.getUserName(t.rider_id)||"").toLowerCase().includes(c);l=f||S||k}return a&&l},this.rideDataSource.sortingDataAccessor=(t,e)=>{switch(e){case"rider_id":return this.getUserName(t.rider_id).toLowerCase();case"driver_id":return t.driver_id?this.getUserName(t.driver_id).toLowerCase():"zzz";case"created_at":case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;default:return t[e]||""}}}loadUsers(){return x(this,null,function*(){this.loadingUsers=!0;try{this.users=yield this.userService.getAllUsers(),this.userDataSource.data=this.users,this.userDataSource.sort=this.userSort,this.userDataSource.paginator=this.userPaginator,(this.userRoleFilter||this.userSearchTerm)&&this.applyUserFilters()}catch(t){console.error("Error loading users:",t),this.snackBar.open("Failed to load users","Close",{duration:3e3})}finally{this.loadingUsers=!1}})}applyUserFilters(){let t=JSON.stringify({role:this.userRoleFilter,searchTerm:this.userSearchTerm});this.userDataSource.filter=t,this.userDataSource.paginator&&this.dataLoadingComplete&&this.userDataSource.paginator.firstPage()}getRoleDisplayName(t){return t.charAt(0).toUpperCase()+t.slice(1)}approveDriver(t){return x(this,null,function*(){try{if(yield this.userService.approveDriver(t))this.snackBar.open("Driver approved successfully","Close",{duration:3e3});else throw new Error("Failed to approve driver")}catch(e){console.error("Error approving driver:",e),this.snackBar.open("Failed to approve driver","Close",{duration:3e3})}})}openUserDetails(t){this.dialog.open(ei,{width:"500px",data:t})}getStatusDisplay(t){return t.role==="driver"?t.is_approved?"Approved":"Pending Approval":t.role==="admin"?t.is_approved?"Active":"Inactive":"Active"}loadRides(){return x(this,null,function*(){this.loadingRides=!0;try{let t=yield this.rideService.getAllRides();this.updateChangedRidesOnly(t),(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.ridePaginator&&this.dataLoadingComplete&&(this.ridePaginator.length=this.rides.length),this.lastRideRefreshTime=new Date,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator}catch(t){console.error("Error loading rides:",t),this.snackBar.open("Failed to load rides","Close",{duration:3e3})}finally{this.loadingRides=!1}})}applyRideFilters(){let t=JSON.stringify({status:this.rideStatusFilter,searchTerm:this.rideSearchTerm});this.rideDataSource.filter=t,this.rideDataSource.paginator&&this.dataLoadingComplete&&this.rideDataSource.paginator.firstPage()}updateRideStatus(t,e){return x(this,null,function*(){try{if(yield this.rideService.updateRideStatus(t,e))this.snackBar.open(`Ride status updated to ${this.getStatusDisplayName(e)}`,"Close",{duration:3e3});else throw new Error("Failed to update ride status")}catch(i){console.error("Error updating ride status:",i),this.snackBar.open("Failed to update ride status","Close",{duration:3e3})}})}openDriverSelectionDialog(t){this.dialog.open(Qt,{width:"500px",data:{drivers:this.users.filter(i=>i.role==="driver"&&i.is_approved)}}).afterClosed().subscribe(i=>x(this,null,function*(){if(i)try{if(yield this.rideService.assignRideToDriver(t,i))this.snackBar.open("Driver assigned successfully","Close",{duration:3e3});else throw new Error("Failed to assign driver")}catch(a){console.error("Error assigning driver:",a),this.snackBar.open("Failed to assign driver","Close",{duration:3e3})}}))}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null}onRideUpdated(t){let e=[...this.rideDataSource.data],i=e.findIndex(a=>a.id===t.id);if(i!==-1){e[i]=t,this.rideDataSource.data=e,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator;let a=this.rides.findIndex(l=>l.id===t.id);a!==-1&&(this.rides[a]=t),this.lastRideRefreshTime=new Date}}openCreateRideDialog(){this.dialog.open(ri,{width:"600px"}).afterClosed().subscribe(e=>{e&&this.snackBar.open("Ride created successfully","Close",{duration:3e3})})}loadStatistics(){return x(this,null,function*(){this.loadingStatistics=!0;try{this.statistics=yield this.statisticsService.generateSystemStatistics()}catch(t){console.error("Error loading statistics:",t),this.snackBar.open("Failed to load statistics","Close",{duration:3e3})}finally{this.loadingStatistics=!1}})}getStatusDisplayName(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}getStatusColor(t){return{requested:"warn",assigned:"primary","in-progress":"accent",completed:"primary",canceled:"warn"}[t]||"primary"}formatDate(t){return new Date(t).toLocaleString()}getUserName(t){return t&&(this.users.find(i=>i.id===t)?.full_name||t)||"N/A"}ngOnDestroy(){this.refreshRidesInterval&&clearInterval(this.refreshRidesInterval),this.ridesSubscription&&this.ridesSubscription.unsubscribe()}static \u0275fac=function(e){return new(e||r)(C(re),C(Se),C(ai),C(j),C(zi))};static \u0275cmp=F({type:r,selectors:[["app-admin"]],viewQuery:function(e,i){if(e&1&&(oe(Za,5),oe(Ka,5),oe(Ja,5),oe(en,5)),e&2){let a;q(a=G())&&(i.userSort=a.first),q(a=G())&&(i.userPaginator=a.first),q(a=G())&&(i.rideSort=a.first),q(a=G())&&(i.ridePaginator=a.first)}},decls:86,vars:13,consts:[["loadingStats",""],["loadingUsersTemplate",""],["loadingRidesTemplate",""],["userSort","matSort"],["userPaginator",""],["rideSort","matSort"],["ridePaginator",""],[1,"dashboard-container"],[1,"dashboard-title"],["animationDuration","300ms"],["label","Dashboard Overview"],[1,"tab-content"],["class","stats-container",4,"ngIf","ngIfElse"],["label","User Management"],[1,"filters-container"],["appearance","outline"],[3,"ngModelChange","selectionChange","ngModel"],["value",""],["value","rider"],["value","driver"],["value","admin"],["matInput","","placeholder","Search by name or email",3,"ngModelChange","keyup","ngModel"],["matSuffix",""],[4,"ngIf","ngIfElse"],["label","Ride Management"],[1,"refresh-info"],[1,"last-refresh"],["mat-icon-button","","color","primary","matTooltip","Refresh rides manually",3,"click"],[1,"auto-refresh-note"],[1,"action-buttons"],["mat-raised-button","","color","primary",3,"click"],["value","requested"],["value","assigned"],["value","in-progress"],["value","completed"],["value","canceled"],["matInput","","placeholder","Search by location or name",3,"ngModelChange","keyup","ngModel"],["label","Stripe Payment Processing"],["label","Ride Pricing"],["class","ride-detail-overlay",4,"ngIf"],["class","close-overlay",3,"click",4,"ngIf"],[1,"stats-container"],["cols","2","rowHeight","250px","gutterSize","16px"],[1,"stats-card"],[1,"stats-grid"],[1,"stat-item"],[1,"stat-value"],[1,"stat-label"],[1,"loading-container"],["diameter","50"],["mat-table","","matSort","",1,"mat-elevation-z2","full-width",3,"dataSource"],["matColumnDef","email"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","full_name"],["matColumnDef","role"],["matColumnDef","created_at"],["matColumnDef","status"],["mat-header-cell","",4,"matHeaderCellDef"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["showFirstLastButtons","","aria-label","Select page of users",3,"pageSizeOptions","pageSize"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["selected","",3,"color"],["mat-header-cell",""],["mat-icon-button","","color","primary","matTooltip","Approve Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","View Details",3,"click"],["mat-icon-button","","color","primary","matTooltip","Approve Driver",3,"click"],["mat-header-row",""],["mat-row",""],["matColumnDef","rider_id"],["matColumnDef","driver_id"],["matColumnDef","pickup_location"],["matColumnDef","dropoff_location"],["matColumnDef","price"],["showFirstLastButtons","","aria-label","Select page of rides",3,"length","pageSizeOptions","pageSize"],["mat-icon-button","","color","primary","matTooltip","Assign Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Reassign Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Cancel Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Start Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Complete Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Assign Driver",3,"click"],["mat-icon-button","","color","primary","matTooltip","Reassign Driver",3,"click"],["mat-icon-button","","color","accent","matTooltip","Cancel Ride",3,"click"],["mat-icon-button","","color","warn","matTooltip","Start Ride",3,"click"],["mat-icon-button","","color","primary","matTooltip","Complete Ride",3,"click"],[1,"ride-detail-overlay"],[3,"rideUpdated","rideId","onClose"],[1,"close-overlay",3,"click"]],template:function(e,i){if(e&1){let a=I();n(0,"div",7)(1,"h1",8),s(2,"Admin Dashboard"),o(),n(3,"mat-tab-group",9)(4,"mat-tab",10)(5,"div",11),p(6,tn,56,8,"div",12)(7,rn,4,0,"ng-template",null,0,St),o()(),n(9,"mat-tab",13)(10,"div",11)(11,"mat-card")(12,"mat-card-header")(13,"mat-card-title"),s(14,"User Management"),o()(),n(15,"mat-card-content")(16,"div",14)(17,"mat-form-field",15)(18,"mat-select",16),Pe("ngModelChange",function(c){return y(a),Ie(i.userRoleFilter,c)||(i.userRoleFilter=c),b(c)}),_("selectionChange",function(){return y(a),b(i.applyUserFilters())}),n(19,"mat-option",17),s(20,"All Roles"),o(),n(21,"mat-option",18),s(22,"Rider"),o(),n(23,"mat-option",19),s(24,"Driver"),o(),n(25,"mat-option",20),s(26,"Admin"),o()()(),n(27,"mat-form-field",15)(28,"mat-label"),s(29,"Search"),o(),n(30,"input",21),Pe("ngModelChange",function(c){return y(a),Ie(i.userSearchTerm,c)||(i.userSearchTerm=c),b(c)}),_("keyup",function(){return y(a),b(i.applyUserFilters())}),o(),n(31,"mat-icon",22),s(32,"search"),o()()(),p(33,yn,25,6,"div",23)(34,bn,4,0,"ng-template",null,1,St),o()()()(),n(36,"mat-tab",24)(37,"div",11)(38,"mat-card")(39,"mat-card-header")(40,"mat-card-title"),s(41,"Ride Management"),o(),n(42,"div",25)(43,"span",26),s(44),o(),n(45,"button",27),_("click",function(){return y(a),b(i.loadRides())}),n(46,"mat-icon"),s(47,"refresh"),o()(),n(48,"span",28),s(49,"(Real-time updates)"),o()(),n(50,"div",29)(51,"button",30),_("click",function(){return y(a),b(i.openCreateRideDialog())}),n(52,"mat-icon"),s(53,"add"),o(),s(54," Create Ride "),o()()(),n(55,"mat-card-content")(56,"div",14)(57,"mat-form-field",15)(58,"mat-select",16),Pe("ngModelChange",function(c){return y(a),Ie(i.rideStatusFilter,c)||(i.rideStatusFilter=c),b(c)}),_("selectionChange",function(){return y(a),b(i.applyRideFilters())}),n(59,"mat-option",17),s(60,"All Statuses"),o(),n(61,"mat-option",31),s(62,"Requested"),o(),n(63,"mat-option",32),s(64,"Assigned"),o(),n(65,"mat-option",33),s(66,"In Progress"),o(),n(67,"mat-option",34),s(68,"Completed"),o(),n(69,"mat-option",35),s(70,"Canceled"),o()()(),n(71,"mat-form-field",15)(72,"input",36),Pe("ngModelChange",function(c){return y(a),Ie(i.rideSearchTerm,c)||(i.rideSearchTerm=c),b(c)}),_("keyup",function(){return y(a),b(i.applyRideFilters())}),o(),n(73,"mat-icon",22),s(74,"search"),o()()(),p(75,Hn,31,7,"div",23)(76,qn,4,0,"ng-template",null,2,St),o()()()(),n(78,"mat-tab",37)(79,"div",11),h(80,"app-stripe-payment"),o()(),n(81,"mat-tab",38)(82,"div",11),h(83,"app-ride-pricing"),o()()()(),p(84,Gn,2,2,"div",39)(85,Qn,3,0,"div",40)}if(e&2){let a=Q(8),l=Q(35),c=Q(77);d(6),m("ngIf",i.statistics)("ngIfElse",a),d(12),ke("ngModel",i.userRoleFilter),d(12),ke("ngModel",i.userSearchTerm),d(3),m("ngIf",!i.loadingUsers)("ngIfElse",l),d(11),D("Last refreshed: ",i.formatDate(i.lastRideRefreshTime.toISOString()),""),d(14),ke("ngModel",i.rideStatusFilter),d(14),ke("ngModel",i.rideSearchTerm),d(3),m("ngIf",!i.loadingRides)("ngIfElse",c),d(9),m("ngIf",i.selectedRideId),d(),m("ngIf",i.selectedRideId)}},dependencies:[V,Y,Z,ge,X,It,he,ie,We,Xe,Ze,Ye,lr,or,sr,et,Ft,zt,Ut,Bt,Ot,Vt,Lt,Nt,$t,jt,hr,di,gr,fr,ci,be,ye,H,K,Tt,J,ee,_e,fe,U,$,Be,te,ve,pe,Ce,we,At,Et,mr,pr,Sr,br,hi,it,tt,ue,cr,ti,ii],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.dashboard-title[_ngcontent-%COMP%]{margin-bottom:20px;color:#3f51b5;font-weight:500}.tab-content[_ngcontent-%COMP%]{padding:20px 0;overflow-y:auto}.filters-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:20px;align-items:center}.mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-top:20px}table[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#0000008a}.stats-container[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.stats-card[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column}.full-width[_ngcontent-%COMP%]{width:100%}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px;margin-top:16px;padding:16px}.scrollable-content[_ngcontent-%COMP%]{overflow-y:auto;max-height:calc(100% - 60px);padding-right:8px}mat-card-content[_ngcontent-%COMP%]{overflow-y:auto;flex:1}.stat-item[_ngcontent-%COMP%]{text-align:center;padding:10px;border-radius:4px;background-color:#3f51b51a}.stat-value[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#1976d2}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#0000008a;margin-top:4px}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto}@media (max-width: 768px){.filters-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.mat-form-field[_ngcontent-%COMP%]{width:100%}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.mat-grid-tile[_ngcontent-%COMP%]{min-height:260px}.refresh-info[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:auto;font-size:14px;color:#0009}.last-refresh[_ngcontent-%COMP%]{margin-right:8px}.auto-refresh-note[_ngcontent-%COMP%]{margin-left:8px;font-style:italic;font-size:12px}.action-buttons[_ngcontent-%COMP%]{margin-left:20px;display:flex;align-items:center}.close-overlay[_ngcontent-%COMP%]{position:fixed;top:20px;right:20px;z-index:2000;cursor:pointer;background:#fff;border-radius:50%;box-shadow:0 2px 8px #00000026;padding:8px;display:flex;align-items:center;justify-content:center}"]})};export{Dr as AdminComponent};
