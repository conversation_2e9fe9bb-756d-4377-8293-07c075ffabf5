import{c as h}from"./chunk-H3RPDCYZ.js";import{f as g}from"./chunk-L5P5FOLI.js";var m=class{applyChanges(e,t,s,r,i){e.forEachOperation((o,c,a)=>{let l,n;if(o.previousIndex==null){let _=s(o,c,a);l=t.createEmbeddedView(_.templateRef,_.context,_.index),n=h.INSERTED}else a==null?(t.remove(c),n=h.REMOVED):(l=t.get(c),t.move(l,a),n=h.MOVED);i&&i({context:l?.context,operation:n,record:o})})}detach(){}};var d=class{_multiple;_emitChanges;compareWith;_selection=new Set;_deselectedToEmit=[];_selectedToEmit=[];_selected;get selected(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected}changed=new g;constructor(e=!1,t,s=!0,r){this._multiple=e,this._emitChanges=s,this.compareWith=r,t&&t.length&&(e?t.forEach(i=>this._markSelected(i)):this._markSelected(t[0]),this._selectedToEmit.length=0)}select(...e){this._verifyValueAssignment(e),e.forEach(s=>this._markSelected(s));let t=this._hasQueuedChanges();return this._emitChangeEvent(),t}deselect(...e){this._verifyValueAssignment(e),e.forEach(s=>this._unmarkSelected(s));let t=this._hasQueuedChanges();return this._emitChangeEvent(),t}setSelection(...e){this._verifyValueAssignment(e);let t=this.selected,s=new Set(e.map(i=>this._getConcreteValue(i)));e.forEach(i=>this._markSelected(i)),t.filter(i=>!s.has(this._getConcreteValue(i,s))).forEach(i=>this._unmarkSelected(i));let r=this._hasQueuedChanges();return this._emitChangeEvent(),r}toggle(e){return this.isSelected(e)?this.deselect(e):this.select(e)}clear(e=!0){this._unmarkAll();let t=this._hasQueuedChanges();return e&&this._emitChangeEvent(),t}isSelected(e){return this._selection.has(this._getConcreteValue(e))}isEmpty(){return this._selection.size===0}hasValue(){return!this.isEmpty()}sort(e){this._multiple&&this.selected&&this._selected.sort(e)}isMultipleSelection(){return this._multiple}_emitChangeEvent(){this._selected=null,(this._selectedToEmit.length||this._deselectedToEmit.length)&&(this.changed.next({source:this,added:this._selectedToEmit,removed:this._deselectedToEmit}),this._deselectedToEmit=[],this._selectedToEmit=[])}_markSelected(e){e=this._getConcreteValue(e),this.isSelected(e)||(this._multiple||this._unmarkAll(),this.isSelected(e)||this._selection.add(e),this._emitChanges&&this._selectedToEmit.push(e))}_unmarkSelected(e){e=this._getConcreteValue(e),this.isSelected(e)&&(this._selection.delete(e),this._emitChanges&&this._deselectedToEmit.push(e))}_unmarkAll(){this.isEmpty()||this._selection.forEach(e=>this._unmarkSelected(e))}_verifyValueAssignment(e){e.length>1&&this._multiple}_hasQueuedChanges(){return!!(this._deselectedToEmit.length||this._selectedToEmit.length)}_getConcreteValue(e,t){if(this.compareWith){t=t??this._selection;for(let s of t)if(this.compareWith(e,s))return s;return e}else return e}};export{m as a,d as b};
