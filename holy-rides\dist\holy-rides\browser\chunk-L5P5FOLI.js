import{a as k,b as ae}from"./chunk-ODN5LVDJ.js";function It(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ct=It(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function T(e){return typeof e=="function"}var Do=It(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Bt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Y=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(T(r))try{r()}catch(i){t=i instanceof Do?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{zu(i)}catch(s){t=t??[],s instanceof Do?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Do(t)}}add(t){var n;if(t&&t!==this)if(this.closed)zu(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Bt(n,t)}remove(t){let{_finalizers:n}=this;n&&Bt(n,t),t instanceof e&&t._removeParent(this)}};Y.EMPTY=(()=>{let e=new Y;return e.closed=!0,e})();var Ys=Y.EMPTY;function _o(e){return e instanceof Y||e&&"closed"in e&&T(e.remove)&&T(e.add)&&T(e.unsubscribe)}function zu(e){T(e)?e():e.unsubscribe()}var Le={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Dn={setTimeout(e,t,...n){let{delegate:r}=Dn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Dn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Eo(e){Dn.setTimeout(()=>{let{onUnhandledError:t}=Le;if(t)t(e);else throw e})}function Vt(){}var Gu=Ks("C",void 0,void 0);function Wu(e){return Ks("E",void 0,e)}function qu(e){return Ks("N",e,void 0)}function Ks(e,t,n){return{kind:e,value:t,error:n}}var Ht=null;function _n(e){if(Le.useDeprecatedSynchronousErrorHandling){let t=!Ht;if(t&&(Ht={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Ht;if(Ht=null,n)throw r}}else e()}function Zu(e){Le.useDeprecatedSynchronousErrorHandling&&Ht&&(Ht.errorThrown=!0,Ht.error=e)}var Ut=class extends Y{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,_o(t)&&t.add(this)):this.destination=nb}static create(t,n,r){return new je(t,n,r)}next(t){this.isStopped?Xs(qu(t),this):this._next(t)}error(t){this.isStopped?Xs(Wu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Xs(Gu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},eb=Function.prototype.bind;function Qs(e,t){return eb.call(e,t)}var Js=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){wo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){wo(r)}else wo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){wo(n)}}},je=class extends Ut{constructor(t,n,r){super();let o;if(T(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Le.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Qs(t.next,i),error:t.error&&Qs(t.error,i),complete:t.complete&&Qs(t.complete,i)}):o=t}this.destination=new Js(o)}};function wo(e){Le.useDeprecatedSynchronousErrorHandling?Zu(e):Eo(e)}function tb(e){throw e}function Xs(e,t){let{onStoppedNotification:n}=Le;n&&Dn.setTimeout(()=>n(e,t))}var nb={closed:!0,next:Vt,error:tb,complete:Vt};function rb(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new je({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new ct)}});e.subscribe(i)})}function ea(e){return T(e?.lift)}function I(e){return t=>{if(ea(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function E(e,t,n,r,o){return new ta(e,t,n,r,o)}var ta=class extends Ut{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Me(e,t){return I((n,r)=>{let o=0;n.subscribe(E(r,i=>e.call(t,i,o++)&&r.next(i)))})}var En=typeof Symbol=="function"&&Symbol.observable||"@@observable";function me(e){return e}function ob(...e){return na(e)}function na(e){return e.length===0?me:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var N=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=sb(n)?n:new je(n,r,o);return _n(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Yu(r),new r((o,i)=>{let s=new je({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[En](){return this}pipe(...n){return na(n)(this)}toPromise(n){return n=Yu(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Yu(e){var t;return(t=e??Le.Promise)!==null&&t!==void 0?t:Promise}function ib(e){return e&&T(e.next)&&T(e.error)&&T(e.complete)}function sb(e){return e&&e instanceof Ut||ib(e)&&_o(e)}function ra(){return I((e,t)=>{let n=null;e._refCount++;let r=E(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var oa=class extends N{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,ea(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Y;let n=this.getSubject();t.add(this.source.subscribe(E(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Y.EMPTY)}return t}refCount(){return ra()(this)}};var Ku=It(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var B=(()=>{class e extends N{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Io(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Ku}next(n){_n(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){_n(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){_n(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ys:(this.currentObservers=null,i.push(n),new Y(()=>{this.currentObservers=null,Bt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new N;return n.source=this,n}}return e.create=(t,n)=>new Io(t,n),e})(),Io=class extends B{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Ys}};var $t=class extends B{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var cr={now(){return(cr.delegate||Date).now()},delegate:void 0};var lr=class extends B{constructor(t=1/0,n=1/0,r=cr){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Co=class extends Y{constructor(t,n){super()}schedule(t,n=0){return this}};var ur={setInterval(e,t,...n){let{delegate:r}=ur;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=ur;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Mo=class extends Co{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return ur.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&ur.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Bt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var wn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};wn.now=cr.now;var To=class extends wn{constructor(t,n=wn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var lt=new To(Mo),Qu=lt;var zt=new N(e=>e.complete());function xo(e){return e&&T(e.schedule)}function ia(e){return e[e.length-1]}function So(e){return T(ia(e))?e.pop():void 0}function We(e){return xo(ia(e))?e.pop():void 0}function Xu(e,t){return typeof ia(e)=="number"?e.pop():t}function ed(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Ju(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Gt(e){return this instanceof Gt?(this.v=e,this):new Gt(e)}function td(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(v,w){i.push([f,g,v,w])>1||c(f,g)})},h&&(o[f]=h(o[f])))}function c(f,h){try{l(r[f](h))}catch(g){p(i[0][3],g)}}function l(f){f.value instanceof Gt?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){c("next",f)}function d(f){c("throw",f)}function p(f,h){f(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function nd(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Ju=="function"?Ju(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var No=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ao(e){return T(e?.then)}function Ro(e){return T(e[En])}function Oo(e){return Symbol.asyncIterator&&T(e?.[Symbol.asyncIterator])}function ko(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function ab(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Fo=ab();function Po(e){return T(e?.[Fo])}function Lo(e){return td(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Gt(n.read());if(o)return yield Gt(void 0);yield yield Gt(r)}}finally{n.releaseLock()}})}function jo(e){return T(e?.getReader)}function P(e){if(e instanceof N)return e;if(e!=null){if(Ro(e))return cb(e);if(No(e))return lb(e);if(Ao(e))return ub(e);if(Oo(e))return rd(e);if(Po(e))return db(e);if(jo(e))return fb(e)}throw ko(e)}function cb(e){return new N(t=>{let n=e[En]();if(T(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function lb(e){return new N(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function ub(e){return new N(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Eo)})}function db(e){return new N(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function rd(e){return new N(t=>{pb(e,t).catch(n=>t.error(n))})}function fb(e){return rd(Lo(e))}function pb(e,t){var n,r,o,i;return ed(this,void 0,void 0,function*(){try{for(n=nd(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ce(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Bo(e,t=0){return I((n,r)=>{n.subscribe(E(r,o=>ce(r,e,()=>r.next(o),t),()=>ce(r,e,()=>r.complete(),t),o=>ce(r,e,()=>r.error(o),t)))})}function Vo(e,t=0){return I((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function od(e,t){return P(e).pipe(Vo(t),Bo(t))}function id(e,t){return P(e).pipe(Vo(t),Bo(t))}function sd(e,t){return new N(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ad(e,t){return new N(n=>{let r;return ce(n,t,()=>{r=e[Fo](),ce(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>T(r?.return)&&r.return()})}function Ho(e,t){if(!e)throw new Error("Iterable cannot be null");return new N(n=>{ce(n,t,()=>{let r=e[Symbol.asyncIterator]();ce(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function cd(e,t){return Ho(Lo(e),t)}function ld(e,t){if(e!=null){if(Ro(e))return od(e,t);if(No(e))return sd(e,t);if(Ao(e))return id(e,t);if(Oo(e))return Ho(e,t);if(Po(e))return ad(e,t);if(jo(e))return cd(e,t)}throw ko(e)}function Ae(e,t){return t?ld(e,t):P(e)}function In(...e){let t=We(e);return Ae(e,t)}function mb(e,t){let n=T(e)?e:()=>e,r=o=>o.error(n());return new N(t?o=>t.schedule(r,0,o):r)}function hb(e){return!!e&&(e instanceof N||T(e.lift)&&T(e.subscribe))}function Uo(e){return e instanceof Date&&!isNaN(e)}var gb=It(e=>function(n=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n});function bb(e,t){let{first:n,each:r,with:o=yb,scheduler:i=t??lt,meta:s=null}=Uo(e)?{first:e}:typeof e=="number"?{each:e}:e;if(n==null&&r==null)throw new TypeError("No timeout provided.");return I((a,c)=>{let l,u,d=null,p=0,f=h=>{u=ce(c,i,()=>{try{l.unsubscribe(),P(o({meta:s,lastValue:d,seen:p})).subscribe(c)}catch(g){c.error(g)}},h)};l=a.subscribe(E(c,h=>{u?.unsubscribe(),p++,c.next(d=h),r>0&&f(r)},void 0,void 0,()=>{u?.closed||u?.unsubscribe(),d=null})),!p&&f(n!=null?typeof n=="number"?n:+n-i.now():r)})}function yb(e){throw new gb(e)}function X(e,t){return I((n,r)=>{let o=0;n.subscribe(E(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:vb}=Array;function Db(e,t){return vb(t)?e(...t):e(t)}function $o(e){return X(t=>Db(e,t))}var{isArray:_b}=Array,{getPrototypeOf:Eb,prototype:wb,keys:Ib}=Object;function zo(e){if(e.length===1){let t=e[0];if(_b(t))return{args:t,keys:null};if(Cb(t)){let n=Ib(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Cb(e){return e&&typeof e=="object"&&Eb(e)===wb}function Go(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function sa(...e){let t=We(e),n=So(e),{args:r,keys:o}=zo(e);if(r.length===0)return Ae([],t);let i=new N(Mb(r,t,o?s=>Go(o,s):me));return n?i.pipe($o(n)):i}function Mb(e,t,n=me){return r=>{ud(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ud(t,()=>{let l=Ae(e[c],t),u=!1;l.subscribe(E(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ud(e,t,n){e?ce(n,e,t):t()}function dd(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,p=()=>{d&&!c.length&&!l&&t.complete()},f=g=>l<r?h(g):c.push(g),h=g=>{i&&t.next(g),l++;let v=!1;P(n(g,u++)).subscribe(E(t,w=>{o?.(w),i?f(w):t.next(w)},()=>{v=!0},void 0,()=>{if(v)try{for(l--;c.length&&l<r;){let w=c.shift();s?ce(t,s,()=>h(w)):h(w)}p()}catch(w){t.error(w)}}))};return e.subscribe(E(t,f,()=>{d=!0,p()})),()=>{a?.()}}function Wt(e,t,n=1/0){return T(t)?Wt((r,o)=>X((i,s)=>t(r,i,o,s))(P(e(r,o))),n):(typeof t=="number"&&(n=t),I((r,o)=>dd(r,o,e,n)))}function dr(e=1/0){return Wt(me,e)}function fd(){return dr(1)}function Cn(...e){return fd()(Ae(e,We(e)))}function Tb(e){return new N(t=>{P(e()).subscribe(t)})}function xb(...e){let t=So(e),{args:n,keys:r}=zo(e),o=new N(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;P(n[u]).subscribe(E(i,p=>{d||(d=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?Go(r,a):a),i.complete())}))}});return t?o.pipe($o(t)):o}function fr(e=0,t,n=Qu){let r=-1;return t!=null&&(xo(t)?n=t:r=t),new N(o=>{let i=Uo(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Sb(e=0,t=lt){return e<0&&(e=0),fr(e,e,t)}function Nb(...e){let t=We(e),n=Xu(e,1/0),r=e;return r.length?r.length===1?P(r[0]):dr(n)(Ae(r,t)):zt}var Ab=new N(Vt);function pd(e){return I((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(E(n,l=>{r=!0,o=l,i||P(e(l)).subscribe(i=E(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Rb(e,t=lt){return pd(()=>fr(e,t))}function md(e){return I((t,n)=>{let r=null,o=!1,i;r=t.subscribe(E(n,void 0,void 0,s=>{i=P(e(s,md(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function hd(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(E(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function aa(e,t){return T(t)?Wt(e,t,1):Wt(e,1)}function qt(e,t=lt){return I((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(E(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function pr(e){return I((t,n)=>{let r=!1;t.subscribe(E(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function mr(e){return e<=0?()=>zt:I((t,n)=>{let r=0;t.subscribe(E(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function ca(e,t=me){return e=e??Ob,I((n,r)=>{let o,i=!0;n.subscribe(E(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Ob(e,t){return e===t}function Wo(e=kb){return I((t,n)=>{let r=!1;t.subscribe(E(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function kb(){return new ct}function la(e){return I((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Fb(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Me((o,i)=>e(o,i,r)):me,mr(1),n?pr(t):Wo(()=>new ct))}function ua(e){return e<=0?()=>zt:I((t,n)=>{let r=[];t.subscribe(E(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Pb(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Me((o,i)=>e(o,i,r)):me,ua(1),n?pr(t):Wo(()=>new ct))}function Lb(){return I((e,t)=>{let n,r=!1;e.subscribe(E(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function jb(e,t){return I(hd(e,t,arguments.length>=2,!0))}function fa(e={}){let{connector:t=()=>new B,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,l=0,u=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=c=void 0,u=d=!1},h=()=>{let g=s;f(),g?.unsubscribe()};return I((g,v)=>{l++,!d&&!u&&p();let w=c=c??t();v.add(()=>{l--,l===0&&!d&&!u&&(a=da(h,o))}),w.subscribe(v),!s&&l>0&&(s=new je({next:U=>w.next(U),error:U=>{d=!0,p(),a=da(f,n,U),w.error(U)},complete:()=>{u=!0,p(),a=da(f,r),w.complete()}}),P(g).subscribe(s))})(i)}}function da(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new je({next:()=>{r.unsubscribe(),e()}});return P(t(...n)).subscribe(r)}function Bb(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,fa({connector:()=>new lr(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function hr(e){return Me((t,n)=>e<=n)}function pa(...e){let t=We(e);return I((n,r)=>{(t?Cn(e,n,t):Cn(e,n)).subscribe(r)})}function ma(e,t){return I((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(E(r,c=>{o?.unsubscribe();let l=0,u=i++;P(e(c,u)).subscribe(o=E(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Mn(e){return I((t,n)=>{P(e).subscribe(E(n,()=>n.complete(),Vt)),!n.closed&&t.subscribe(n)})}function Vb(e,t=!1){return I((n,r)=>{let o=0;n.subscribe(E(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function ha(e,t,n){let r=T(e)||t||n?{next:e,error:t,complete:n}:e;return r?I((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(E(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):me}function va(e,t){return Object.is(e,t)}var ne=null,qo=!1,Da=1,ge=Symbol("SIGNAL");function A(e){let t=ne;return ne=e,t}function _a(){return ne}var Tn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function br(e){if(qo)throw new Error("");if(ne===null)return;ne.consumerOnSignalRead(e);let t=ne.nextProducerIndex++;if(Xo(ne),t<ne.producerNode.length&&ne.producerNode[t]!==e&&gr(ne)){let n=ne.producerNode[t];Qo(n,ne.producerIndexOfThis[t])}ne.producerNode[t]!==e&&(ne.producerNode[t]=e,ne.producerIndexOfThis[t]=gr(ne)?bd(e,ne,t):0),ne.producerLastReadVersion[t]=e.version}function gd(){Da++}function Ea(e){if(!(gr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Da)){if(!e.producerMustRecompute(e)&&!Ko(e)){ya(e);return}e.producerRecomputeValue(e),ya(e)}}function wa(e){if(e.liveConsumerNode===void 0)return;let t=qo;qo=!0;try{for(let n of e.liveConsumerNode)n.dirty||Hb(n)}finally{qo=t}}function Ia(){return ne?.consumerAllowSignalWrites!==!1}function Hb(e){e.dirty=!0,wa(e),e.consumerMarkedDirty?.(e)}function ya(e){e.dirty=!1,e.lastCleanEpoch=Da}function yr(e){return e&&(e.nextProducerIndex=0),A(e)}function Yo(e,t){if(A(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(gr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Qo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Ko(e){Xo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Ea(n),r!==n.version))return!0}return!1}function vr(e){if(Xo(e),gr(e))for(let t=0;t<e.producerNode.length;t++)Qo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function bd(e,t,n){if(yd(e),e.liveConsumerNode.length===0&&vd(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=bd(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Qo(e,t){if(yd(e),e.liveConsumerNode.length===1&&vd(e))for(let r=0;r<e.producerNode.length;r++)Qo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Xo(o),o.producerIndexOfThis[r]=t}}function gr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Xo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function yd(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function vd(e){return e.producerNode!==void 0}function Jo(e,t){let n=Object.create(Ub);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Ea(n),br(n),n.value===Zo)throw n.error;return n.value};return r[ge]=n,r}var ga=Symbol("UNSET"),ba=Symbol("COMPUTING"),Zo=Symbol("ERRORED"),Ub=ae(k({},Tn),{value:ga,dirty:!0,error:null,equal:va,kind:"computed",producerMustRecompute(e){return e.value===ga||e.value===ba},producerRecomputeValue(e){if(e.value===ba)throw new Error("Detected cycle in computations.");let t=e.value;e.value=ba;let n=yr(e),r,o=!1;try{r=e.computation(),A(null),o=t!==ga&&t!==Zo&&r!==Zo&&e.equal(t,r)}catch(i){r=Zo,e.error=i}finally{Yo(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function $b(){throw new Error}var Dd=$b;function _d(e){Dd(e)}function Ca(e){Dd=e}var zb=null;function Ma(e,t){let n=Object.create(ei);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(br(n),n.value);return r[ge]=n,r}function Dr(e,t){Ia()||_d(e),e.equal(e.value,t)||(e.value=t,Gb(e))}function Ta(e,t){Ia()||_d(e),Dr(e,t(e.value))}var ei=ae(k({},Tn),{equal:va,value:void 0,kind:"signal"});function Gb(e){e.version++,gd(),wa(e),zb?.()}function xa(e){let t=A(null);try{return e()}finally{A(t)}}var Sa;function _r(){return Sa}function ut(e){let t=Sa;return Sa=e,t}var ti=Symbol("NotFound");var bf="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",D=class extends Error{code;constructor(t,n){super(rl(t,n)),this.code=t}};function Yb(e){return`NG0${Math.abs(e)}`}function rl(e,t){return`${Yb(e)}${t?": "+t:""}`}var yf=Symbol("InputSignalNode#UNSET"),Kb=ae(k({},ei),{transformFn:void 0,applyValueToInputSignal(e,t){Dr(e,t)}});function vf(e,t){let n=Object.create(Kb);n.value=e,n.transformFn=t?.transform;function r(){if(br(n),n.value===yf){let o=null;throw new D(-950,o)}return n.value}return r[ge]=n,r}function Pr(e){return{toString:e}.toString()}var ni="__parameters__";function Qb(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Df(e,t,n){return Pr(()=>{let r=Qb(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(ni)?c[ni]:Object.defineProperty(c,ni,{value:[]})[ni];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var qe=globalThis;function V(e){for(let t in e)if(e[t]===V)return t;throw Error("Could not find renamed property on target object.")}function Xb(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function ve(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ve).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Ga(e,t){return e?t?`${e} ${t}`:e:t||""}var Jb=V({__forward_ref__:V});function _f(e){return e.__forward_ref__=_f,e.toString=function(){return ve(this())},e}function se(e){return Ef(e)?e():e}function Ef(e){return typeof e=="function"&&e.hasOwnProperty(Jb)&&e.__forward_ref__===_f}function b(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function oe(e){return{providers:e.providers||[],imports:e.imports||[]}}function Bi(e){return Ed(e,wf)||Ed(e,If)}function vO(e){return Bi(e)!==null}function Ed(e,t){return e.hasOwnProperty(t)?e[t]:null}function ey(e){let t=e&&(e[wf]||e[If]);return t||null}function wd(e){return e&&(e.hasOwnProperty(Id)||e.hasOwnProperty(ty))?e[Id]:null}var wf=V({\u0275prov:V}),Id=V({\u0275inj:V}),If=V({ngInjectableDef:V}),ty=V({ngInjectorDef:V}),y=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=b({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Cf(e){return e&&!!e.\u0275providers}var ny=V({\u0275cmp:V}),ry=V({\u0275dir:V}),oy=V({\u0275pipe:V}),iy=V({\u0275mod:V}),pi=V({\u0275fac:V}),Cr=V({__NG_ELEMENT_ID__:V}),Cd=V({__NG_ENV_ID__:V});function kn(e){return typeof e=="string"?e:e==null?"":String(e)}function sy(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():kn(e)}function Mf(e,t){throw new D(-200,e)}function ol(e,t){throw new D(-201,!1)}var R=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(R||{}),Wa;function Tf(){return Wa}function be(e){let t=Wa;return Wa=e,t}function xf(e,t,n){let r=Bi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&R.Optional)return null;if(t!==void 0)return t;ol(e,"Injector")}var ay={},Yt=ay,qa="__NG_DI_FLAG__",mi=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?ti:Yt,r)}},hi="ngTempTokenPath",cy="ngTokenPath",ly=/\n/gm,uy="\u0275",Md="__source";function dy(e,t=R.Default){if(_r()===void 0)throw new D(-203,!1);if(_r()===null)return xf(e,void 0,t);{let n=_r(),r;return n instanceof mi?r=n.injector:r=n,r.get(e,t&R.Optional?null:void 0,t)}}function C(e,t=R.Default){return(Tf()||dy)(se(e),t)}function m(e,t=R.Default){return C(e,Vi(t))}function Vi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Za(e){let t=[];for(let n=0;n<e.length;n++){let r=se(e[n]);if(Array.isArray(r)){if(r.length===0)throw new D(900,!1);let o,i=R.Default;for(let s=0;s<r.length;s++){let a=r[s],c=fy(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(C(o,i))}else t.push(C(r))}return t}function Sf(e,t){return e[qa]=t,e.prototype[qa]=t,e}function fy(e){return e[qa]}function py(e,t,n,r){let o=e[hi];throw t[Md]&&o.unshift(t[Md]),e.message=my(`
`+e.message,o,n,r),e[cy]=o,e[hi]=null,e}function my(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==uy?e.slice(2):e;let o=ve(t);if(Array.isArray(t))o=t.map(ve).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ve(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(ly,`
  `)}`}var il=Sf(Df("Optional"),8);var Nf=Sf(Df("SkipSelf"),4);function Qt(e,t){let n=e.hasOwnProperty(pi);return n?e[pi]:null}function hy(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function gy(e){return e.flat(Number.POSITIVE_INFINITY)}function sl(e,t){e.forEach(n=>Array.isArray(n)?sl(n,t):t(n))}function Af(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function gi(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function by(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function yy(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Hi(e,t,n){let r=Lr(e,t);return r>=0?e[r|1]=n:(r=~r,yy(e,r,t,n)),r}function Na(e,t){let n=Lr(e,t);if(n>=0)return e[n|1]}function Lr(e,t){return vy(e,t,1)}function vy(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ye={},he=[],Mr=new y(""),Rf=new y("",-1),Of=new y(""),bi=class{get(t,n=Yt){if(n===Yt){let r=new Error(`NullInjectorError: No provider for ${ve(t)}!`);throw r.name="NullInjectorError",r}return n}};function kf(e,t){let n=e[iy]||null;if(!n&&t===!0)throw new Error(`Type ${ve(e)} does not have '\u0275mod' property.`);return n}function Tt(e){return e[ny]||null}function Ff(e){return e[ry]||null}function Dy(e){return e[oy]||null}function jr(e){return{\u0275providers:e}}function _y(...e){return{\u0275providers:Pf(!0,e),\u0275fromNgModule:!0}}function Pf(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return sl(t,s=>{let a=s;Ya(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Lf(o,i),n}function Lf(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];al(o,i=>{t(i,r)})}}function Ya(e,t,n,r){if(e=se(e),!e)return!1;let o=null,i=wd(e),s=!i&&Tt(e);if(!i&&!s){let c=e.ngModule;if(i=wd(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Ya(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{sl(i.imports,u=>{Ya(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&Lf(l,t)}if(!a){let l=Qt(o)||(()=>new o);t({provide:o,useFactory:l,deps:he},o),t({provide:Of,useValue:o,multi:!0},o),t({provide:Mr,useValue:()=>C(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;al(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function al(e,t){for(let n of e)Cf(n)&&(n=n.\u0275providers),Array.isArray(n)?al(n,t):t(n)}var Ey=V({provide:String,useValue:V});function jf(e){return e!==null&&typeof e=="object"&&Ey in e}function wy(e){return!!(e&&e.useExisting)}function Iy(e){return!!(e&&e.useFactory)}function Fn(e){return typeof e=="function"}function Cy(e){return!!e.useClass}var Ui=new y(""),ai={},Td={},Aa;function $i(){return Aa===void 0&&(Aa=new bi),Aa}var Oe=class{},Tr=class extends Oe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Qa(t,s=>this.processProvider(s)),this.records.set(Rf,xn(void 0,this)),o.has("environment")&&this.records.set(Oe,xn(void 0,this));let i=this.records.get(Ui);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Of,he,R.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?ti:Yt,r)}destroy(){wr(this),this._destroyed=!0;let t=A(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),A(t)}}onDestroy(t){return wr(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){wr(this);let n=ut(this),r=be(void 0),o;try{return t()}finally{ut(n),be(r)}}get(t,n=Yt,r=R.Default){if(wr(this),t.hasOwnProperty(Cd))return t[Cd](this);r=Vi(r);let o,i=ut(this),s=be(void 0);try{if(!(r&R.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=Ny(t)&&Bi(t);l&&this.injectableDefInScope(l)?c=xn(Ka(t),ai):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&R.Self?$i():this.parent;return n=r&R.Optional&&n===Yt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[hi]=a[hi]||[]).unshift(ve(t)),i)throw a;return py(a,t,"R3InjectorError",this.source)}else throw a}finally{be(s),ut(i)}}resolveInjectorInitializers(){let t=A(null),n=ut(this),r=be(void 0),o;try{let i=this.get(Mr,he,R.Self);for(let s of i)s()}finally{ut(n),be(r),A(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ve(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=se(t);let n=Fn(t)?t:se(t&&t.provide),r=Ty(t);if(!Fn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=xn(void 0,ai,!0),o.factory=()=>Za(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=A(null);try{return n.value===Td?Mf(ve(t)):n.value===ai&&(n.value=Td,n.value=n.factory()),typeof n.value=="object"&&n.value&&Sy(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{A(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=se(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Ka(e){let t=Bi(e),n=t!==null?t.factory:Qt(e);if(n!==null)return n;if(e instanceof y)throw new D(204,!1);if(e instanceof Function)return My(e);throw new D(204,!1)}function My(e){if(e.length>0)throw new D(204,!1);let n=ey(e);return n!==null?()=>n.factory(e):()=>new e}function Ty(e){if(jf(e))return xn(void 0,e.useValue);{let t=Bf(e);return xn(t,ai)}}function Bf(e,t,n){let r;if(Fn(e)){let o=se(e);return Qt(o)||Ka(o)}else if(jf(e))r=()=>se(e.useValue);else if(Iy(e))r=()=>e.useFactory(...Za(e.deps||[]));else if(wy(e))r=()=>C(se(e.useExisting));else{let o=se(e&&(e.useClass||e.provide));if(xy(e))r=()=>new o(...Za(e.deps));else return Qt(o)||Ka(o)}return r}function wr(e){if(e.destroyed)throw new D(205,!1)}function xn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function xy(e){return!!e.deps}function Sy(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Ny(e){return typeof e=="function"||typeof e=="object"&&e instanceof y}function Qa(e,t){for(let n of e)Array.isArray(n)?Qa(n,t):n&&Cf(n)?Qa(n.\u0275providers,t):t(n)}function zi(e,t){let n;e instanceof Tr?(wr(e),n=e):n=new mi(e);let r,o=ut(n),i=be(void 0);try{return t()}finally{ut(o),be(i)}}function Vf(){return Tf()!==void 0||_r()!=null}function cl(e){if(!Vf())throw new D(-203,!1)}function Ay(e){return typeof e=="function"}var mt=0,x=1,M=2,ue=3,He=4,De=5,Pn=6,yi=7,re=8,Xt=9,dt=10,G=11,xr=12,xd=13,zn=14,Te=15,Jt=16,Sn=17,ft=18,Gi=19,Hf=20,Ct=21,Ra=22,en=23,Re=24,Rn=25,K=26,Uf=1;var tn=7,vi=8,Ln=9,le=10;function Mt(e){return Array.isArray(e)&&typeof e[Uf]=="object"}function ht(e){return Array.isArray(e)&&e[Uf]===!0}function ll(e){return(e.flags&4)!==0}function Gn(e){return e.componentOffset>-1}function Wi(e){return(e.flags&1)===1}function Ke(e){return!!e.template}function Di(e){return(e[M]&512)!==0}function Wn(e){return(e[M]&256)===256}var Xa=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function $f(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var qi=(()=>{let e=()=>zf;return e.ngInherit=!0,e})();function zf(e){return e.type.prototype.ngOnChanges&&(e.setInput=Oy),Ry}function Ry(){let e=Wf(this),t=e?.current;if(t){let n=e.previous;if(n===Ye)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Oy(e,t,n,r,o){let i=this.declaredInputs[r],s=Wf(e)||ky(e,{previous:Ye,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Xa(l&&l.currentValue,n,c===Ye),$f(e,t,o,n)}var Gf="__ngSimpleChanges__";function Wf(e){return e[Gf]||null}function ky(e,t){return e[Gf]=t}var Sd=null;var L=function(e,t=null,n){Sd?.(e,t,n)},qf="svg",Fy="math";function Qe(e){for(;Array.isArray(e);)e=e[mt];return e}function Zf(e,t){return Qe(t[e])}function rt(e,t){return Qe(t[e.index])}function ul(e,t){return e.data[t]}function Br(e,t){return e[t]}function Xe(e,t){let n=t[e];return Mt(n)?n:n[mt]}function Py(e){return(e[M]&4)===4}function dl(e){return(e[M]&128)===128}function Ly(e){return ht(e[ue])}function xt(e,t){return t==null?null:e[t]}function Yf(e){e[Sn]=0}function Kf(e){e[M]&1024||(e[M]|=1024,dl(e)&&qn(e))}function jy(e,t){for(;e>0;)t=t[zn],e--;return t}function Zi(e){return!!(e[M]&9216||e[Re]?.dirty)}function Ja(e){e[dt].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),Zi(e)&&qn(e)}function qn(e){e[dt].changeDetectionScheduler?.notify(0);let t=nn(e);for(;t!==null&&!(t[M]&8192||(t[M]|=8192,!dl(t)));)t=nn(t)}function Qf(e,t){if(Wn(e))throw new D(911,!1);e[Ct]===null&&(e[Ct]=[]),e[Ct].push(t)}function By(e,t){if(e[Ct]===null)return;let n=e[Ct].indexOf(t);n!==-1&&e[Ct].splice(n,1)}function nn(e){let t=e[ue];return ht(t)?t[ue]:t}function fl(e){return e[yi]??=[]}function pl(e){return e.cleanup??=[]}function Vy(e,t,n,r){let o=fl(t);o.push(n),e.firstCreatePass&&pl(e).push(r,o.length-1)}var S={lFrame:rp(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var ec=!1;function Hy(){return S.lFrame.elementDepthCount}function Uy(){S.lFrame.elementDepthCount++}function $y(){S.lFrame.elementDepthCount--}function ml(){return S.bindingsEnabled}function Xf(){return S.skipHydrationRootTNode!==null}function zy(e){return S.skipHydrationRootTNode===e}function Gy(){S.skipHydrationRootTNode=null}function _(){return S.lFrame.lView}function $(){return S.lFrame.tView}function DO(e){return S.lFrame.contextLView=e,e[re]}function _O(e){return S.lFrame.contextLView=null,e}function de(){let e=Jf();for(;e!==null&&e.type===64;)e=e.parent;return e}function Jf(){return S.lFrame.currentTNode}function Wy(){let e=S.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function At(e,t){let n=S.lFrame;n.currentTNode=e,n.isParent=t}function hl(){return S.lFrame.isParent}function gl(){S.lFrame.isParent=!1}function qy(){return S.lFrame.contextLView}function ep(){return ec}function _i(e){let t=ec;return ec=e,t}function ln(){let e=S.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Zy(){return S.lFrame.bindingIndex}function Yy(e){return S.lFrame.bindingIndex=e}function un(){return S.lFrame.bindingIndex++}function bl(e){let t=S.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Ky(){return S.lFrame.inI18n}function Qy(e,t){let n=S.lFrame;n.bindingIndex=n.bindingRootIndex=e,tc(t)}function Xy(){return S.lFrame.currentDirectiveIndex}function tc(e){S.lFrame.currentDirectiveIndex=e}function Jy(e){let t=S.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function yl(){return S.lFrame.currentQueryIndex}function Yi(e){S.lFrame.currentQueryIndex=e}function ev(e){let t=e[x];return t.type===2?t.declTNode:t.type===1?e[De]:null}function tp(e,t,n){if(n&R.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&R.Host);)if(o=ev(i),o===null||(i=i[zn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=S.lFrame=np();return r.currentTNode=t,r.lView=e,!0}function vl(e){let t=np(),n=e[x];S.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function np(){let e=S.lFrame,t=e===null?null:e.child;return t===null?rp(e):t}function rp(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function op(){let e=S.lFrame;return S.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var ip=op;function Dl(){let e=op();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function tv(e){return(S.lFrame.contextLView=jy(e,S.lFrame.contextLView))[re]}function gt(){return S.lFrame.selectedIndex}function rn(e){S.lFrame.selectedIndex=e}function Vr(){let e=S.lFrame;return ul(e.tView,e.selectedIndex)}function EO(){S.lFrame.currentNamespace=qf}function wO(){nv()}function nv(){S.lFrame.currentNamespace=null}function rv(){return S.lFrame.currentNamespace}var sp=!0;function Ki(){return sp}function Qi(e){sp=e}function ov(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=zf(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function _l(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ci(e,t,n){ap(e,t,3,n)}function li(e,t,n,r){(e[M]&3)===n&&ap(e,t,n,r)}function Oa(e,t){let n=e[M];(n&3)===t&&(n&=16383,n+=1,e[M]=n)}function ap(e,t,n,r){let o=r!==void 0?e[Sn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Sn]+=65536),(a<i||i==-1)&&(iv(e,n,t,c),e[Sn]=(e[Sn]&**********)+c+2),c++}function Nd(e,t){L(4,e,t);let n=A(null);try{t.call(e)}finally{A(n),L(5,e,t)}}function iv(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[M]>>14<e[Sn]>>16&&(e[M]&3)===t&&(e[M]+=16384,Nd(a,i)):Nd(a,i)}var On=-1,on=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function sv(e){return(e.flags&8)!==0}function av(e){return(e.flags&16)!==0}function cv(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];lv(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function cp(e){return e===3||e===4||e===6}function lv(e){return e.charCodeAt(0)===64}function jn(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ad(e,n,o,null,t[++r]):Ad(e,n,o,null,null))}}return e}function Ad(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function lp(e){return e!==On}function Ei(e){return e&32767}function uv(e){return e>>16}function wi(e,t){let n=uv(e),r=t;for(;n>0;)r=r[zn],n--;return r}var nc=!0;function Ii(e){let t=nc;return nc=e,t}var dv=256,up=dv-1,dp=5,fv=0,Ze={};function pv(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Cr)&&(r=n[Cr]),r==null&&(r=n[Cr]=fv++);let o=r&up,i=1<<o;t.data[e+(o>>dp)]|=i}function Ci(e,t){let n=fp(e,t);if(n!==-1)return n;let r=t[x];r.firstCreatePass&&(e.injectorIndex=t.length,ka(r.data,e),ka(t,null),ka(r.blueprint,null));let o=El(e,t),i=e.injectorIndex;if(lp(o)){let s=Ei(o),a=wi(o,t),c=a[x].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function ka(e,t){e.push(0,0,0,0,0,0,0,0,t)}function fp(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function El(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=bp(o),r===null)return On;if(n++,o=o[zn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return On}function rc(e,t,n){pv(e,t,n)}function mv(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(cp(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function pp(e,t,n){if(n&R.Optional||e!==void 0)return e;ol(t,"NodeInjector")}function mp(e,t,n,r){if(n&R.Optional&&r===void 0&&(r=null),(n&(R.Self|R.Host))===0){let o=e[Xt],i=be(void 0);try{return o?o.get(t,r,n&R.Optional):xf(t,r,n&R.Optional)}finally{be(i)}}return pp(r,t,n)}function hp(e,t,n,r=R.Default,o){if(e!==null){if(t[M]&2048&&!(r&R.Self)){let s=yv(e,t,n,r,Ze);if(s!==Ze)return s}let i=gp(e,t,n,r,Ze);if(i!==Ze)return i}return mp(t,n,r,o)}function gp(e,t,n,r,o){let i=gv(n);if(typeof i=="function"){if(!tp(t,e,r))return r&R.Host?pp(o,n,r):mp(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&R.Optional))ol(n);else return s}finally{ip()}}else if(typeof i=="number"){let s=null,a=fp(e,t),c=On,l=r&R.Host?t[Te][De]:null;for((a===-1||r&R.SkipSelf)&&(c=a===-1?El(e,t):t[a+8],c===On||!Od(r,!1)?a=-1:(s=t[x],a=Ei(c),t=wi(c,t)));a!==-1;){let u=t[x];if(Rd(i,a,u.data)){let d=hv(a,t,n,s,r,l);if(d!==Ze)return d}c=t[a+8],c!==On&&Od(r,t[x].data[a+8]===l)&&Rd(i,a,t)?(s=u,a=Ei(c),t=wi(c,t)):a=-1}}return o}function hv(e,t,n,r,o,i){let s=t[x],a=s.data[e+8],c=r==null?Gn(a)&&nc:r!=s&&(a.type&3)!==0,l=o&R.Host&&i===a,u=ui(a,s,n,c,l);return u!==null?Sr(t,s,u,a):Ze}function ui(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:l;for(let f=d;f<p;f++){let h=s[f];if(f<c&&n===h||f>=c&&h.type===n)return f}if(o){let f=s[c];if(f&&Ke(f)&&f.type===n)return c}return null}function Sr(e,t,n,r){let o=e[n],i=t.data;if(o instanceof on){let s=o;s.resolving&&Mf(sy(i[n]));let a=Ii(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?be(s.injectImpl):null,u=tp(e,r,R.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&ov(n,i[n],t)}finally{l!==null&&be(l),Ii(a),s.resolving=!1,ip()}}return o}function gv(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Cr)?e[Cr]:void 0;return typeof t=="number"?t>=0?t&up:bv:t}function Rd(e,t,n){let r=1<<e;return!!(n[t+(e>>dp)]&r)}function Od(e,t){return!(e&R.Self)&&!(e&R.Host&&t)}var Kt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return hp(this._tNode,this._lView,t,Vi(r),n)}};function bv(){return new Kt(de(),_())}function wl(e){return Pr(()=>{let t=e.prototype.constructor,n=t[pi]||oc(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[pi]||oc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function oc(e){return Ef(e)?()=>{let t=oc(se(e));return t&&t()}:Qt(e)}function yv(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[M]&2048&&!Di(s);){let a=gp(i,s,n,r|R.Self,Ze);if(a!==Ze)return a;let c=i.parent;if(!c){let l=s[Hf];if(l){let u=l.get(n,Ze,r);if(u!==Ze)return u}c=bp(s),s=s[zn]}i=c}return o}function bp(e){let t=e[x],n=t.type;return n===2?t.declTNode:n===1?e[De]:null}function yp(e){return mv(de(),e)}function kd(e,t=null,n=null,r){let o=vp(e,t,n,r);return o.resolveInjectorInitializers(),o}function vp(e,t=null,n=null,r,o=new Set){let i=[n||he,_y(e)];return r=r||(typeof e=="object"?void 0:ve(e)),new Tr(i,t||$i(),r||null,o)}var J=class e{static THROW_IF_NOT_FOUND=Yt;static NULL=new bi;static create(t,n){if(Array.isArray(t))return kd({name:""},n,t,"");{let r=t.name??"";return kd({name:r},t.parent,t.providers,r)}}static \u0275prov=b({token:e,providedIn:"any",factory:()=>C(Rf)});static __NG_ELEMENT_ID__=-1};var Fd=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>yp(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},vv=new y("");vv.__NG_ELEMENT_ID__=e=>{let t=de();if(t===null)throw new D(204,!1);if(t.type&2)return t.value;if(e&R.Optional)return null;throw new D(204,!1)};var Dp=!1,Xi=(()=>{class e{static __NG_ELEMENT_ID__=Dv;static __NG_ENV_ID__=n=>n}return e})(),Mi=class extends Xi{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Wn(n)?(t(),()=>{}):(Qf(n,t),()=>By(n,t))}};function Dv(){return new Mi(_())}var sn=class{},Ji=new y("",{providedIn:"root",factory:()=>!1});var _p=new y(""),Ep=new y(""),Zn=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new $t(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})();var ic=class extends B{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Vf()&&(this.destroyRef=m(Xi,{optional:!0})??void 0,this.pendingTasks=m(Zn,{optional:!0})??void 0)}emit(t){let n=A(null);try{super.next(t)}finally{A(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Y&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ye=ic;function Nr(...e){}function wp(e){let t,n;function r(){e=Nr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Pd(e){return queueMicrotask(()=>e()),()=>{e=Nr}}var Il="isAngularZone",Ti=Il+"_ID",_v=0,O=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ye(!1);onMicrotaskEmpty=new ye(!1);onStable=new ye(!1);onError=new ye(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Dp}=t;if(typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Iv(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Il)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new D(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Ev,Nr,Nr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Ev={};function Cl(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function wv(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){wp(()=>{e.callbackScheduled=!1,sc(e),e.isCheckStableRunning=!0,Cl(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),sc(e)}function Iv(e){let t=()=>{wv(e)},n=_v++;e._inner=e._inner.fork({name:"angular",properties:{[Il]:!0,[Ti]:n,[Ti+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Cv(c))return r.invokeTask(i,s,a,c);try{return Ld(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),jd(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Ld(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Mv(c)&&t(),jd(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,sc(e),Cl(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function sc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Ld(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function jd(e){e._nesting--,Cl(e)}var ac=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ye;onMicrotaskEmpty=new ye;onStable=new ye;onError=new ye;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Cv(e){return Ip(e,"__ignore_ng_zone__")}function Mv(e){return Ip(e,"__scheduler_tick__")}function Ip(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Je=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Tv=new y("",{providedIn:"root",factory:()=>{let e=m(O),t=m(Je);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Bd(e,t){return vf(e,t)}function xv(e){return vf(yf,e)}var IO=(Bd.required=xv,Bd);function Sv(){return Yn(de(),_())}function Yn(e,t){return new ee(rt(e,t))}var ee=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Sv}return e})();function Cp(e){return e instanceof ee?e.nativeElement:e}function es(e){return typeof e=="function"&&e[ge]!==void 0}function Ml(e,t){let n=Ma(e,t?.equal),r=n[ge];return n.set=o=>Dr(r,o),n.update=o=>Ta(r,o),n.asReadonly=Nv.bind(n),n}function Nv(){let e=this[ge];if(e.readonlyFn===void 0){let t=()=>this();t[ge]=e,e.readonlyFn=t}return e.readonlyFn}function Mp(e){return es(e)&&typeof e.set=="function"}function Av(){return this._results[Symbol.iterator]()}var Bn=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new B}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=gy(t);(this._changesDetected=!hy(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Av};function Tp(e){return(e.flags&128)===128}var xp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(xp||{}),Sp=new Map,Rv=0;function Ov(){return Rv++}function kv(e){Sp.set(e[Gi],e)}function cc(e){Sp.delete(e[Gi])}var Vd="__ngContext__";function Kn(e,t){Mt(t)?(e[Vd]=t[Gi],kv(t)):e[Vd]=t}function Np(e){return Rp(e[xr])}function Ap(e){return Rp(e[He])}function Rp(e){for(;e!==null&&!ht(e);)e=e[He];return e}var lc;function Op(e){lc=e}function Fv(){if(lc!==void 0)return lc;if(typeof document<"u")return document;throw new D(210,!1)}var Rt=new y("",{providedIn:"root",factory:()=>Pv}),Pv="ng",Tl=new y(""),ot=new y("",{providedIn:"platform",factory:()=>"unknown"});var Qn=new y(""),Hr=new y("",{providedIn:"root",factory:()=>Fv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Lv="h",jv="b";var kp=!1,Bv=new y("",{providedIn:"root",factory:()=>kp});var xl=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(xl||{}),Xn=new y(""),Hd=new Set;function dn(e){Hd.has(e)||(Hd.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Sl=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Vv}return e})();function Vv(){return new Sl(_(),de())}var Nn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Nn||{}),Fp=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})(),Hv=[Nn.EarlyRead,Nn.Write,Nn.MixedReadWrite,Nn.Read],Uv=(()=>{class e{ngZone=m(O);scheduler=m(sn);errorHandler=m(Je,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){m(Xn,{optional:!0})}execute(){let n=this.sequences.size>0;n&&L(16),this.executing=!0;for(let r of Hv)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&L(17)}register(n){let{view:r}=n;r!==void 0?((r[Rn]??=[]).push(n),qn(r),r[M]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(xl.AFTER_NEXT_RENDER,n):n()}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})(),uc=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Rn];t&&(this.view[Rn]=t.filter(n=>n!==this))}};function $v(e,t){!t?.injector&&cl($v);let n=t?.injector??m(J);return dn("NgAfterRender"),Pp(e,n,t,!1)}function Nl(e,t){!t?.injector&&cl(Nl);let n=t?.injector??m(J);return dn("NgAfterNextRender"),Pp(e,n,t,!0)}function zv(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Pp(e,t,n,r){let o=t.get(Fp);o.impl??=t.get(Uv);let i=t.get(Xn,null,{optional:!0}),s=n?.phase??Nn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Xi):null,c=t.get(Sl,null,{optional:!0}),l=new uc(o.impl,zv(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var Gv=()=>null;function Lp(e,t,n=!1){return Gv(e,t,n)}function jp(e,t){let n=e.contentQueries;if(n!==null){let r=A(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Yi(i),a.contentQueries(2,t[s],s)}}}finally{A(r)}}}function dc(e,t,n){Yi(0);let r=A(null);try{t(e,n)}finally{A(r)}}function Al(e,t,n){if(ll(t)){let r=A(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{A(r)}}}var et=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(et||{}),ri;function Wv(){if(ri===void 0&&(ri=null,qe.trustedTypes))try{ri=qe.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ri}function ts(e){return Wv()?.createHTML(e)||e}var oi;function qv(){if(oi===void 0&&(oi=null,qe.trustedTypes))try{oi=qe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return oi}function Ud(e){return qv()?.createScriptURL(e)||e}var pt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${bf})`}},fc=class extends pt{getTypeName(){return"HTML"}},pc=class extends pt{getTypeName(){return"Style"}},mc=class extends pt{getTypeName(){return"Script"}},hc=class extends pt{getTypeName(){return"URL"}},gc=class extends pt{getTypeName(){return"ResourceURL"}};function Ue(e){return e instanceof pt?e.changingThisBreaksApplicationSecurity:e}function Ot(e,t){let n=Zv(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${bf})`)}return n===t}function Zv(e){return e instanceof pt&&e.getTypeName()||null}function Bp(e){return new fc(e)}function Vp(e){return new pc(e)}function Hp(e){return new mc(e)}function Up(e){return new hc(e)}function $p(e){return new gc(e)}function Yv(e){let t=new yc(e);return Kv()?new bc(t):t}var bc=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(ts(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},yc=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=ts(t),n}};function Kv(){try{return!!new window.DOMParser().parseFromString(ts(""),"text/html")}catch{return!1}}var Qv=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function ns(e){return e=String(e),e.match(Qv)?e:"unsafe:"+e}function bt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Ur(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var zp=bt("area,br,col,hr,img,wbr"),Gp=bt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Wp=bt("rp,rt"),Xv=Ur(Wp,Gp),Jv=Ur(Gp,bt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),eD=Ur(Wp,bt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),$d=Ur(zp,Jv,eD,Xv),qp=bt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),tD=bt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),nD=bt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),rD=Ur(qp,tD,nD),oD=bt("script,style,template"),vc=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=aD(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=sD(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=zd(t).toLowerCase();if(!$d.hasOwnProperty(n))return this.sanitizedSomething=!0,!oD.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!rD.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;qp[a]&&(c=ns(c)),this.buf.push(" ",s,'="',Gd(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=zd(t).toLowerCase();$d.hasOwnProperty(n)&&!zp.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Gd(t))}};function iD(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function sD(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Zp(t);return t}function aD(e){let t=e.firstChild;if(t&&iD(e,t))throw Zp(t);return t}function zd(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Zp(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var cD=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,lD=/([^\#-~ |!])/g;function Gd(e){return e.replace(/&/g,"&amp;").replace(cD,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(lD,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ii;function Yp(e,t){let n=null;try{ii=ii||Yv(e);let r=t?String(t):"";n=ii.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=ii.getInertBodyElement(r)}while(r!==i);let a=new vc().sanitizeChildren(Wd(n)||n);return ts(a)}finally{if(n){let r=Wd(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Wd(e){return"content"in e&&uD(e)?e.content:null}function uD(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var it=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(it||{});function dD(e){let t=Kp();return t?t.sanitize(it.URL,e)||"":Ot(e,"URL")?Ue(e):ns(kn(e))}function fD(e){let t=Kp();if(t)return Ud(t.sanitize(it.RESOURCE_URL,e)||"");if(Ot(e,"ResourceURL"))return Ud(Ue(e));throw new D(904,!1)}function pD(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?fD:dD}function CO(e,t,n){return pD(t,n)(e)}function Kp(){let e=_();return e&&e[dt].sanitizer}var mD=/^>|^->|<!--|-->|--!>|<!-$/g,hD=/(<|>)/g,gD="\u200B$1\u200B";function bD(e){return e.replace(mD,t=>t.replace(hD,gD))}function Qp(e){return e instanceof Function?e():e}function yD(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Xp="ng-template";function vD(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&yD(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Rl(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Rl(e){return e.type===4&&e.value!==Xp}function DD(e,t,n){let r=e.type===4&&!n?Xp:e.value;return t===r}function _D(e,t,n){let r=4,o=e.attrs,i=o!==null?ID(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Be(r)&&!Be(c))return!1;if(s&&Be(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!DD(e,c,n)||c===""&&t.length===1){if(Be(r))return!1;s=!0}}else if(r&8){if(o===null||!vD(e,o,c,n)){if(Be(r))return!1;s=!0}}else{let l=t[++a],u=ED(c,o,Rl(e),n);if(u===-1){if(Be(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(Be(r))return!1;s=!0}}}}return Be(r)||s}function Be(e){return(e&1)===0}function ED(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return CD(t,e)}function Jp(e,t,n=!1){for(let r=0;r<t.length;r++)if(_D(e,t[r],n))return!0;return!1}function wD(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function ID(e){for(let t=0;t<e.length;t++){let n=e[t];if(cp(n))return t}return e.length}function CD(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function MD(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function qd(e,t){return e?":not("+t.trim()+")":t}function TD(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Be(s)&&(t+=qd(i,o),o=""),r=s,i=i||!Be(r);n++}return o!==""&&(t+=qd(i,o)),t}function xD(e){return e.map(TD).join(",")}function SD(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Be(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var _e={};function ND(e,t){return e.createText(t)}function AD(e,t,n){e.setValue(t,n)}function RD(e,t){return e.createComment(bD(t))}function em(e,t,n){return e.createElement(t,n)}function xi(e,t,n,r,o){e.insertBefore(t,n,r,o)}function tm(e,t,n){e.appendChild(t,n)}function Zd(e,t,n,r,o){r!==null?xi(e,t,n,r,o):tm(e,t,n)}function OD(e,t,n){e.removeChild(null,t,n)}function kD(e,t,n){e.setAttribute(t,"style",n)}function FD(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function nm(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&cv(e,t,r),o!==null&&FD(e,t,o),i!==null&&kD(e,t,i)}function Ol(e,t,n,r,o,i,s,a,c,l,u){let d=K+r,p=d+o,f=PD(d,p),h=typeof l=="function"?l():l;return f[x]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function PD(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:_e);return n}function LD(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ol(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function kl(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[mt]=o,d[M]=r|4|128|8|64|1024,(l!==null||e&&e[M]&2048)&&(d[M]|=2048),Yf(d),d[ue]=d[zn]=e,d[re]=n,d[dt]=s||e&&e[dt],d[G]=a||e&&e[G],d[Xt]=c||e&&e[Xt]||null,d[De]=i,d[Gi]=Ov(),d[Pn]=u,d[Hf]=l,d[Te]=t.type==2?e[Te]:d,d}function jD(e,t,n){let r=rt(t,e),o=LD(n),i=e[dt].rendererFactory,s=Fl(e,kl(e,o,null,rm(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function rm(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function om(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Fl(e,t){return e[xr]?e[xd][He]=t:e[xr]=t,e[xd]=t,t}function MO(e=1){im($(),_(),gt()+e,!1)}function im(e,t,n,r){if(!r)if((t[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ci(t,i,n)}else{let i=e.preOrderHooks;i!==null&&li(t,i,0,n)}rn(n)}var rs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(rs||{});function Dc(e,t,n,r){let o=A(null);try{let[i,s,a]=e.inputs[n],c=null;(s&rs.SignalBased)!==0&&(c=t[i][ge]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):$f(t,c,i,r)}finally{A(o)}}function sm(e,t,n,r,o){let i=gt(),s=r&2;try{rn(-1),s&&t.length>K&&im(e,t,K,!1),L(s?2:0,o),n(r,o)}finally{rn(i),L(s?3:1,o)}}function os(e,t,n){zD(e,t,n),(n.flags&64)===64&&GD(e,t,n)}function Pl(e,t,n=rt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function BD(e,t,n,r){let i=r.get(Bv,kp)||n===et.ShadowDom,s=e.selectRootElement(t,i);return VD(s),s}function VD(e){HD(e)}var HD=()=>null;function UD(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function is(e,t,n,r,o,i,s,a){if(!a&&jl(t,e,n,r,o)){Gn(t)&&$D(n,t.index);return}if(t.type&3){let c=rt(t,n);r=UD(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function $D(e,t){let n=Xe(t,e);n[M]&16||(n[M]|=64)}function zD(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Gn(n)&&jD(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Ci(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Sr(t,e,s,n);if(Kn(c,t),i!==null&&YD(t,s-r,c,a,n,i),Ke(a)){let l=Xe(n.index,t);l[re]=Sr(t,e,s,n)}}}function GD(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Xy();try{rn(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];tc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&WD(c,l)}}finally{rn(-1),tc(s)}}function WD(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Ll(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Jp(t,i.selectors,!1)&&(r??=[],Ke(i)?r.unshift(i):r.push(i))}return r}function qD(e,t,n,r,o,i){let s=rt(e,t);ZD(t[G],s,i,e.value,n,r,o)}function ZD(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?kn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function YD(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Dc(r,n,c,l)}}function KD(e,t){let n=e[Xt],r=n?n.get(Je,null):null;r&&r.handleError(t)}function jl(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],d=t.data[l];Dc(d,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];Dc(u,l,r,o),a=!0}return a}function QD(e,t){let n=Xe(t,e),r=n[x];XD(r,n);let o=n[mt];o!==null&&n[Pn]===null&&(n[Pn]=Lp(o,n[Xt])),L(18),Bl(r,n,n[re]),L(19,n[re])}function XD(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Bl(e,t,n){vl(t);try{let r=e.viewQuery;r!==null&&dc(1,r,n);let o=e.template;o!==null&&sm(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ft]?.finishViewCreation(e),e.staticContentQueries&&jp(e,t),e.staticViewQueries&&dc(2,e.viewQuery,n);let i=e.components;i!==null&&JD(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[M]&=-5,Dl()}}function JD(e,t){for(let n=0;n<t.length;n++)QD(e,t[n])}function $r(e,t,n,r){let o=A(null);try{let i=t.tView,a=e[M]&4096?4096:16,c=kl(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Jt]=l;let u=e[ft];return u!==null&&(c[ft]=u.createEmbeddedView(i)),Bl(i,c,n),c}finally{A(o)}}function Vn(e,t){return!t||t.firstChild===null||Tp(e)}var e_;function Vl(e,t){return e_(e,t)}var tt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(tt||{});function Hl(e){return(e.flags&32)===32}function An(e,t,n,r,o){if(r!=null){let i,s=!1;ht(r)?i=r:Mt(r)&&(s=!0,r=r[mt]);let a=Qe(r);e===0&&n!==null?o==null?tm(t,n,a):xi(t,n,a,o||null,!0):e===1&&n!==null?xi(t,n,a,o||null,!0):e===2?OD(t,a,s):e===3&&t.destroyNode(a),i!=null&&u_(t,e,i,n,o)}}function t_(e,t){am(e,t),t[mt]=null,t[De]=null}function n_(e,t,n,r,o,i){r[mt]=o,r[De]=t,cs(e,r,n,1,o,i)}function am(e,t){t[dt].changeDetectionScheduler?.notify(9),cs(e,t,t[G],2,null,null)}function r_(e){let t=e[xr];if(!t)return Fa(e[x],e);for(;t;){let n=null;if(Mt(t))n=t[xr];else{let r=t[le];r&&(n=r)}if(!n){for(;t&&!t[He]&&t!==e;)Mt(t)&&Fa(t[x],t),t=t[ue];t===null&&(t=e),Mt(t)&&Fa(t[x],t),n=t&&t[He]}t=n}}function Ul(e,t){let n=e[Ln],r=n.indexOf(t);n.splice(r,1)}function ss(e,t){if(Wn(t))return;let n=t[G];n.destroyNode&&cs(e,t,n,3,null,null),r_(t)}function Fa(e,t){if(Wn(t))return;let n=A(null);try{t[M]&=-129,t[M]|=256,t[Re]&&vr(t[Re]),i_(e,t),o_(e,t),t[x].type===1&&t[G].destroy();let r=t[Jt];if(r!==null&&ht(t[ue])){r!==t[ue]&&Ul(r,t);let o=t[ft];o!==null&&o.detachView(e)}cc(t)}finally{A(n)}}function o_(e,t){let n=e.cleanup,r=t[yi];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[yi]=null);let o=t[Ct];if(o!==null){t[Ct]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[en];if(i!==null){t[en]=null;for(let s of i)s.destroy()}}function i_(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof on)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];L(4,a,c);try{c.call(a)}finally{L(5,a,c)}}else{L(4,o,i);try{i.call(o)}finally{L(5,o,i)}}}}}function cm(e,t,n){return s_(e,t.parent,n)}function s_(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[mt];if(Gn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===et.None||o===et.Emulated)return null}return rt(r,n)}function lm(e,t,n){return c_(e,t,n)}function a_(e,t,n){return e.type&40?rt(e,n):null}var c_=a_,Yd;function as(e,t,n,r){let o=cm(e,r,t),i=t[G],s=r.parent||t[De],a=lm(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Zd(i,o,n[c],a,!1);else Zd(i,o,n,a,!1);Yd!==void 0&&Yd(i,r,t,n,o)}function Ir(e,t){if(t!==null){let n=t.type;if(n&3)return rt(t,e);if(n&4)return _c(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Ir(e,r);{let o=e[t.index];return ht(o)?_c(-1,o):Qe(o)}}else{if(n&128)return Ir(e,t.next);if(n&32)return Vl(t,e)()||Qe(e[t.index]);{let r=um(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=nn(e[Te]);return Ir(o,r)}else return Ir(e,t.next)}}}return null}function um(e,t){if(t!==null){let r=e[Te][De],o=t.projection;return r.projection[o]}return null}function _c(e,t){let n=le+e+1;if(n<t.length){let r=t[n],o=r[x].firstChild;if(o!==null)return Ir(r,o)}return t[tn]}function $l(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Kn(Qe(a),r),n.flags|=2),!Hl(n))if(c&8)$l(e,t,n.child,r,o,i,!1),An(t,e,o,a,i);else if(c&32){let l=Vl(n,r),u;for(;u=l();)An(t,e,o,u,i);An(t,e,o,a,i)}else c&16?dm(e,t,r,n,o,i):An(t,e,o,a,i);n=s?n.projectionNext:n.next}}function cs(e,t,n,r,o,i){$l(n,r,e.firstChild,t,o,i,!1)}function l_(e,t,n){let r=t[G],o=cm(e,n,t),i=n.parent||t[De],s=lm(i,n,t);dm(r,0,t,n,o,s)}function dm(e,t,n,r,o,i){let s=n[Te],c=s[De].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];An(t,e,o,u,i)}else{let l=c,u=s[ue];Tp(r)&&(l.flags|=128),$l(e,t,l,u,o,i,!0)}}function u_(e,t,n,r,o){let i=n[tn],s=Qe(n);i!==s&&An(t,e,r,i,o);for(let a=le;a<n.length;a++){let c=n[a];cs(c[x],c,e,t,r,i)}}function d_(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:tt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=tt.Important),e.setStyle(n,r,o,i))}}function Si(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Qe(i)),ht(i)&&f_(i,r);let s=n.type;if(s&8)Si(e,t,n.child,r);else if(s&32){let a=Vl(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=um(t,n);if(Array.isArray(a))r.push(...a);else{let c=nn(t[Te]);Si(c[x],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function f_(e,t){for(let n=le;n<e.length;n++){let r=e[n],o=r[x].firstChild;o!==null&&Si(r[x],r,o,t)}e[tn]!==e[mt]&&t.push(e[tn])}function fm(e){if(e[Rn]!==null){for(let t of e[Rn])t.impl.addSequence(t);e[Rn].length=0}}var pm=[];function p_(e){return e[Re]??m_(e)}function m_(e){let t=pm.pop()??Object.create(g_);return t.lView=e,t}function h_(e){e.lView[Re]!==e&&(e.lView=null,pm.push(e))}var g_=ae(k({},Tn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{qn(e.lView)},consumerOnSignalRead(){this.lView[Re]=this}});function b_(e){let t=e[Re]??Object.create(y_);return t.lView=e,t}var y_=ae(k({},Tn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=nn(e.lView);for(;t&&!mm(t[x]);)t=nn(t);t&&Kf(t)},consumerOnSignalRead(){this.lView[Re]=this}});function mm(e){return e.type!==2}function hm(e){if(e[en]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[en])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[M]&8192)}}var v_=100;function gm(e,t=!0,n=0){let o=e[dt].rendererFactory,i=!1;i||o.begin?.();try{D_(e,n)}catch(s){throw t&&KD(e,s),s}finally{i||o.end?.()}}function D_(e,t){let n=ep();try{_i(!0),Ec(e,t);let r=0;for(;Zi(e);){if(r===v_)throw new D(103,!1);r++,Ec(e,1)}}finally{_i(n)}}function __(e,t,n,r){if(Wn(t))return;let o=t[M],i=!1,s=!1;vl(t);let a=!0,c=null,l=null;i||(mm(e)?(l=p_(t),c=yr(l)):_a()===null?(a=!1,l=b_(t),c=yr(l)):t[Re]&&(vr(t[Re]),t[Re]=null));try{Yf(t),Yy(e.bindingStartIndex),n!==null&&sm(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&ci(t,f,null)}else{let f=e.preOrderHooks;f!==null&&li(t,f,0,null),Oa(t,0)}if(s||E_(t),hm(t),bm(t,0),e.contentQueries!==null&&jp(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&ci(t,f)}else{let f=e.contentHooks;f!==null&&li(t,f,1),Oa(t,1)}I_(e,t);let d=e.components;d!==null&&vm(t,d,0);let p=e.viewQuery;if(p!==null&&dc(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&ci(t,f)}else{let f=e.viewHooks;f!==null&&li(t,f,2),Oa(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Ra]){for(let f of t[Ra])f();t[Ra]=null}i||(fm(t),t[M]&=-73)}catch(u){throw i||qn(t),u}finally{l!==null&&(Yo(l,c),a&&h_(l)),Dl()}}function bm(e,t){for(let n=Np(e);n!==null;n=Ap(n))for(let r=le;r<n.length;r++){let o=n[r];ym(o,t)}}function E_(e){for(let t=Np(e);t!==null;t=Ap(t)){if(!(t[M]&2))continue;let n=t[Ln];for(let r=0;r<n.length;r++){let o=n[r];Kf(o)}}}function w_(e,t,n){L(18);let r=Xe(t,e);ym(r,n),L(19,r[re])}function ym(e,t){dl(e)&&Ec(e,t)}function Ec(e,t){let r=e[x],o=e[M],i=e[Re],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Ko(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)__(r,e,r.template,e[re]);else if(o&8192){hm(e),bm(e,1);let a=r.components;a!==null&&vm(e,a,1),fm(e)}}function vm(e,t,n){for(let r=0;r<t.length;r++)w_(e,t[r],n)}function I_(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)rn(~o);else{let i=o,s=n[++r],a=n[++r];Qy(s,i);let c=t[i];L(24,c),a(2,c),L(25,c)}}}finally{rn(-1)}}function zl(e,t){let n=ep()?64:1088;for(e[dt].changeDetectionScheduler?.notify(t);e;){e[M]|=n;let r=nn(e);if(Di(e)&&!r)return e;e=r}return null}function Dm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function _m(e,t){let n=le+t;if(n<e.length)return e[n]}function zr(e,t,n,r=!0){let o=t[x];if(C_(o,t,e,n),r){let s=_c(n,e),a=t[G],c=a.parentNode(e[tn]);c!==null&&n_(o,e[De],a,t,c,s)}let i=t[Pn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Em(e,t){let n=Ar(e,t);return n!==void 0&&ss(n[x],n),n}function Ar(e,t){if(e.length<=le)return;let n=le+t,r=e[n];if(r){let o=r[Jt];o!==null&&o!==e&&Ul(o,r),t>0&&(e[n-1][He]=r[He]);let i=gi(e,le+t);t_(r[x],r);let s=i[ft];s!==null&&s.detachView(i[x]),r[ue]=null,r[He]=null,r[M]&=-129}return r}function C_(e,t,n,r){let o=le+r,i=n.length;r>0&&(n[o-1][He]=t),r<i-le?(t[He]=n[o],Af(n,le+r,t)):(n.push(t),t[He]=null),t[ue]=n;let s=t[Jt];s!==null&&n!==s&&wm(s,t);let a=t[ft];a!==null&&a.insertView(e),Ja(t),t[M]|=128}function wm(e,t){let n=e[Ln],r=t[ue];if(Mt(r))e[M]|=2;else{let o=r[ue][Te];t[Te]!==o&&(e[M]|=2)}n===null?e[Ln]=[t]:n.push(t)}var Rr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[x];return Si(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[re]}set context(t){this._lView[re]=t}get destroyed(){return Wn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[ue];if(ht(t)){let n=t[vi],r=n?n.indexOf(this):-1;r>-1&&(Ar(t,r),gi(n,r))}this._attachedToViewContainer=!1}ss(this._lView[x],this._lView)}onDestroy(t){Qf(this._lView,t)}markForCheck(){zl(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){Ja(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,gm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Di(this._lView),n=this._lView[Jt];n!==null&&!t&&Ul(n,this._lView),am(this._lView[x],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=t;let n=Di(this._lView),r=this._lView[Jt];r!==null&&!n&&wm(r,this._lView),Ja(this._lView)}};var St=(()=>{class e{static __NG_ELEMENT_ID__=x_}return e})(),M_=St,T_=class extends M_{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=$r(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Rr(o)}};function x_(){return ls(de(),_())}function ls(e,t){return e.type&4?new T_(t,e,Yn(e,t)):null}function Jn(e,t,n,r,o){let i=e.data[t];if(i===null)i=S_(e,t,n,r,o),Ky()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Wy();i.injectorIndex=s===null?-1:s.injectorIndex}return At(i,!0),i}function S_(e,t,n,r,o){let i=Jf(),s=hl(),a=s?i:i&&i.parent,c=e.data[t]=A_(e,a,n,t,r,o);return N_(e,c,i,s),c}function N_(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function A_(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Xf()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var SO=new RegExp(`^(\\d+)*(${jv}|${Lv})*(.*)`);var R_=()=>null;function Hn(e,t){return R_(e,t)}var O_=class{},Im=class{},wc=class{resolveComponentFactory(t){throw Error(`No component factory found for ${ve(t)}.`)}},us=class{static NULL=new wc},nt=class{},Gr=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>k_()}return e})();function k_(){let e=_(),t=de(),n=Xe(t.index,e);return(Mt(n)?n:e)[G]}var F_=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>null})}return e})();var Pa={},Ic=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Vi(r);let o=this.injector.get(t,Pa,r);return o!==Pa||n===Pa?o:this.parentInjector.get(t,n,r)}};function Cc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ga(o,a);else if(i==2){let c=a,l=t[++s];r=Ga(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function W(e,t=R.Default){let n=_();if(n===null)return C(e,t);let r=de();return hp(r,n,se(e),t)}function OO(){let e="invalid";throw new Error(e)}function Gl(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=L_(s);u===null?a=s:[a,c,l]=u,V_(e,t,n,a,i,c,l)}i!==null&&r!==null&&P_(n,r,i)}function P_(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new D(-301,!1);r.push(t[o],i)}}function L_(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&Ke(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,j_(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function j_(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function B_(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function V_(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&Ke(f)&&(c=!0,B_(e,n,p)),rc(Ci(n,t),e,f.type)}W_(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,u=!1,d=om(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=jn(n.mergedAttrs,f.hostAttrs),U_(e,n,t,d,f),G_(d,f,o),s!==null&&s.has(f)){let[g,v]=s.get(f);n.directiveToIndex.set(f.type,[d,g+n.directiveStart,v+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}H_(e,n,i)}function H_(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Kd(0,t,o,r),Kd(1,t,o,r),Xd(t,r,!1);else{let i=n.get(o);Qd(0,t,i,r),Qd(1,t,i,r),Xd(t,r,!0)}}}function Kd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Cm(t,i)}}function Qd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Cm(t,s)}}function Cm(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Xd(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Rl(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function U_(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Qt(o.type,!0)),s=new on(i,Ke(o),W);e.blueprint[r]=s,n[r]=s,$_(e,t,r,om(e,n,o.hostVars,_e),o)}function $_(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;z_(s)!=a&&s.push(a),s.push(n,r,i)}}function z_(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function G_(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ke(t)&&(n[""]=e)}}function W_(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Mm(e,t,n,r,o,i,s,a){let c=t.consts,l=xt(c,s),u=Jn(t,e,2,r,l);return i&&Gl(t,n,u,xt(c,a),o),u.mergedAttrs=jn(u.mergedAttrs,u.attrs),u.attrs!==null&&Cc(u,u.attrs,!1),u.mergedAttrs!==null&&Cc(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Tm(e,t){_l(e,t),ll(t)&&e.queries.elementEnd(t)}var Ni=class extends us{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Tt(t);return new an(n,this.ngModule)}};function q_(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&rs.SignalBased)!==0};return o&&(i.transform=o),i})}function Z_(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Y_(e,t,n){let r=t instanceof Oe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Ic(n,r):n}function K_(e){let t=e.get(nt,null);if(t===null)throw new D(407,!1);let n=e.get(F_,null),r=e.get(sn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function Q_(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return em(t,n,n==="svg"?qf:n==="math"?Fy:null)}var an=class extends Im{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=q_(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Z_(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=xD(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){L(22);let i=A(null);try{let s=this.componentDef,a=r?["ng-version","19.2.9"]:SD(this.componentDef.selectors[0]),c=Ol(0,null,null,1,0,null,null,null,null,[a],null),l=Y_(s,o||this.ngModule,t),u=K_(l),d=u.rendererFactory.createRenderer(null,s),p=r?BD(d,r,s.encapsulation,l):Q_(s,d),f=kl(null,c,null,512|rm(s),null,null,u,d,l,null,Lp(p,l,!0));f[K]=p,vl(f);let h=null;try{let g=Mm(K,c,f,"#host",()=>[this.componentDef],!0,0);p&&(nm(d,p,g),Kn(p,f)),os(c,f,g),Al(c,g,f),Tm(c,g),n!==void 0&&X_(g,this.ngContentSelectors,n),h=Xe(g.index,f),f[re]=h[re],Bl(c,f,null)}catch(g){throw h!==null&&cc(h),cc(f),g}finally{L(23),Dl()}return new Mc(this.componentType,f)}finally{A(i)}}},Mc=class extends O_{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=ul(n[x],K),this.location=Yn(this._tNode,n),this.instance=Xe(this._tNode.index,n)[re],this.hostView=this.changeDetectorRef=new Rr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=jl(r,o[x],o,t,n);this.previousInputValues.set(t,n);let s=Xe(r.index,o);zl(s,1)}get injector(){return new Kt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function X_(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var kt=(()=>{class e{static __NG_ELEMENT_ID__=J_}return e})();function J_(){let e=de();return Sm(e,_())}var eE=kt,xm=class extends eE{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Yn(this._hostTNode,this._hostLView)}get injector(){return new Kt(this._hostTNode,this._hostLView)}get parentInjector(){let t=El(this._hostTNode,this._hostLView);if(lp(t)){let n=wi(t,this._hostLView),r=Ei(t),o=n[x].data[r+8];return new Kt(o,n)}else return new Kt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Jd(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-le}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Hn(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Vn(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Ay(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new an(Tt(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let g=(s?l:this.parentInjector).get(Oe,null);g&&(i=g)}let u=Tt(c.componentType??{}),d=Hn(this._lContainer,u?.id??null),p=d?.firstChild??null,f=c.create(l,o,p,i);return this.insertImpl(f.hostView,a,Vn(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Ly(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[ue],l=new xm(c,c[De],c[ue]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return zr(s,o,i,r),t.attachToViewContainerRef(),Af(La(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Jd(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Ar(this._lContainer,n);r&&(gi(La(this._lContainer),n),ss(r[x],r))}detach(t){let n=this._adjustIndex(t,-1),r=Ar(this._lContainer,n);return r&&gi(La(this._lContainer),n)!=null?new Rr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Jd(e){return e[vi]}function La(e){return e[vi]||(e[vi]=[])}function Sm(e,t){let n,r=t[e.index];return ht(r)?n=r:(n=Dm(r,t,null,e),t[e.index]=n,Fl(t,n)),nE(n,t,e,r),new xm(n,e,t)}function tE(e,t){let n=e[G],r=n.createComment(""),o=rt(t,e),i=n.parentNode(o);return xi(n,i,r,n.nextSibling(o),!1),r}var nE=iE,rE=()=>!1;function oE(e,t,n){return rE(e,t,n)}function iE(e,t,n,r){if(e[tn])return;let o;n.type&8?o=Qe(r):o=tE(t,n),e[tn]=o}var Tc=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},xc=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)ql(t,n).matches!==null&&this.queries[n].setDirty()}},Ai=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=dE(t):this.predicate=t}},Sc=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Nc=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,sE(n,i)),this.matchTNodeWithReadOption(t,n,ui(n,t,i,!1,!1))}else r===St?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,ui(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===ee||o===kt||o===St&&n.type&4)this.addMatch(n.index,-2);else{let i=ui(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function sE(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function aE(e,t){return e.type&11?Yn(e,t):e.type&4?ls(e,t):null}function cE(e,t,n,r){return n===-1?aE(t,e):n===-2?lE(e,t,r):Sr(e,e[x],n,t)}function lE(e,t,n){if(n===ee)return Yn(t,e);if(n===St)return ls(t,e);if(n===kt)return Sm(t,e)}function Nm(e,t,n,r){let o=t[ft].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(cE(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Ac(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Nm(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let d=le;d<u.length;d++){let p=u[d];p[Jt]===p[ue]&&Ac(p[x],p,l,r)}if(u[Ln]!==null){let d=u[Ln];for(let p=0;p<d.length;p++){let f=d[p];Ac(f[x],f,l,r)}}}}}return r}function Wl(e,t){return e[ft].queries[t].queryList}function Am(e,t,n){let r=new Bn((n&4)===4);return Vy(e,t,r,r.destroy),(t[ft]??=new xc).queries.push(new Tc(r))-1}function uE(e,t,n){let r=$();return r.firstCreatePass&&(Om(r,new Ai(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Am(r,_(),t)}function Rm(e,t,n,r){let o=$();if(o.firstCreatePass){let i=de();Om(o,new Ai(t,n,r),i.index),fE(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Am(o,_(),n)}function dE(e){return e.split(",").map(t=>t.trim())}function Om(e,t,n){e.queries===null&&(e.queries=new Sc),e.queries.track(new Nc(t,n))}function fE(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function ql(e,t){return e.queries.getByIndex(t)}function km(e,t){let n=e[x],r=ql(n,t);return r.crossesNgTemplate?Ac(n,e,t,[]):Nm(n,e,r,t)}function Fm(e,t,n){let r,o=Jo(()=>{r._dirtyCounter();let i=gE(r,e);if(t&&i===void 0)throw new D(-951,!1);return i});return r=o[ge],r._dirtyCounter=Ml(0),r._flatValue=void 0,o}function pE(e){return Fm(!0,!1,e)}function mE(e){return Fm(!0,!0,e)}function hE(e,t){let n=e[ge];n._lView=_(),n._queryIndex=t,n._queryList=Wl(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function gE(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[M]&4)return t?void 0:he;let o=Wl(n,r),i=km(n,r);return o.reset(i,Cp),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function ef(e,t){return pE(t)}function bE(e,t){return mE(t)}var FO=(ef.required=bE,ef);var Un=class{},yE=class{};var Rc=class extends Un{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ni(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=kf(t);this._bootstrapComponents=Qp(i.bootstrap),this._r3Injector=vp(t,n,[{provide:Un,useValue:this},{provide:us,useValue:this.componentFactoryResolver},...r],ve(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Oc=class extends yE{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Rc(this.moduleType,t,[])}};var Ri=class extends Un{injector;componentFactoryResolver=new Ni(this);instance=null;constructor(t){super();let n=new Tr([...t.providers,{provide:Un,useValue:this},{provide:us,useValue:this.componentFactoryResolver}],t.parent||$i(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function vE(e,t,n=null){return new Ri({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var DE=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Pf(!1,n.type),o=r.length>0?vE([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=b({token:e,providedIn:"environment",factory:()=>new e(C(Oe))})}return e})();function Ee(e){return Pr(()=>{let t=Pm(e),n=ae(k({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===xp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(DE).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||et.Emulated,styles:e.styles||he,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&dn("NgStandalone"),Lm(n);let r=e.dependencies;return n.directiveDefs=tf(r,!1),n.pipeDefs=tf(r,!0),n.id=CE(n),n})}function _E(e){return Tt(e)||Ff(e)}function EE(e){return e!==null}function ie(e){return Pr(()=>({type:e.type,bootstrap:e.bootstrap||he,declarations:e.declarations||he,imports:e.imports||he,exports:e.exports||he,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function wE(e,t){if(e==null)return Ye;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=rs.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function IE(e){if(e==null)return Ye;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function q(e){return Pr(()=>{let t=Pm(e);return Lm(t),t})}function Wr(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Pm(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Ye,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||he,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:wE(e.inputs,t),outputs:IE(e.outputs),debugInfo:null}}function Lm(e){e.features?.forEach(t=>t(e))}function tf(e,t){if(!e)return null;let n=t?Dy:_E;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(EE)}function CE(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function ME(e){return Object.getPrototypeOf(e.prototype).constructor}function er(e){let t=ME(e.type),n=!0,r=[e];for(;t;){let o;if(Ke(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new D(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ja(e.inputs),s.declaredInputs=ja(e.declaredInputs),s.outputs=ja(e.outputs);let a=o.hostBindings;a&&AE(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&SE(e,c),l&&NE(e,l),TE(e,o),Xb(e.outputs,o.outputs),Ke(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===er&&(n=!1)}}t=Object.getPrototypeOf(t)}xE(r)}function TE(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function xE(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=jn(o.hostAttrs,n=jn(n,o.hostAttrs))}}function ja(e){return e===Ye?{}:e===he?[]:e}function SE(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function NE(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function AE(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function jO(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.findHostDirectiveDefs=jm,n.hostDirectives=r?e.map(kc):[e]):r?n.hostDirectives.unshift(...e.map(kc)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function jm(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)nf(kc(i),t,n)}else nf(r,t,n)}function nf(e,t,n){let r=Ff(e.directive);RE(r.declaredInputs,e.inputs),jm(r,t,n),n.set(r,e),t.push(r)}function kc(e){return typeof e=="function"?{directive:se(e),inputs:Ye,outputs:Ye}:{directive:se(e.directive),inputs:rf(e.inputs),outputs:rf(e.outputs)}}function rf(e){if(e===void 0||e.length===0)return Ye;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function RE(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function Bm(e){return Zl(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function OE(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Zl(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function qr(e,t,n){return e[t]=n}function kE(e,t){return e[t]}function xe(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Or(e,t,n,r){let o=xe(e,t,n);return xe(e,t+1,r)||o}function FE(e,t,n,r,o){let i=Or(e,t,n,r);return xe(e,t+2,o)||i}function PE(e,t,n,r,o,i){let s=Or(e,t,n,r);return Or(e,t+2,o,i)||s}function LE(e,t,n,r,o,i,s,a,c){let l=t.consts,u=Jn(t,e,4,s||null,a||null);ml()&&Gl(t,n,u,xt(l,c),Ll),u.mergedAttrs=jn(u.mergedAttrs,u.attrs),_l(t,u);let d=u.tView=Ol(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function Oi(e,t,n,r,o,i,s,a,c,l){let u=n+K,d=t.firstCreatePass?LE(u,t,e,r,o,i,s,a,c):t.data[u];At(d,!1);let p=BE(t,e,d,n);Ki()&&as(t,e,p,d),Kn(p,e);let f=Dm(p,e,p,d);return e[u]=f,Fl(e,f),oE(f,d,e),Wi(d)&&os(t,e,d),c!=null&&Pl(e,d,l),d}function jE(e,t,n,r,o,i,s,a){let c=_(),l=$(),u=xt(l.consts,i);return Oi(c,l,e,t,n,r,o,u,s,a),jE}var BE=VE;function VE(e,t,n,r){return Qi(!0),t[G].createComment("")}var HE=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Vm=new y("");var Hm=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>new Fc})}return e})(),Fc=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function ds(e){return!!e&&typeof e.then=="function"}function Yl(e){return!!e&&typeof e.subscribe=="function"}var Um=new y("");function BO(e){return jr([{provide:Um,multi:!0,useValue:e}])}var $m=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=m(Um,{optional:!0})??[];injector=m(J);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=zi(this.injector,o);if(ds(i))n.push(i);else if(Yl(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),UE=new y("");function $E(){Ca(()=>{throw new D(600,!1)})}function zE(e){return e.isBoundToModule}var GE=10;var Nt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=m(Tv);afterRenderManager=m(Fp);zonelessEnabled=m(Ji);rootEffectScheduler=m(Hm);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new B;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=m(Zn).hasPendingTasks.pipe(X(n=>!n));constructor(){m(Xn,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=m(Oe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=J.NULL){L(10);let i=n instanceof Im;if(!this._injector.get($m).done){let f="";throw new D(405,f)}let a;i?a=n:a=this._injector.get(us).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=zE(a)?void 0:this._injector.get(Un),l=r||a.selector,u=a.create(o,[],l,c),d=u.location.nativeElement,p=u.injector.get(Vm,null);return p?.registerApplication(d),u.onDestroy(()=>{this.detachView(u.hostView),di(this.components,u),p?.unregisterApplication(d)}),this._loadComponent(u),L(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){L(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(xl.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new D(101,!1);let n=A(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,A(n),this.afterTick.next(),L(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(nt,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<GE;)L(14),this.synchronizeOnce(),L(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)WE(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Zi(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;di(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(UE,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>di(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new D(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function di(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function WE(e,t,n,r){if(!n&&!Zi(e))return;gm(e,t,n&&!r?0:1)}function fn(e,t,n,r){let o=_(),i=un();if(xe(o,i,t)){let s=$(),a=Vr();qD(a,o,e,t,n,r)}return fn}function Kl(e,t,n,r){return xe(e,un(),n)?t+kn(n)+r:_e}function qE(e,t,n,r,o,i){let s=Zy(),a=Or(e,s,n,o);return bl(2),a?t+kn(n)+r+kn(o)+i:_e}function si(e,t){return e<<17|t<<2}function cn(e){return e>>17&32767}function ZE(e){return(e&2)==2}function YE(e,t){return e&131071|t<<17}function Pc(e){return e|2}function $n(e){return(e&131068)>>2}function Ba(e,t){return e&-131069|t<<2}function KE(e){return(e&1)===1}function Lc(e){return e|1}function QE(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=cn(s),c=$n(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Lr(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=cn(e[a+1]);e[r+1]=si(p,a),p!==0&&(e[p+1]=Ba(e[p+1],r)),e[a+1]=YE(e[a+1],r)}else e[r+1]=si(a,0),a!==0&&(e[a+1]=Ba(e[a+1],r)),a=r;else e[r+1]=si(c,0),a===0?a=r:e[c+1]=Ba(e[c+1],r),c=r;l&&(e[r+1]=Pc(e[r+1])),of(e,u,r,!0),of(e,u,r,!1),XE(t,u,e,r,i),s=si(a,c),i?t.classBindings=s:t.styleBindings=s}function XE(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Lr(i,t)>=0&&(n[r+1]=Lc(n[r+1]))}function of(e,t,n,r){let o=e[n+1],i=t===null,s=r?cn(o):$n(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];JE(c,t)&&(a=!0,e[s+1]=r?Lc(l):Pc(l)),s=r?cn(l):$n(l)}a&&(e[n+1]=r?Pc(o):Lc(o))}function JE(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Lr(e,t)>=0:!1}var Ve={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function ew(e){return e.substring(Ve.key,Ve.keyEnd)}function tw(e){return nw(e),zm(e,Gm(e,0,Ve.textEnd))}function zm(e,t){let n=Ve.textEnd;return n===t?-1:(t=Ve.keyEnd=rw(e,Ve.key=t,n),Gm(e,t,n))}function nw(e){Ve.key=0,Ve.keyEnd=0,Ve.value=0,Ve.valueEnd=0,Ve.textEnd=e.length}function Gm(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function rw(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function ow(e,t,n){let r=_(),o=un();if(xe(r,o,t)){let i=$(),s=Vr();is(i,s,r,e,t,r[G],n,!1)}return ow}function jc(e,t,n,r,o){jl(t,e,n,o?"class":"style",r)}function iw(e,t,n){return qm(e,t,n,!1),iw}function Se(e,t){return qm(e,t,null,!0),Se}function Zr(e){Zm(dw,Wm,e,!0)}function Wm(e,t){for(let n=tw(t);n>=0;n=zm(t,n))Hi(e,ew(t),!0)}function qm(e,t,n,r){let o=_(),i=$(),s=bl(2);if(i.firstUpdatePass&&Km(i,e,s,r),t!==_e&&xe(o,s,t)){let a=i.data[gt()];Qm(i,a,o,o[G],e,o[s+1]=pw(t,n),r,s)}}function Zm(e,t,n,r){let o=$(),i=bl(2);o.firstUpdatePass&&Km(o,null,i,r);let s=_();if(n!==_e&&xe(s,i,n)){let a=o.data[gt()];if(Xm(a,r)&&!Ym(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Ga(c,n||"")),jc(o,a,s,n,r)}else fw(o,a,s,s[G],s[i+1],s[i+1]=uw(e,t,n),r,i)}}function Ym(e,t){return t>=e.expandoStartIndex}function Km(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[gt()],s=Ym(e,n);Xm(i,r)&&t===null&&!s&&(t=!1),t=sw(o,i,t,r),QE(o,i,t,n,s,r)}}function sw(e,t,n,r){let o=Jy(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Va(null,e,t,n,r),n=kr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Va(o,e,t,n,r),i===null){let c=aw(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Va(null,e,t,c[1],r),c=kr(c,t.attrs,r),cw(e,t,r,c))}else i=lw(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function aw(e,t,n){let r=n?t.classBindings:t.styleBindings;if($n(r)!==0)return e[cn(r)]}function cw(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[cn(o)]=r}function lw(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=kr(r,s,n)}return kr(r,t.attrs,n)}function Va(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=kr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function kr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Hi(e,s,n?!0:t[++i]))}return e===void 0?null:e}function uw(e,t,n){if(n==null||n==="")return he;let r=[],o=Ue(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function dw(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Hi(e,r,n)}function fw(e,t,n,r,o,i,s,a){o===_e&&(o=he);let c=0,l=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;u!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=l<i.length?i[l+1]:void 0,h=null,g;u===d?(c+=2,l+=2,p!==f&&(h=d,g=f)):d===null||u!==null&&u<d?(c+=2,h=u):(l+=2,h=d,g=f),h!==null&&Qm(e,t,n,r,h,g,s,a),u=c<o.length?o[c]:null,d=l<i.length?i[l]:null}}function Qm(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=KE(l)?sf(c,t,n,o,$n(l),s):void 0;if(!ki(u)){ki(i)||ZE(l)&&(i=sf(c,null,n,o,a,s));let d=Zf(gt(),n);d_(r,s,d,o,i)}}function sf(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,p=n[o+1];p===_e&&(p=d?he:void 0);let f=d?Na(p,r):u===r?p:void 0;if(l&&!ki(f)&&(f=Na(c,r)),ki(f)&&(a=f,s))return a;let h=e[o+1];o=s?cn(h):$n(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Na(c,r))}return a}function ki(e){return e!==void 0}function pw(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=ve(Ue(e)))),e}function Xm(e,t){return(e.flags&(t?8:16))!==0}function VO(e,t,n){let r=_(),o=Kl(r,e,t,n);Zm(Hi,Wm,o,!0)}var Bc=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Ha(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function mw(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],d=Ha(i,l,i,u,n);if(d!==0){d<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),f=t[c],h=Ha(s,p,c,f,n);if(h!==0){h<0&&e.updateValue(s,f),s--,c--;continue}let g=n(i,l),v=n(s,p),w=n(i,u);if(Object.is(w,v)){let U=n(c,f);Object.is(U,g)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new Fi,o??=cf(e,i,s,n),Vc(e,r,i,w))e.updateValue(i,u),i++,s++;else if(o.has(w))r.set(g,e.detach(i)),s--;else{let U=e.create(i,t[i]);e.attach(i,U),i++,s++}}for(;i<=c;)af(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),d=l.value,p=Ha(i,u,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,l=c.next();else{r??=new Fi,o??=cf(e,i,s,n);let f=n(i,d);if(Vc(e,r,i,f))e.updateValue(i,d),i++,s++,l=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)af(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Vc(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function af(e,t,n,r,o){if(Vc(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function cf(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Fi=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function HO(e,t){dn("NgControlFlow");let n=_(),r=un(),o=n[r]!==_e?n[r]:-1,i=o!==-1?Pi(n,K+o):void 0,s=0;if(xe(n,r,e)){let a=A(null);try{if(i!==void 0&&Em(i,s),e!==-1){let c=K+e,l=Pi(n,c),u=zc(n[x],c),d=Hn(l,u.tView.ssrId),p=$r(n,u,t,{dehydratedView:d});zr(l,p,s,Vn(u,d))}}finally{A(a)}}else if(i!==void 0){let a=_m(i,s);a!==void 0&&(a[re]=t)}}var Hc=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-le}};function UO(e,t){return t}var Uc=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function $O(e,t,n,r,o,i,s,a,c,l,u,d,p){dn("NgControlFlow");let f=_(),h=$(),g=c!==void 0,v=_(),w=a?s.bind(v[Te][re]):s,U=new Uc(g,w);v[K+e]=U,Oi(f,h,e+1,t,n,r,o,xt(h.consts,i)),g&&Oi(f,h,e+2,c,l,u,d,xt(h.consts,p))}var $c=class extends Bc{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-le}at(t){return this.getLView(t)[re].$implicit}attach(t,n){let r=n[Pn];this.needsIndexUpdate||=t!==this.length,zr(this.lContainer,n,t,Vn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,hw(this.lContainer,t)}create(t,n){let r=Hn(this.lContainer,this.templateTNode.tView.ssrId),o=$r(this.hostLView,this.templateTNode,new Hc(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){ss(t[x],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[re].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[re].$index=t}getLView(t){return gw(this.lContainer,t)}};function zO(e){let t=A(null),n=gt();try{let r=_(),o=r[x],i=r[n],s=n+1,a=Pi(r,s);if(i.liveCollection===void 0){let l=zc(o,s);i.liveCollection=new $c(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(mw(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=un(),u=c.length===0;if(xe(r,l,u)){let d=n+2,p=Pi(r,d);if(u){let f=zc(o,d),h=Hn(p,f.tView.ssrId),g=$r(r,f,void 0,{dehydratedView:h});zr(p,g,0,Vn(f,h))}else Em(p,0)}}}finally{A(t)}}function Pi(e,t){return e[t]}function hw(e,t){return Ar(e,t)}function gw(e,t){return _m(e,t)}function zc(e,t){return ul(e,t)}function pn(e,t,n,r){let o=_(),i=$(),s=K+e,a=o[G],c=i.firstCreatePass?Mm(s,i,o,t,Ll,ml(),n,r):i.data[s],l=bw(i,o,c,a,t,e);o[s]=l;let u=Wi(c);return At(c,!0),nm(a,l,c),!Hl(c)&&Ki()&&as(i,o,l,c),(Hy()===0||u)&&Kn(l,o),Uy(),u&&(os(i,o,c),Al(i,c,o)),r!==null&&Pl(o,c),pn}function mn(){let e=de();hl()?gl():(e=e.parent,At(e,!1));let t=e;zy(t)&&Gy(),$y();let n=$();return n.firstCreatePass&&Tm(n,t),t.classesWithoutHost!=null&&sv(t)&&jc(n,t,_(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&av(t)&&jc(n,t,_(),t.stylesWithoutHost,!1),mn}function yt(e,t,n,r){return pn(e,t,n,r),mn(),yt}var bw=(e,t,n,r,o,i)=>(Qi(!0),em(r,o,rv()));function yw(e,t,n,r,o){let i=t.consts,s=xt(i,r),a=Jn(t,e,8,"ng-container",s);s!==null&&Cc(a,s,!0);let c=xt(i,o);return ml()&&Gl(t,n,a,c,Ll),a.mergedAttrs=jn(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function Jm(e,t,n){let r=_(),o=$(),i=e+K,s=o.firstCreatePass?yw(i,o,r,t,n):o.data[i];At(s,!0);let a=Dw(o,r,s,e);return r[i]=a,Ki()&&as(o,r,a,s),Kn(a,r),Wi(s)&&(os(o,r,s),Al(o,s,r)),n!=null&&Pl(r,s),Jm}function eh(){let e=de(),t=$();return hl()?gl():(e=e.parent,At(e,!1)),t.firstCreatePass&&(_l(t,e),ll(e)&&t.queries.elementEnd(e)),eh}function vw(e,t,n){return Jm(e,t,n),eh(),vw}var Dw=(e,t,n,r)=>(Qi(!0),RD(t[G],""));function GO(){return _()}function _w(e,t,n){let r=_(),o=un();if(xe(r,o,t)){let i=$(),s=Vr();is(i,s,r,e,t,r[G],n,!0)}return _w}var Zt=void 0;function Ew(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var ww=["en",[["a","p"],["AM","PM"],Zt],[["AM","PM"],Zt,Zt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Zt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Zt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Zt,"{1} 'at' {0}",Zt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Ew],Ua={};function Ne(e){let t=Iw(e),n=lf(t);if(n)return n;let r=t.split("-")[0];if(n=lf(r),n)return n;if(r==="en")return ww;throw new D(701,!1)}function lf(e){return e in Ua||(Ua[e]=qe.ng&&qe.ng.common&&qe.ng.common.locales&&qe.ng.common.locales[e]),Ua[e]}var Q=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(Q||{});function Iw(e){return e.toLowerCase().replace(/_/g,"-")}var Li="en-US";var Cw=Li;function Mw(e){typeof e=="string"&&(Cw=e.toLowerCase().replace(/_/g,"-"))}function uf(e,t,n){return function r(o){if(o===Function)return n;let i=Gn(e)?Xe(e.index,t):t;zl(i,5);let s=t[re],a=df(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=df(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function df(e,t,n,r){let o=A(null);try{return L(6,t,n),n(r)!==!1}catch(i){return Tw(e,i),!1}finally{L(7,t,n),A(o)}}function Tw(e,t){let n=e[Xt],r=n?n.get(Je,null):null;r&&r.handleError(t)}function ff(e,t,n,r,o,i){let s=t[n],a=t[x],l=a.data[n].outputs[r],u=s[l],d=a.firstCreatePass?pl(a):null,p=fl(t),f=u.subscribe(i),h=p.length;p.push(i,f),d&&d.push(o,e.index,h,-(h+1))}var xw=new Map;function Sw(e,t,n,r){let o=_(),i=$(),s=de();return th(i,o,o[G],s,e,t,r),Sw}function Nw(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[yi],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function th(e,t,n,r,o,i,s){let a=Wi(r),l=e.firstCreatePass?pl(e):null,u=fl(t),d=!0;if(r.type&3||s){let p=rt(r,t),f=s?s(p):p,h=u.length,g=s?w=>s(Qe(w[r.index])):r.index,v=null;if(!s&&a&&(v=Nw(e,t,o,r.index)),v!==null){let w=v.__ngLastListenerFn__||v;w.__ngNextListenerFn__=i,v.__ngLastListenerFn__=i,d=!1}else{i=uf(r,t,i);let w=t[Xt].get(Rt);xw.get(w)?.(f,o,i);let Z=n.listen(f,o,i);u.push(i,Z),l&&l.push(o,g,h,h+1)}}else i=uf(r,t,i);if(d){let p=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let h=0;h<f.length;h+=2){let g=f[h],v=f[h+1];ff(r,t,g,v,o,i)}if(p&&p.length)for(let h of p)ff(r,t,h,o,o,i)}}function WO(e=1){return tv(e)}function Aw(e,t){let n=null,r=wD(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Jp(e,i,!0):MD(r,i))return o}return n}function Ft(e){let t=_()[Te][De];if(!t.projection){let n=e?e.length:1,r=t.projection=by(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Aw(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function we(e,t=0,n,r,o,i){let s=_(),a=$(),c=r?e+1:null;c!==null&&Oi(s,a,c,r,o,i,null,n);let l=Jn(a,K+e,16,null,n||null);l.projection===null&&(l.projection=t),gl();let d=!s[Pn]||Xf();s[Te][De].projection[l.projection]===null&&c!==null?Rw(s,a,c):d&&!Hl(l)&&l_(a,s,l)}function Rw(e,t,n){let r=K+n,o=t.data[r],i=e[r],s=Hn(i,o.tView.ssrId),a=$r(e,o,void 0,{dehydratedView:s});zr(i,a,0,Vn(o,s))}function Ow(e,t,n,r,o){let i=_(),s=Kl(i,t,n,r);if(s!==_e){let a=$(),c=Vr();is(a,c,i,e,s,i[G],o,!1)}return Ow}function qO(e,t,n,r){Rm(e,t,n,r)}function ZO(e,t,n){uE(e,t,n)}function YO(e){let t=_(),n=$(),r=yl();Yi(r+1);let o=ql(n,r);if(e.dirty&&Py(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=km(t,r);e.reset(i,Cp),e.notifyOnChanges()}return!0}return!1}function KO(){return Wl(_(),yl())}function QO(e,t,n,r,o){hE(t,Rm(e,n,r,o))}function XO(e=1){Yi(yl()+e)}function nh(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function JO(e){let t=qy();return Br(t,K+e)}function ek(e,t=""){let n=_(),r=$(),o=e+K,i=r.firstCreatePass?Jn(r,o,1,t,null):r.data[o],s=kw(r,n,i,t,e);n[o]=s,Ki()&&as(r,n,s,i),At(i,!1)}var kw=(e,t,n,r,o)=>(Qi(!0),ND(t[G],r));function Fw(e){return rh("",e,""),Fw}function rh(e,t,n){let r=_(),o=Kl(r,e,t,n);return o!==_e&&oh(r,gt(),o),rh}function Pw(e,t,n,r,o){let i=_(),s=qE(i,e,t,n,r,o);return s!==_e&&oh(i,gt(),s),Pw}function oh(e,t,n){let r=Zf(t,e);AD(e[G],r,n)}function Lw(e,t,n){Mp(t)&&(t=t());let r=_(),o=un();if(xe(r,o,t)){let i=$(),s=Vr();is(i,s,r,e,t,r[G],n,!1)}return Lw}function tk(e,t){let n=Mp(e);return n&&e.set(t),n}function jw(e,t){let n=_(),r=$(),o=de();return th(r,n,n[G],o,e,t),jw}var Bw={};function Vw(e){let t=$(),n=_(),r=e+K,o=Jn(t,r,128,null,null);return At(o,!1),nh(t,n,r,Bw),Vw}function Hw(e,t,n){let r=$();if(r.firstCreatePass){let o=Ke(e);Gc(n,r.data,r.blueprint,o,!0),Gc(t,r.data,r.blueprint,o,!1)}}function Gc(e,t,n,r,o){if(e=se(e),Array.isArray(e))for(let i=0;i<e.length;i++)Gc(e[i],t,n,r,o);else{let i=$(),s=_(),a=de(),c=Fn(e)?e:se(e.provide),l=Bf(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Fn(e)||!e.multi){let f=new on(l,o,W),h=za(c,t,o?u:u+p,d);h===-1?(rc(Ci(a,s),i,c),$a(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=za(c,t,u+p,d),h=za(c,t,u,u+p),g=f>=0&&n[f],v=h>=0&&n[h];if(o&&!v||!o&&!g){rc(Ci(a,s),i,c);let w=zw(o?$w:Uw,n.length,o,r,l);!o&&v&&(n[h].providerFactory=w),$a(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(w),s.push(w)}else{let w=ih(n[o?h:f],l,!o&&r);$a(i,e,f>-1?f:h,w)}!o&&r&&v&&n[h].componentProviders++}}}function $a(e,t,n,r){let o=Fn(t),i=Cy(t);if(o||i){let c=(i?se(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function ih(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function za(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Uw(e,t,n,r){return Wc(this.multi,[])}function $w(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Sr(n,n[x],this.providerFactory.index,r);i=a.slice(0,s),Wc(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Wc(o,i);return i}function Wc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function zw(e,t,n,r,o){let i=new on(e,n,W);return i.multi=[],i.index=t,i.componentProviders=0,ih(i,o,r&&!n),i}function Gw(e,t=[]){return n=>{n.providersResolver=(r,o)=>Hw(r,o?o(e):e,t)}}function nk(e,t,n){let r=ln()+e,o=_();return o[r]===_e?qr(o,r,n?t.call(n):t()):kE(o,r)}function rk(e,t,n,r){return sh(_(),ln(),e,t,n,r)}function ok(e,t,n,r,o){return ah(_(),ln(),e,t,n,r,o)}function fs(e,t){let n=e[t];return n===_e?void 0:n}function sh(e,t,n,r,o,i){let s=t+n;return xe(e,s,o)?qr(e,s+1,i?r.call(i,o):r(o)):fs(e,s+1)}function ah(e,t,n,r,o,i,s){let a=t+n;return Or(e,a,o,i)?qr(e,a+2,s?r.call(s,o,i):r(o,i)):fs(e,a+2)}function Ww(e,t,n,r,o,i,s,a){let c=t+n;return FE(e,c,o,i,s)?qr(e,c+3,a?r.call(a,o,i,s):r(o,i,s)):fs(e,c+3)}function qw(e,t,n,r,o,i,s,a,c){let l=t+n;return PE(e,l,o,i,s,a)?qr(e,l+4,c?r.call(c,o,i,s,a):r(o,i,s,a)):fs(e,l+4)}function ik(e,t){let n=$(),r,o=e+K;n.firstCreatePass?(r=Zw(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Qt(r.type,!0)),s,a=be(W);try{let c=Ii(!1),l=i();return Ii(c),nh(n,_(),o,l),l}finally{be(a)}}function Zw(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function sk(e,t,n){let r=e+K,o=_(),i=Br(o,r);return ps(o,r)?sh(o,ln(),t,i.transform,n,i):i.transform(n)}function ak(e,t,n,r){let o=e+K,i=_(),s=Br(i,o);return ps(i,o)?ah(i,ln(),t,s.transform,n,r,s):s.transform(n,r)}function ck(e,t,n,r,o){let i=e+K,s=_(),a=Br(s,i);return ps(s,i)?Ww(s,ln(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function lk(e,t,n,r,o,i){let s=e+K,a=_(),c=Br(a,s);return ps(a,s)?qw(a,ln(),t,c.transform,n,r,o,i,c):c.transform(n,r,o,i)}function ps(e,t){return e[x].data[t].pure}function uk(e,t){return ls(e,t)}var Fr=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},Ql=new Fr("19.2.9"),qc=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},dk=(()=>{class e{compileModuleSync(n){return new Oc(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=kf(n),i=Qp(o.declarations).reduce((s,a)=>{let c=Tt(a);return c&&s.push(new an(c)),s},[]);return new qc(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Yw=(()=>{class e{zone=m(O);changeDetectionScheduler=m(sn);applicationRef=m(Nt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Kw=new y("",{factory:()=>!1});function ch({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new O(ae(k({},lh()),{scheduleInRootZone:n})),[{provide:O,useFactory:e},{provide:Mr,multi:!0,useFactory:()=>{let r=m(Yw,{optional:!0});return()=>r.initialize()}},{provide:Mr,multi:!0,useFactory:()=>{let r=m(Qw);return()=>{r.initialize()}}},t===!0?{provide:_p,useValue:!0}:[],{provide:Ep,useValue:n??Dp}]}function fk(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=ch({ngZoneFactory:()=>{let o=lh(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&dn("NgZone_CoalesceEvent"),new O(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return jr([{provide:Kw,useValue:!0},{provide:Ji,useValue:!1},r])}function lh(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Qw=(()=>{class e{subscription=new Y;initialized=!1;zone=m(O);pendingTasks=m(Zn);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{O.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{O.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Xw=(()=>{class e{appRef=m(Nt);taskService=m(Zn);ngZone=m(O);zonelessEnabled=m(Ji);tracing=m(Xn,{optional:!0});disableScheduling=m(_p,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new Y;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ti):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(m(Ep,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ac||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Pd:wp;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ti+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Pd(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Jw(){return typeof $localize<"u"&&$localize.locale||Li}var Yr=new y("",{providedIn:"root",factory:()=>m(Yr,R.Optional|R.SkipSelf)||Jw()});var Zc=new y(""),eI=new y("");function Er(e){return!e.moduleRef}function tI(e){let t=Er(e)?e.r3Injector:e.moduleRef.injector,n=t.get(O);return n.run(()=>{Er(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Je,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Er(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Zc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Zc);s.add(i),e.moduleRef.onDestroy(()=>{di(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return rI(r,n,()=>{let i=t.get($m);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Yr,Li);if(Mw(s||Li),!t.get(eI,!0))return Er(e)?t.get(Nt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Er(e)){let c=t.get(Nt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return nI(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function nI(e,t){let n=e.injector.get(Nt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new D(-403,!1);t.push(e)}function rI(e,t,n){try{let r=n();return ds(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var fi=null;function oI(e=[],t){return J.create({name:t,providers:[{provide:Ui,useValue:"platform"},{provide:Zc,useValue:new Set([()=>fi=null])},...e]})}function iI(e=[]){if(fi)return fi;let t=oI(e);return fi=t,$E(),sI(t),t}function sI(e){let t=e.get(Tl,null);zi(e,()=>{t?.forEach(n=>n())})}var Xl=(()=>{class e{static __NG_ELEMENT_ID__=aI}return e})();function aI(e){return cI(de(),_(),(e&16)===16)}function cI(e,t,n){if(Gn(e)&&!n){let r=Xe(e.index,t);return new Rr(r,r)}else if(e.type&175){let r=t[Te];return new Rr(r,t)}return null}var Yc=class{constructor(){}supports(t){return Bm(t)}create(t){return new Kc(t)}},lI=(e,t)=>t,Kc=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||lI}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<pf(r,o,i)?n:r,a=pf(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;u<=h&&h<l&&(i[p]=f+1)}let d=s.previousIndex;i[d]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Bm(t))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,OE(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new Qc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new ji),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new ji),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Qc=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Xc=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},ji=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Xc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function pf(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}var Jc=class{constructor(){}supports(t){return t instanceof Map||Zl(t)}create(){return new el}},el=class{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||Zl(t)))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new tl(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},tl=class{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(t){this.key=t}};function mf(){return new Jl([new Yc])}var Jl=(()=>{class e{factories;static \u0275prov=b({token:e,providedIn:"root",factory:mf});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||mf()),deps:[[e,new Nf,new il]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new D(901,!1)}}return e})();function hf(){return new eu([new Jc])}var eu=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:hf});factories;constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||hf()),deps:[[e,new Nf,new il]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new D(901,!1)}}return e})();function uh(e){L(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=iI(r),i=[ch({}),{provide:sn,useExisting:Xw},...n||[]],s=new Ri({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return tI({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{L(9)}}function ke(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function dh(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function tu(e){return xa(e)}function pk(e,t){return Jo(e,t?.equal)}var nl=class{[ge];constructor(t){this[ge]=t}destroy(){this[ge].destroy()}};function nu(e,t){!t?.injector&&cl(nu);let n=t?.injector??m(J),r=t?.manualCleanup!==!0?n.get(Xi):null,o,i=n.get(Sl,null,{optional:!0}),s=n.get(sn);return i!==null&&!t?.forceRoot?(o=fI(i.view,s,e),r instanceof Mi&&r._lView===i.view&&(r=null)):o=pI(e,n.get(Hm),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new nl(o)}var fh=ae(k({},Tn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Nr,run(){if(this.dirty=!1,this.hasRun&&!Ko(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=yr(this),n=_i(!1);try{this.maybeCleanup(),this.fn(e)}finally{_i(n),Yo(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),uI=ae(k({},fh),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){vr(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),dI=ae(k({},fh),{consumerMarkedDirty(){this.view[M]|=8192,qn(this.view),this.notifier.notify(13)},destroy(){vr(this),this.onDestroyFn(),this.maybeCleanup(),this.view[en]?.delete(this)}});function fI(e,t,n){let r=Object.create(dI);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[en]??=new Set,e[en].add(r),r.consumerMarkedDirty(r),r}function pI(e,t,n){let r=Object.create(uI);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function ph(e,t){let n=Tt(e),r=t.elementInjector||$i();return new an(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function mk(e){let t=Tt(e);if(!t)return null;let n=new an(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var F=new y("");var gh=null;function vt(){return gh}function ru(e){gh??=e}var Kr=class{},Qr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>m(bh),providedIn:"platform"})}return e})(),mI=new y(""),bh=(()=>{class e extends Qr{_location;_history;_doc=m(F);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return vt().getBaseHref(this._doc)}onPopState(n){let r=vt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=vt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function ms(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function mh(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function $e(e){return e&&e[0]!=="?"?`?${e}`:e}var tr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>m(yh),providedIn:"root"})}return e})(),hs=new y(""),yh=(()=>{class e extends tr{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??m(F).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return ms(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+$e(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(Qr),C(hs,8))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vh=(()=>{class e{_subject=new B;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=bI(mh(hh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+$e(r))}normalize(n){return e.stripTrailingSlash(gI(this._basePath,hh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+$e(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+$e(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=$e;static joinWithSlash=ms;static stripTrailingSlash=mh;static \u0275fac=function(r){return new(r||e)(C(tr))};static \u0275prov=b({token:e,factory:()=>hI(),providedIn:"root"})}return e})();function hI(){return new vh(C(tr))}function gI(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function hh(e){return e.replace(/\/index.html$/,"")}function bI(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var yI=(()=>{class e extends tr{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=ms(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(Qr),C(hs,8))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();var pu=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(pu||{});var fe=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(fe||{}),j=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(j||{}),Ie=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ie||{}),Ce={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Ch(e){return Ne(e)[Q.LocaleId]}function Mh(e,t,n){let r=Ne(e),o=[r[Q.DayPeriodsFormat],r[Q.DayPeriodsStandalone]],i=Fe(o,t);return Fe(i,n)}function Th(e,t,n){let r=Ne(e),o=[r[Q.DaysFormat],r[Q.DaysStandalone]],i=Fe(o,t);return Fe(i,n)}function xh(e,t,n){let r=Ne(e),o=[r[Q.MonthsFormat],r[Q.MonthsStandalone]],i=Fe(o,t);return Fe(i,n)}function Sh(e,t){let r=Ne(e)[Q.Eras];return Fe(r,t)}function Xr(e,t){let n=Ne(e);return Fe(n[Q.DateFormat],t)}function Jr(e,t){let n=Ne(e);return Fe(n[Q.TimeFormat],t)}function eo(e,t){let r=Ne(e)[Q.DateTimeFormat];return Fe(r,t)}function st(e,t){let n=Ne(e),r=n[Q.NumberSymbols][t];if(typeof r>"u"){if(t===Ce.CurrencyDecimal)return n[Q.NumberSymbols][Ce.Decimal];if(t===Ce.CurrencyGroup)return n[Q.NumberSymbols][Ce.Group]}return r}function Nh(e,t){return Ne(e)[Q.NumberFormats][t]}function Ah(e){if(!e[Q.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[Q.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function Rh(e){let t=Ne(e);return Ah(t),(t[Q.ExtraData][2]||[]).map(r=>typeof r=="string"?ou(r):[ou(r[0]),ou(r[1])])}function Oh(e,t,n){let r=Ne(e);Ah(r);let o=[r[Q.ExtraData][0],r[Q.ExtraData][1]],i=Fe(o,t)||[];return Fe(i,n)||[]}function Fe(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function ou(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var vI=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,gs={},DI=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function kh(e,t,n,r){let o=SI(e);t=Dt(n,t)||t;let s=[],a;for(;t;)if(a=DI.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=Ph(r,c),o=xI(o,r));let l="";return s.forEach(u=>{let d=MI(u);l+=d?d(o,n,c):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function _s(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Dt(e,t){let n=Ch(e);if(gs[n]??={},gs[n][t])return gs[n][t];let r="";switch(t){case"shortDate":r=Xr(e,Ie.Short);break;case"mediumDate":r=Xr(e,Ie.Medium);break;case"longDate":r=Xr(e,Ie.Long);break;case"fullDate":r=Xr(e,Ie.Full);break;case"shortTime":r=Jr(e,Ie.Short);break;case"mediumTime":r=Jr(e,Ie.Medium);break;case"longTime":r=Jr(e,Ie.Long);break;case"fullTime":r=Jr(e,Ie.Full);break;case"short":let o=Dt(e,"shortTime"),i=Dt(e,"shortDate");r=bs(eo(e,Ie.Short),[o,i]);break;case"medium":let s=Dt(e,"mediumTime"),a=Dt(e,"mediumDate");r=bs(eo(e,Ie.Medium),[s,a]);break;case"long":let c=Dt(e,"longTime"),l=Dt(e,"longDate");r=bs(eo(e,Ie.Long),[c,l]);break;case"full":let u=Dt(e,"fullTime"),d=Dt(e,"fullDate");r=bs(eo(e,Ie.Full),[u,d]);break}return r&&(gs[n][t]=r),r}function bs(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function ze(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function _I(e,t){return ze(e,3).substring(0,t)}function te(e,t,n=0,r=!1,o=!1){return function(i,s){let a=EI(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return _I(a,t);let c=st(s,Ce.MinusSign);return ze(a,t,c,r,o)}}function EI(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function H(e,t,n=fe.Format,r=!1){return function(o,i){return wI(o,i,e,t,n,r)}}function wI(e,t,n,r,o,i){switch(n){case 2:return xh(t,o,r)[e.getMonth()];case 1:return Th(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let l=Rh(t),u=Oh(t,o,r),d=l.findIndex(p=>{if(Array.isArray(p)){let[f,h]=p,g=s>=f.hours&&a>=f.minutes,v=s<h.hours||s===h.hours&&a<h.minutes;if(f.hours<h.hours){if(g&&v)return!0}else if(g||v)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return Mh(t,o,r)[s<12?0:1];case 3:return Sh(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function ys(e){return function(t,n,r){let o=-1*r,i=st(n,Ce.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+ze(s,2,i)+ze(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+ze(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+ze(s,2,i)+":"+ze(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+ze(s,2,i)+":"+ze(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var II=0,Ds=4;function CI(e){let t=_s(e,II,1).getDay();return _s(e,0,1+(t<=Ds?Ds:Ds+7)-t)}function Fh(e){let t=e.getDay(),n=t===0?-3:Ds-t;return _s(e.getFullYear(),e.getMonth(),e.getDate()+n)}function iu(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Fh(n),s=CI(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return ze(o,e,st(r,Ce.MinusSign))}}function vs(e,t=!1){return function(n,r){let i=Fh(n).getFullYear();return ze(i,e,st(r,Ce.MinusSign),t)}}var su={};function MI(e){if(su[e])return su[e];let t;switch(e){case"G":case"GG":case"GGG":t=H(3,j.Abbreviated);break;case"GGGG":t=H(3,j.Wide);break;case"GGGGG":t=H(3,j.Narrow);break;case"y":t=te(0,1,0,!1,!0);break;case"yy":t=te(0,2,0,!0,!0);break;case"yyy":t=te(0,3,0,!1,!0);break;case"yyyy":t=te(0,4,0,!1,!0);break;case"Y":t=vs(1);break;case"YY":t=vs(2,!0);break;case"YYY":t=vs(3);break;case"YYYY":t=vs(4);break;case"M":case"L":t=te(1,1,1);break;case"MM":case"LL":t=te(1,2,1);break;case"MMM":t=H(2,j.Abbreviated);break;case"MMMM":t=H(2,j.Wide);break;case"MMMMM":t=H(2,j.Narrow);break;case"LLL":t=H(2,j.Abbreviated,fe.Standalone);break;case"LLLL":t=H(2,j.Wide,fe.Standalone);break;case"LLLLL":t=H(2,j.Narrow,fe.Standalone);break;case"w":t=iu(1);break;case"ww":t=iu(2);break;case"W":t=iu(1,!0);break;case"d":t=te(2,1);break;case"dd":t=te(2,2);break;case"c":case"cc":t=te(7,1);break;case"ccc":t=H(1,j.Abbreviated,fe.Standalone);break;case"cccc":t=H(1,j.Wide,fe.Standalone);break;case"ccccc":t=H(1,j.Narrow,fe.Standalone);break;case"cccccc":t=H(1,j.Short,fe.Standalone);break;case"E":case"EE":case"EEE":t=H(1,j.Abbreviated);break;case"EEEE":t=H(1,j.Wide);break;case"EEEEE":t=H(1,j.Narrow);break;case"EEEEEE":t=H(1,j.Short);break;case"a":case"aa":case"aaa":t=H(0,j.Abbreviated);break;case"aaaa":t=H(0,j.Wide);break;case"aaaaa":t=H(0,j.Narrow);break;case"b":case"bb":case"bbb":t=H(0,j.Abbreviated,fe.Standalone,!0);break;case"bbbb":t=H(0,j.Wide,fe.Standalone,!0);break;case"bbbbb":t=H(0,j.Narrow,fe.Standalone,!0);break;case"B":case"BB":case"BBB":t=H(0,j.Abbreviated,fe.Format,!0);break;case"BBBB":t=H(0,j.Wide,fe.Format,!0);break;case"BBBBB":t=H(0,j.Narrow,fe.Format,!0);break;case"h":t=te(3,1,-12);break;case"hh":t=te(3,2,-12);break;case"H":t=te(3,1);break;case"HH":t=te(3,2);break;case"m":t=te(4,1);break;case"mm":t=te(4,2);break;case"s":t=te(5,1);break;case"ss":t=te(5,2);break;case"S":t=te(6,1);break;case"SS":t=te(6,2);break;case"SSS":t=te(6,3);break;case"Z":case"ZZ":case"ZZZ":t=ys(0);break;case"ZZZZZ":t=ys(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=ys(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=ys(2);break;default:return null}return su[e]=t,t}function Ph(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function TI(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function xI(e,t,n){let o=e.getTimezoneOffset(),i=Ph(t,o);return TI(e,-1*(i-o))}function SI(e){if(Dh(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return _s(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(vI))return NI(r)}let t=new Date(e);if(!Dh(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function NI(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),l=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,l),t}function Dh(e){return e instanceof Date&&!isNaN(e.valueOf())}var AI=/^(\d+)?\.((\d+)(-(\d+))?)?$/,_h=22,Es=".",to="0",RI=";",OI=",",au="#";function kI(e,t,n,r,o,i,s=!1){let a="",c=!1;if(!isFinite(e))a=st(n,Ce.Infinity);else{let l=LI(e);s&&(l=PI(l));let u=t.minInt,d=t.minFrac,p=t.maxFrac;if(i){let U=i.match(AI);if(U===null)throw new Error(`${i} is not a valid digit info`);let Z=U[1],jt=U[3],vo=U[5];Z!=null&&(u=cu(Z)),jt!=null&&(d=cu(jt)),vo!=null?p=cu(vo):jt!=null&&d>p&&(p=d)}jI(l,d,p);let f=l.digits,h=l.integerLen,g=l.exponent,v=[];for(c=f.every(U=>!U);h<u;h++)f.unshift(0);for(;h<0;h++)f.unshift(0);h>0?v=f.splice(h,f.length):(v=f,f=[0]);let w=[];for(f.length>=t.lgSize&&w.unshift(f.splice(-t.lgSize,f.length).join(""));f.length>t.gSize;)w.unshift(f.splice(-t.gSize,f.length).join(""));f.length&&w.unshift(f.join("")),a=w.join(st(n,r)),v.length&&(a+=st(n,o)+v.join("")),g&&(a+=st(n,Ce.Exponential)+"+"+g)}return e<0&&!c?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function Lh(e,t,n){let r=Nh(t,pu.Decimal),o=FI(r,st(t,Ce.MinusSign));return kI(e,o,t,Ce.Group,Ce.Decimal,n)}function FI(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(RI),o=r[0],i=r[1],s=o.indexOf(Es)!==-1?o.split(Es):[o.substring(0,o.lastIndexOf(to)+1),o.substring(o.lastIndexOf(to)+1)],a=s[0],c=s[1]||"";n.posPre=a.substring(0,a.indexOf(au));for(let u=0;u<c.length;u++){let d=c.charAt(u);d===to?n.minFrac=n.maxFrac=u+1:d===au?n.maxFrac=u+1:n.posSuf+=d}let l=a.split(OI);if(n.gSize=l[1]?l[1].length:0,n.lgSize=l[2]||l[1]?(l[2]||l[1]).length:0,i){let u=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(au);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+u).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function PI(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function LI(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(Es))>-1&&(t=t.replace(Es,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===to;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===to;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>_h&&(r=r.splice(0,_h-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function jI(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let c=i!==0,l=t+e.integerLen,u=r.reduceRight(function(d,p,f,h){return p=p+d,h[f]=p<10?p:p-10,c&&(h[f]===0&&f>=l?h.pop():c=!1),p>=10?1:0},0);u&&(r.unshift(u),e.integerLen++)}function cu(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}var lu=/\s+/,Eh=[],BI=(()=>{class e{_ngEl;_renderer;initialClasses=Eh;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(lu):Eh}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(lu):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(lu).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(W(ee),W(Gr))};static \u0275dir=q({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var ws=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},jh=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new ws(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),wh(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);wh(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(W(kt),W(St),W(Jl))};static \u0275dir=q({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function wh(e,t){e.context.$implicit=t.item}var VI=(()=>{class e{_viewContainer;_context=new Is;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Ih(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Ih(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(W(kt),W(St))};static \u0275dir=q({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Is=class{$implicit=null;ngIf=null};function Ih(e,t){if(e&&!e.createEmbeddedView)throw new D(2020,!1)}var uu=class{_viewContainerRef;_templateRef;_created=!1;constructor(t,n){this._viewContainerRef=t,this._templateRef=n}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(t){t&&!this._created?this.create():!t&&this._created&&this.destroy()}},Bh=(()=>{class e{_defaultViews=[];_defaultUsed=!1;_caseCount=0;_lastCaseCheckIndex=0;_lastCasesMatched=!1;_ngSwitch;set ngSwitch(n){this._ngSwitch=n,this._caseCount===0&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(n){this._defaultViews.push(n)}_matchCase(n){let r=n===this._ngSwitch;return this._lastCasesMatched||=r,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),r}_updateDefaultCases(n){if(this._defaultViews.length>0&&n!==this._defaultUsed){this._defaultUsed=n;for(let r of this._defaultViews)r.enforceState(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"}})}return e})(),HI=(()=>{class e{ngSwitch;_view;ngSwitchCase;constructor(n,r,o){this.ngSwitch=o,o._addCase(),this._view=new uu(n,r)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}static \u0275fac=function(r){return new(r||e)(W(kt),W(St),W(Bh,9))};static \u0275dir=q({type:e,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"}})}return e})();var UI=(()=>{class e{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(n,r,o){this._ngEl=n,this._differs=r,this._renderer=o}set ngStyle(n){this._ngStyle=n,!this._differ&&n&&(this._differ=this._differs.find(n).create())}ngDoCheck(){if(this._differ){let n=this._differ.diff(this._ngStyle);n&&this._applyChanges(n)}}_setStyle(n,r){let[o,i]=n.split("."),s=o.indexOf("-")===-1?void 0:tt.DashCase;r!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${r}${i}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(n){n.forEachRemovedItem(r=>this._setStyle(r.key,null)),n.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),n.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static \u0275fac=function(r){return new(r||e)(W(ee),W(eu),W(Gr))};static \u0275dir=q({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return e})(),$I=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(W(kt))};static \u0275dir=q({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[qi]})}return e})();function Cs(e,t){return new D(2100,!1)}var du=class{createSubscription(t,n){return tu(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){tu(()=>t.unsubscribe())}},fu=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},zI=new fu,GI=new du,WI=(()=>{class e{_ref;_latestValue=null;markForCheckOnValueUpdate=!0;_subscription=null;_obj=null;_strategy=null;constructor(n){this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(ds(n))return zI;if(Yl(n))return GI;throw Cs(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static \u0275fac=function(r){return new(r||e)(W(Xl,16))};static \u0275pipe=Wr({name:"async",type:e,pure:!1})}return e})();var qI="mediumDate",Vh=new y(""),Hh=new y(""),ZI=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??qI,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return kh(n,s,i||this.locale,a)}catch(s){throw Cs(e,s.message)}}static \u0275fac=function(r){return new(r||e)(W(Yr,16),W(Vh,24),W(Hh,24))};static \u0275pipe=Wr({name:"date",type:e,pure:!0})}return e})();var YI=(()=>{class e{_locale;constructor(n){this._locale=n}transform(n,r,o){if(!KI(n))return null;o||=this._locale;try{let i=QI(n);return Lh(i,o,r)}catch(i){throw Cs(e,i.message)}}static \u0275fac=function(r){return new(r||e)(W(Yr,16))};static \u0275pipe=Wr({name:"number",type:e,pure:!0})}return e})();function KI(e){return!(e==null||e===""||e!==e)}function QI(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var XI=(()=>{class e{transform(n,r,o){if(n==null)return null;if(!(typeof n=="string"||Array.isArray(n)))throw Cs(e,n);return n.slice(r,o)}static \u0275fac=function(r){return new(r||e)};static \u0275pipe=Wr({name:"slice",type:e,pure:!1})}return e})();var Uh=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({})}return e})();function no(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Ms="browser",$h="server";function mu(e){return e===Ms}function Ts(e){return e===$h}var hn=class{};var SF=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>new hu(m(F),window)})}return e})(),hu=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=JI(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function JI(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Ns=new y(""),vu=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new D(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(C(Ns),C(O))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),ro=class{_doc;constructor(t){this._doc=t}manager},xs="ng-app-id";function zh(e){for(let t of e)t.remove()}function Gh(e,t){let n=t.createElement("style");return n.textContent=e,n}function eC(e,t,n,r){let o=e.head?.querySelectorAll(`style[${xs}="${t}"],link[${xs}="${t}"]`);if(o)for(let i of o)i.removeAttribute(xs),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function bu(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Du=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Ts(i),eC(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Gh);r?.forEach(o=>this.addUsage(o,this.external,bu))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(zh(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])zh(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Gh(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,bu(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(xs,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(C(F),C(Rt),C(Hr,8),C(ot))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),gu={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},_u=/%COMP%/g;var qh="%COMP%",tC=`_nghost-${qh}`,nC=`_ngcontent-${qh}`,rC=!0,oC=new y("",{providedIn:"root",factory:()=>rC});function iC(e){return nC.replace(_u,e)}function sC(e){return tC.replace(_u,e)}function Zh(e,t){return t.map(n=>n.replace(_u,e))}var Eu=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=Ts(a),this.defaultRenderer=new oo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===et.ShadowDom&&(r=ae(k({},r),{encapsulation:et.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Ss?o.applyToHost(n):o instanceof io&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case et.Emulated:i=new Ss(c,l,r,this.appId,u,s,a,d,p);break;case et.ShadowDom:return new yu(c,l,n,r,s,a,this.nonce,d,p);default:i=new io(c,l,r,u,s,a,d,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(C(vu),C(Du),C(Rt),C(oC),C(F),C(ot),C(O),C(Hr),C(Xn,8))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),oo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(gu[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Wh(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Wh(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new D(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=gu[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=gu[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(tt.DashCase|tt.Important)?t.style.setProperty(n,r,o&tt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&tt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=vt().getGlobalEventTarget(this.doc,t),!t))throw new D(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Wh(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var yu=class extends oo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=Zh(o.id,u);for(let p of u){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let p of d){let f=bu(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},io=class extends oo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?Zh(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ss=class extends io{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=iC(u),this.hostAttr=sC(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var As=class e extends Kr{supportsDOMEvents=!0;static makeCurrent(){ru(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=aC();return n==null?null:cC(n)}resetBaseElement(){so=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return no(document.cookie,t)}},so=null;function aC(){return so=so||document.querySelector("base"),so?so.getAttribute("href"):null}function cC(e){return new URL(e,document.baseURI).pathname}var lC=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),Kh=(()=>{class e extends ro{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),Yh=["alt","control","meta","shift"],uC={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},dC={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Qh=(()=>{class e extends ro{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>vt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Yh.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=uC[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Yh.forEach(s=>{if(s!==o){let a=dC[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();function fC(e,t){return uh(k({rootComponent:e},pC(t)))}function pC(e){return{appProviders:[...yC,...e?.providers??[]],platformProviders:bC}}function mC(){As.makeCurrent()}function hC(){return new Je}function gC(){return Op(document),document}var bC=[{provide:ot,useValue:Ms},{provide:Tl,useValue:mC,multi:!0},{provide:F,useFactory:gC}];var yC=[{provide:Ui,useValue:"root"},{provide:Je,useFactory:hC},{provide:Ns,useClass:Kh,multi:!0,deps:[F]},{provide:Ns,useClass:Qh,multi:!0,deps:[F]},Eu,Du,vu,{provide:nt,useExisting:Eu},{provide:hn,useClass:lC},[]];var rr=class{},ao=class{},Pt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Os=class{encodeKey(t){return Xh(t)}encodeValue(t){return Xh(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function vC(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var DC=/%(\d[a-f0-9])/gi,_C={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Xh(e){return encodeURIComponent(e).replace(DC,(t,n)=>_C[n]??t)}function Rs(e){return`${e}`}var _t=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Os,t.fromString){if(t.fromObject)throw new D(2805,!1);this.map=vC(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Rs):[Rs(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Rs(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Rs(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var ks=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function EC(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Jh(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function eg(e){return typeof Blob<"u"&&e instanceof Blob}function tg(e){return typeof FormData<"u"&&e instanceof FormData}function wC(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var ng="Content-Type",rg="Accept",og="X-Request-URL",ig="text/plain",sg="application/json",IC=`${sg}, ${ig}, */*`,nr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(EC(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Pt,this.context??=new ks,!this.params)this.params=new _t,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Jh(this.body)||eg(this.body)||tg(this.body)||wC(this.body)?this.body:this.body instanceof _t?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||tg(this.body)?null:eg(this.body)?this.body.type||null:Jh(this.body)?null:typeof this.body=="string"?ig:this.body instanceof _t?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?sg:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,l=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((p,f)=>p.set(f,t.setHeaders[f]),l)),t.setParams&&(u=Object.keys(t.setParams).reduce((p,f)=>p.set(f,t.setParams[f]),u)),new e(n,r,s,{params:u,headers:l,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},gn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(gn||{}),or=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Pt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Fs=class e extends or{constructor(t={}){super(t)}type=gn.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},co=class e extends or{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=gn.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},lo=class extends or{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},CC=200,MC=204;function wu(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var ag=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof nr)i=n;else{let c;o.headers instanceof Pt?c=o.headers:c=new Pt(o.headers);let l;o.params&&(o.params instanceof _t?l=o.params:l=new _t({fromObject:o.params})),i=new nr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=In(i).pipe(aa(c=>this.handler.handle(c)));if(n instanceof nr||o.observe==="events")return s;let a=s.pipe(Me(c=>c instanceof co));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(X(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new D(2806,!1);return c.body}));case"blob":return a.pipe(X(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new D(2807,!1);return c.body}));case"text":return a.pipe(X(c=>{if(c.body!==null&&typeof c.body!="string")throw new D(2808,!1);return c.body}));case"json":default:return a.pipe(X(c=>c.body))}case"response":return a;default:throw new D(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new _t().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,wu(o,r))}post(n,r,o={}){return this.request("POST",n,wu(o,r))}put(n,r,o={}){return this.request("PUT",n,wu(o,r))}static \u0275fac=function(r){return new(r||e)(C(rr))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();var TC=new y("");function xC(e,t){return t(e)}function SC(e,t,n){return(r,o)=>zi(n,()=>t(r,i=>e(i,o)))}var cg=new y(""),lg=new y(""),ug=new y("",{providedIn:"root",factory:()=>!0});var Ps=(()=>{class e extends rr{backend;injector;chain=null;pendingTasks=m(Zn);contributeToStability=m(ug);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(cg),...this.injector.get(lg,[])]));this.chain=r.reduceRight((o,i)=>SC(o,i,this.injector),xC)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(la(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(C(ao),C(Oe))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();var NC=/^\)\]\}',?\n/,AC=RegExp(`^${og}:`,"m");function RC(e){return"responseURL"in e&&e.responseURL?e.responseURL:AC.test(e.getAllResponseHeaders())?e.getResponseHeader(og):null}var Iu=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new D(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?Ae(r.\u0275loadImpl()):In(null)).pipe(ma(()=>new N(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((g,v)=>s.setRequestHeader(g,v.join(","))),n.headers.has(rg)||s.setRequestHeader(rg,IC),!n.headers.has(ng)){let g=n.detectContentTypeHeader();g!==null&&s.setRequestHeader(ng,g)}if(n.responseType){let g=n.responseType.toLowerCase();s.responseType=g!=="json"?g:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let g=s.statusText||"OK",v=new Pt(s.getAllResponseHeaders()),w=RC(s)||n.url;return c=new Fs({headers:v,status:s.status,statusText:g,url:w}),c},u=()=>{let{headers:g,status:v,statusText:w,url:U}=l(),Z=null;v!==MC&&(Z=typeof s.response>"u"?s.responseText:s.response),v===0&&(v=Z?CC:0);let jt=v>=200&&v<300;if(n.responseType==="json"&&typeof Z=="string"){let vo=Z;Z=Z.replace(NC,"");try{Z=Z!==""?JSON.parse(Z):null}catch(Jg){Z=vo,jt&&(jt=!1,Z={error:Jg,text:Z})}}jt?(i.next(new co({body:Z,headers:g,status:v,statusText:w,url:U||void 0})),i.complete()):i.error(new lo({error:Z,headers:g,status:v,statusText:w,url:U||void 0}))},d=g=>{let{url:v}=l(),w=new lo({error:g,status:s.status||0,statusText:s.statusText||"Unknown Error",url:v||void 0});i.error(w)},p=!1,f=g=>{p||(i.next(l()),p=!0);let v={type:gn.DownloadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),n.responseType==="text"&&s.responseText&&(v.partialText=s.responseText),i.next(v)},h=g=>{let v={type:gn.UploadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),i.next(v)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",h)),s.send(a),i.next({type:gn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",h)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(C(hn))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),dg=new y(""),OC="XSRF-TOKEN",kC=new y("",{providedIn:"root",factory:()=>OC}),FC="X-XSRF-TOKEN",PC=new y("",{providedIn:"root",factory:()=>FC}),uo=class{},LC=(()=>{class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=no(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(C(F),C(ot),C(kC))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();function jC(e,t){let n=e.url.toLowerCase();if(!m(dg)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=m(uo).getToken(),o=m(PC);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}function BC(...e){let t=[ag,Iu,Ps,{provide:rr,useExisting:Ps},{provide:ao,useFactory:()=>m(TC,{optional:!0})??m(Iu)},{provide:cg,useValue:jC,multi:!0},{provide:dg,useValue:!0},{provide:uo,useClass:LC}];for(let n of e)t.push(...n.\u0275providers);return jr(t)}var e1=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var VC=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=C(HC),o},providedIn:"root"})}return e})(),HC=(()=>{class e extends VC{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case it.NONE:return r;case it.HTML:return Ot(r,"HTML")?Ue(r):Yp(this._doc,String(r)).toString();case it.STYLE:return Ot(r,"Style")?Ue(r):r;case it.SCRIPT:if(Ot(r,"Script"))return Ue(r);throw new D(5200,!1);case it.URL:return Ot(r,"URL")?Ue(r):ns(String(r));case it.RESOURCE_URL:if(Ot(r,"ResourceURL"))return Ue(r);throw new D(5201,!1);default:throw new D(5202,!1)}}bypassSecurityTrustHtml(n){return Bp(n)}bypassSecurityTrustStyle(n){return Vp(n)}bypassSecurityTrustScript(n){return Hp(n)}bypassSecurityTrustUrl(n){return Up(n)}bypassSecurityTrustResourceUrl(n){return $p(n)}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fo(e){return e.buttons===0||e.detail===0}function po(e){let t=e.touches&&e.touches[0]||e.changedTouches&&e.changedTouches[0];return!!t&&t.identifier===-1&&(t.radiusX==null||t.radiusX===1)&&(t.radiusY==null||t.radiusY===1)}var Cu;function fg(){if(Cu==null){let e=typeof document<"u"?document.head:null;Cu=!!(e&&(e.createShadowRoot||e.attachShadow))}return Cu}function Mu(e){if(fg()){let t=e.getRootNode?e.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&t instanceof ShadowRoot)return t}return null}function Tu(){let e=typeof document<"u"&&document?document.activeElement:null;for(;e&&e.shadowRoot;){let t=e.shadowRoot.activeElement;if(t===e)break;e=t}return e}function Ge(e){return e.composedPath?e.composedPath()[0]:e.target}function bn(e,t,n,r,o){let i=parseInt(Ql.major),s=parseInt(Ql.minor);return i>19||i===19&&s>0||i===0&&s===0?e.listen(t,n,r,o):(t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)})}var xu;try{xu=typeof Intl<"u"&&Intl.v8BreakIterator}catch{xu=!1}var pe=(()=>{class e{_platformId=m(ot);isBrowser=this._platformId?mu(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||xu)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var mo;function pg(){if(mo==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>mo=!0}))}finally{mo=mo||!1}return mo}function ir(e){return pg()?e:!!e.capture}function Su(e,t=0){return mg(e)?Number(e):arguments.length===2?t:0}function mg(e){return!isNaN(parseFloat(e))&&!isNaN(Number(e))}function at(e){return e instanceof ee?e.nativeElement:e}var hg=new y("cdk-input-modality-detector-options"),gg={ignoreKeys:[18,17,224,91,16]},bg=650,Nu={passive:!0,capture:!0},yg=(()=>{class e{_platform=m(pe);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new $t(null);_options;_lastTouchMs=0;_onKeydown=n=>{this._options?.ignoreKeys?.some(r=>r===n.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=Ge(n))};_onMousedown=n=>{Date.now()-this._lastTouchMs<bg||(this._modality.next(fo(n)?"keyboard":"mouse"),this._mostRecentTarget=Ge(n))};_onTouchstart=n=>{if(po(n)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=Ge(n)};constructor(){let n=m(O),r=m(F),o=m(hg,{optional:!0});if(this._options=k(k({},gg),o),this.modalityDetected=this._modality.pipe(hr(1)),this.modalityChanged=this.modalityDetected.pipe(ca()),this._platform.isBrowser){let i=m(nt).createRenderer(null,null);this._listenerCleanups=n.runOutsideAngular(()=>[bn(i,r,"keydown",this._onKeydown,Nu),bn(i,r,"mousedown",this._onMousedown,Nu),bn(i,r,"touchstart",this._onTouchstart,Nu)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(n=>n())}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ho=function(e){return e[e.IMMEDIATE=0]="IMMEDIATE",e[e.EVENTUAL=1]="EVENTUAL",e}(ho||{}),vg=new y("cdk-focus-monitor-default-options"),Ls=ir({passive:!0,capture:!0}),js=(()=>{class e{_ngZone=m(O);_platform=m(pe);_inputModalityDetector=m(yg);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=m(F,{optional:!0});_stopInputModalityDetector=new B;constructor(){let n=m(vg,{optional:!0});this._detectionMode=n?.detectionMode||ho.IMMEDIATE}_rootNodeFocusAndBlurListener=n=>{let r=Ge(n);for(let o=r;o;o=o.parentElement)n.type==="focus"?this._onFocus(n,o):this._onBlur(n,o)};monitor(n,r=!1){let o=at(n);if(!this._platform.isBrowser||o.nodeType!==1)return In();let i=Mu(o)||this._getDocument(),s=this._elementInfo.get(o);if(s)return r&&(s.checkChildren=!0),s.subject;let a={checkChildren:r,subject:new B,rootNode:i};return this._elementInfo.set(o,a),this._registerGlobalListeners(a),a.subject}stopMonitoring(n){let r=at(n),o=this._elementInfo.get(r);o&&(o.subject.complete(),this._setClasses(r),this._elementInfo.delete(r),this._removeGlobalListeners(o))}focusVia(n,r,o){let i=at(n),s=this._getDocument().activeElement;i===s?this._getClosestElementsInfo(i).forEach(([a,c])=>this._originChanged(a,r,c)):(this._setOrigin(r),typeof i.focus=="function"&&i.focus(o))}ngOnDestroy(){this._elementInfo.forEach((n,r)=>this.stopMonitoring(r))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(n){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(n)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:n&&this._isLastInteractionFromInputLabel(n)?"mouse":"program"}_shouldBeAttributedToTouch(n){return this._detectionMode===ho.EVENTUAL||!!n?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(n,r){n.classList.toggle("cdk-focused",!!r),n.classList.toggle("cdk-touch-focused",r==="touch"),n.classList.toggle("cdk-keyboard-focused",r==="keyboard"),n.classList.toggle("cdk-mouse-focused",r==="mouse"),n.classList.toggle("cdk-program-focused",r==="program")}_setOrigin(n,r=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=n,this._originFromTouchInteraction=n==="touch"&&r,this._detectionMode===ho.IMMEDIATE){clearTimeout(this._originTimeoutId);let o=this._originFromTouchInteraction?bg:1;this._originTimeoutId=setTimeout(()=>this._origin=null,o)}})}_onFocus(n,r){let o=this._elementInfo.get(r),i=Ge(n);!o||!o.checkChildren&&r!==i||this._originChanged(r,this._getFocusOrigin(i),o)}_onBlur(n,r){let o=this._elementInfo.get(r);!o||o.checkChildren&&n.relatedTarget instanceof Node&&r.contains(n.relatedTarget)||(this._setClasses(r),this._emitOrigin(o,null))}_emitOrigin(n,r){n.subject.observers.length&&this._ngZone.run(()=>n.subject.next(r))}_registerGlobalListeners(n){if(!this._platform.isBrowser)return;let r=n.rootNode,o=this._rootNodeFocusListenerCount.get(r)||0;o||this._ngZone.runOutsideAngular(()=>{r.addEventListener("focus",this._rootNodeFocusAndBlurListener,Ls),r.addEventListener("blur",this._rootNodeFocusAndBlurListener,Ls)}),this._rootNodeFocusListenerCount.set(r,o+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Mn(this._stopInputModalityDetector)).subscribe(i=>{this._setOrigin(i,!0)}))}_removeGlobalListeners(n){let r=n.rootNode;if(this._rootNodeFocusListenerCount.has(r)){let o=this._rootNodeFocusListenerCount.get(r);o>1?this._rootNodeFocusListenerCount.set(r,o-1):(r.removeEventListener("focus",this._rootNodeFocusAndBlurListener,Ls),r.removeEventListener("blur",this._rootNodeFocusAndBlurListener,Ls),this._rootNodeFocusListenerCount.delete(r))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(n,r,o){this._setClasses(n,r),this._emitOrigin(o,r),this._lastFocusOrigin=r}_getClosestElementsInfo(n){let r=[];return this._elementInfo.forEach((o,i)=>{(i===n||o.checkChildren&&i.contains(n))&&r.push([i,o])}),r}_isLastInteractionFromInputLabel(n){let{_mostRecentTarget:r,mostRecentModality:o}=this._inputModalityDetector;if(o!=="mouse"||!r||r===n||n.nodeName!=="INPUT"&&n.nodeName!=="TEXTAREA"||n.disabled)return!1;let i=n.labels;if(i){for(let s=0;s<i.length;s++)if(i[s].contains(r))return!0}return!1}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),UC=(()=>{class e{_elementRef=m(ee);_focusMonitor=m(js);_monitorSubscription;_focusOrigin=null;cdkFocusChange=new ye;constructor(){}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let n=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(n,n.nodeType===1&&n.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(r=>{this._focusOrigin=r,this.cdkFocusChange.emit(r)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"]})}return e})();var Bs=new WeakMap,Et=(()=>{class e{_appRef;_injector=m(J);_environmentInjector=m(Oe);load(n){let r=this._appRef=this._appRef||this._injector.get(Nt),o=Bs.get(r);o||(o={loaders:new Set,refs:[]},Bs.set(r,o),r.onDestroy(()=>{Bs.get(r)?.refs.forEach(i=>i.destroy()),Bs.delete(r)})),o.loaders.has(n)||(o.loaders.add(n),o.refs.push(ph(n,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Vs=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(r,o){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return e})();function Au(e){return Array.isArray(e)?e:[e]}var Dg=new Set,yn,$C=(()=>{class e{_platform=m(pe);_nonce=m(Hr,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):GC}matchMedia(n){return(this._platform.WEBKIT||this._platform.BLINK)&&zC(n,this._nonce),this._matchMedia(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function zC(e,t){if(!Dg.has(e))try{yn||(yn=document.createElement("style"),t&&yn.setAttribute("nonce",t),yn.setAttribute("type","text/css"),document.head.appendChild(yn)),yn.sheet&&(yn.sheet.insertRule(`@media ${e} {body{ }}`,0),Dg.add(e))}catch(n){console.error(n)}}function GC(e){return{matches:e==="all"||e==="",media:e,addListener:()=>{},removeListener:()=>{}}}var Eg=(()=>{class e{_mediaMatcher=m($C);_zone=m(O);_queries=new Map;_destroySubject=new B;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(n){return _g(Au(n)).some(o=>this._registerQuery(o).mql.matches)}observe(n){let o=_g(Au(n)).map(s=>this._registerQuery(s).observable),i=sa(o);return i=Cn(i.pipe(mr(1)),i.pipe(hr(1),qt(0))),i.pipe(X(s=>{let a={matches:!1,breakpoints:{}};return s.forEach(({matches:c,query:l})=>{a.matches=a.matches||c,a.breakpoints[l]=c}),a}))}_registerQuery(n){if(this._queries.has(n))return this._queries.get(n);let r=this._mediaMatcher.matchMedia(n),i={observable:new N(s=>{let a=c=>this._zone.run(()=>s.next(c));return r.addListener(a),()=>{r.removeListener(a)}}).pipe(pa(r),X(({matches:s})=>({query:n,matches:s})),Mn(this._destroySubject)),mql:r};return this._queries.set(n,i),i}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function _g(e){return e.map(t=>t.split(",")).reduce((t,n)=>t.concat(n)).map(t=>t.trim())}function WC(e){if(e.type==="characterData"&&e.target instanceof Comment)return!0;if(e.type==="childList"){for(let t=0;t<e.addedNodes.length;t++)if(!(e.addedNodes[t]instanceof Comment))return!1;for(let t=0;t<e.removedNodes.length;t++)if(!(e.removedNodes[t]instanceof Comment))return!1;return!0}return!1}var wg=(()=>{class e{create(n){return typeof MutationObserver>"u"?null:new MutationObserver(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ig=(()=>{class e{_mutationObserverFactory=m(wg);_observedElements=new Map;_ngZone=m(O);constructor(){}ngOnDestroy(){this._observedElements.forEach((n,r)=>this._cleanupObserver(r))}observe(n){let r=at(n);return new N(o=>{let s=this._observeElement(r).pipe(X(a=>a.filter(c=>!WC(c))),Me(a=>!!a.length)).subscribe(a=>{this._ngZone.run(()=>{o.next(a)})});return()=>{s.unsubscribe(),this._unobserveElement(r)}})}_observeElement(n){return this._ngZone.runOutsideAngular(()=>{if(this._observedElements.has(n))this._observedElements.get(n).count++;else{let r=new B,o=this._mutationObserverFactory.create(i=>r.next(i));o&&o.observe(n,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(n,{observer:o,stream:r,count:1})}return this._observedElements.get(n).stream})}_unobserveElement(n){this._observedElements.has(n)&&(this._observedElements.get(n).count--,this._observedElements.get(n).count||this._cleanupObserver(n))}_cleanupObserver(n){if(this._observedElements.has(n)){let{observer:r,stream:o}=this._observedElements.get(n);r&&r.disconnect(),o.complete(),this._observedElements.delete(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),nL=(()=>{class e{_contentObserver=m(Ig);_elementRef=m(ee);event=new ye;get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._disabled?this._unsubscribe():this._subscribe()}_disabled=!1;get debounce(){return this._debounce}set debounce(n){this._debounce=Su(n),this._subscribe()}_debounce;_currentSubscription=null;constructor(){}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let n=this._contentObserver.observe(this._elementRef);this._currentSubscription=(this.debounce?n.pipe(qt(this.debounce)):n).subscribe(this.event)}_unsubscribe(){this._currentSubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[2,"cdkObserveContentDisabled","disabled",ke],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"]})}return e})(),Cg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({providers:[wg]})}return e})();var Sg=(()=>{class e{_platform=m(pe);constructor(){}isDisabled(n){return n.hasAttribute("disabled")}isVisible(n){return ZC(n)&&getComputedStyle(n).visibility==="visible"}isTabbable(n){if(!this._platform.isBrowser)return!1;let r=qC(n0(n));if(r&&(Mg(r)===-1||!this.isVisible(r)))return!1;let o=n.nodeName.toLowerCase(),i=Mg(n);return n.hasAttribute("contenteditable")?i!==-1:o==="iframe"||o==="object"||this._platform.WEBKIT&&this._platform.IOS&&!e0(n)?!1:o==="audio"?n.hasAttribute("controls")?i!==-1:!1:o==="video"?i===-1?!1:i!==null?!0:this._platform.FIREFOX||n.hasAttribute("controls"):n.tabIndex>=0}isFocusable(n,r){return t0(n)&&!this.isDisabled(n)&&(r?.ignoreVisibility||this.isVisible(n))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function qC(e){try{return e.frameElement}catch{return null}}function ZC(e){return!!(e.offsetWidth||e.offsetHeight||typeof e.getClientRects=="function"&&e.getClientRects().length)}function YC(e){let t=e.nodeName.toLowerCase();return t==="input"||t==="select"||t==="button"||t==="textarea"}function KC(e){return XC(e)&&e.type=="hidden"}function QC(e){return JC(e)&&e.hasAttribute("href")}function XC(e){return e.nodeName.toLowerCase()=="input"}function JC(e){return e.nodeName.toLowerCase()=="a"}function Ng(e){if(!e.hasAttribute("tabindex")||e.tabIndex===void 0)return!1;let t=e.getAttribute("tabindex");return!!(t&&!isNaN(parseInt(t,10)))}function Mg(e){if(!Ng(e))return null;let t=parseInt(e.getAttribute("tabindex")||"",10);return isNaN(t)?-1:t}function e0(e){let t=e.nodeName.toLowerCase(),n=t==="input"&&e.type;return n==="text"||n==="password"||t==="select"||t==="textarea"}function t0(e){return KC(e)?!1:YC(e)||QC(e)||e.hasAttribute("contenteditable")||Ng(e)}function n0(e){return e.ownerDocument&&e.ownerDocument.defaultView||window}var Hs=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(t){this._enabled=t,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_enabled=!0;constructor(t,n,r,o,i=!1,s){this._element=t,this._checker=n,this._ngZone=r,this._document=o,this._injector=s,i||this.attachAnchors()}destroy(){let t=this._startAnchor,n=this._endAnchor;t&&(t.removeEventListener("focus",this.startAnchorListener),t.remove()),n&&(n.removeEventListener("focus",this.endAnchorListener),n.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusInitialElement(t)))})}focusFirstTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusFirstTabbableElement(t)))})}focusLastTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusLastTabbableElement(t)))})}_getRegionBoundary(t){let n=this._element.querySelectorAll(`[cdk-focus-region-${t}], [cdkFocusRegion${t}], [cdk-focus-${t}]`);return t=="start"?n.length?n[0]:this._getFirstTabbableElement(this._element):n.length?n[n.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(t){let n=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(n){if(!this._checker.isFocusable(n)){let r=this._getFirstTabbableElement(n);return r?.focus(t),!!r}return n.focus(t),!0}return this.focusFirstTabbableElement(t)}focusFirstTabbableElement(t){let n=this._getRegionBoundary("start");return n&&n.focus(t),!!n}focusLastTabbableElement(t){let n=this._getRegionBoundary("end");return n&&n.focus(t),!!n}hasAttached(){return this._hasAttached}_getFirstTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=0;r<n.length;r++){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(n[r]):null;if(o)return o}return null}_getLastTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=n.length-1;r>=0;r--){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(n[r]):null;if(o)return o}return null}_createAnchor(){let t=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,t),t.classList.add("cdk-visually-hidden"),t.classList.add("cdk-focus-trap-anchor"),t.setAttribute("aria-hidden","true"),t}_toggleAnchorTabIndex(t,n){t?n.setAttribute("tabindex","0"):n.removeAttribute("tabindex")}toggleAnchors(t){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_executeOnStable(t){this._injector?Nl(t,{injector:this._injector}):setTimeout(t)}},Ag=(()=>{class e{_checker=m(Sg);_ngZone=m(O);_document=m(F);_injector=m(J);constructor(){m(Et).load(Vs)}create(n,r=!1){return new Hs(n,this._checker,this._ngZone,this._document,r,this._injector)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),r0=(()=>{class e{_elementRef=m(ee);_focusTrapFactory=m(Ag);focusTrap;_previouslyFocusedElement=null;get enabled(){return this.focusTrap?.enabled||!1}set enabled(n){this.focusTrap&&(this.focusTrap.enabled=n)}autoCapture;constructor(){m(pe).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(n){let r=n.autoCapture;r&&!r.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=Tu(),this.focusTrap?.focusInitialElementWhenReady()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[2,"cdkTrapFocus","enabled",ke],autoCapture:[2,"cdkTrapFocusAutoCapture","autoCapture",ke]},exportAs:["cdkTrapFocus"],features:[qi]})}return e})(),Rg=new y("liveAnnouncerElement",{providedIn:"root",factory:Og});function Og(){return null}var kg=new y("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),o0=0,i0=(()=>{class e{_ngZone=m(O);_defaultOptions=m(kg,{optional:!0});_liveElement;_document=m(F);_previousTimeout;_currentPromise;_currentResolve;constructor(){let n=m(Rg,{optional:!0});this._liveElement=n||this._createLiveElement()}announce(n,...r){let o=this._defaultOptions,i,s;return r.length===1&&typeof r[0]=="number"?s=r[0]:[i,s]=r,this.clear(),clearTimeout(this._previousTimeout),i||(i=o&&o.politeness?o.politeness:"polite"),s==null&&o&&(s=o.duration),this._liveElement.setAttribute("aria-live",i),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(a=>this._currentResolve=a)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=n,typeof s=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),s)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let n="cdk-live-announcer-element",r=this._document.getElementsByClassName(n),o=this._document.createElement("div");for(let i=0;i<r.length;i++)r[i].remove();return o.classList.add(n),o.classList.add("cdk-visually-hidden"),o.setAttribute("aria-atomic","true"),o.setAttribute("aria-live","polite"),o.id=`cdk-live-announcer-${o0++}`,this._document.body.appendChild(o),o}_exposeAnnouncerToModals(n){let r=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let o=0;o<r.length;o++){let i=r[o],s=i.getAttribute("aria-owns");s?s.indexOf(n)===-1&&i.setAttribute("aria-owns",s+" "+n):i.setAttribute("aria-owns",n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Lt=function(e){return e[e.NONE=0]="NONE",e[e.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",e[e.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",e}(Lt||{}),Tg="cdk-high-contrast-black-on-white",xg="cdk-high-contrast-white-on-black",Ru="cdk-high-contrast-active",Us=(()=>{class e{_platform=m(pe);_hasCheckedHighContrastMode;_document=m(F);_breakpointSubscription;constructor(){this._breakpointSubscription=m(Eg).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return Lt.NONE;let n=this._document.createElement("div");n.style.backgroundColor="rgb(1,2,3)",n.style.position="absolute",this._document.body.appendChild(n);let r=this._document.defaultView||window,o=r&&r.getComputedStyle?r.getComputedStyle(n):null,i=(o&&o.backgroundColor||"").replace(/ /g,"");switch(n.remove(),i){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return Lt.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return Lt.BLACK_ON_WHITE}return Lt.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let n=this._document.body.classList;n.remove(Ru,Tg,xg),this._hasCheckedHighContrastMode=!0;let r=this.getHighContrastMode();r===Lt.BLACK_ON_WHITE?n.add(Ru,Tg):r===Lt.WHITE_ON_BLACK&&n.add(Ru,xg)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),s0=(()=>{class e{constructor(){m(Us)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[Cg]})}return e})();var Ou={},a0=(()=>{class e{_appId=m(Rt);getId(n){return this._appId!=="ng"&&(n+=this._appId),Ou.hasOwnProperty(n)||(Ou[n]=0),`${n}${Ou[n]++}`}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var c0=200,$s=class{_letterKeyStream=new B;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new B;selectedItem=this._selectedItem;constructor(t,n){let r=typeof n?.debounceInterval=="number"?n.debounceInterval:c0;n?.skipPredicate&&(this._skipPredicateFn=n.skipPredicate),this.setItems(t),this._setupKeyHandler(r)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(t){this._selectedItemIndex=t}setItems(t){this._items=t}handleKey(t){let n=t.keyCode;t.key&&t.key.length===1?this._letterKeyStream.next(t.key.toLocaleUpperCase()):(n>=65&&n<=90||n>=48&&n<=57)&&this._letterKeyStream.next(String.fromCharCode(n))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(t){this._letterKeyStream.pipe(ha(n=>this._pressedLetters.push(n)),qt(t),Me(()=>this._pressedLetters.length>0),X(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(n=>{for(let r=1;r<this._items.length+1;r++){let o=(this._selectedItemIndex+r)%this._items.length,i=this._items[o];if(!this._skipPredicateFn?.(i)&&i.getLabel?.().toLocaleUpperCase().trim().indexOf(n)===0){this._selectedItem.next(i);break}}this._pressedLetters=[]})}};function Fg(e,...t){return t.length?t.some(n=>e[n]):e.altKey||e.shiftKey||e.ctrlKey||e.metaKey}var sr=class{_items;_activeItemIndex=-1;_activeItem=Ml(null);_wrap=!1;_typeaheadSubscription=Y.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=t=>t.disabled;constructor(t,n){this._items=t,t instanceof Bn?this._itemChangesSubscription=t.changes.subscribe(r=>this._itemsChanged(r.toArray())):es(t)&&(this._effectRef=nu(()=>this._itemsChanged(t()),{injector:n}))}tabOut=new B;change=new B;skipPredicate(t){return this._skipPredicateFn=t,this}withWrap(t=!0){return this._wrap=t,this}withVerticalOrientation(t=!0){return this._vertical=t,this}withHorizontalOrientation(t){return this._horizontal=t,this}withAllowedModifierKeys(t){return this._allowedModifierKeys=t,this}withTypeAhead(t=200){this._typeaheadSubscription.unsubscribe();let n=this._getItemsArray();return this._typeahead=new $s(n,{debounceInterval:typeof t=="number"?t:void 0,skipPredicate:r=>this._skipPredicateFn(r)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(r=>{this.setActiveItem(r)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(t=!0){return this._homeAndEnd=t,this}withPageUpDown(t=!0,n=10){return this._pageUpAndDown={enabled:t,delta:n},this}setActiveItem(t){let n=this._activeItem();this.updateActiveItem(t),this._activeItem()!==n&&this.change.next(this._activeItemIndex)}onKeydown(t){let n=t.keyCode,o=["altKey","ctrlKey","metaKey","shiftKey"].every(i=>!t[i]||this._allowedModifierKeys.indexOf(i)>-1);switch(n){case 9:this.tabOut.next();return;case 40:if(this._vertical&&o){this.setNextItemActive();break}else return;case 38:if(this._vertical&&o){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&o){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&o){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&o){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&o){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(i>0?i:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex+this._pageUpAndDown.delta,s=this._getItemsArray().length;this._setActiveItemByIndex(i<s?i:s-1,-1);break}else return;default:(o||Fg(t,"shiftKey"))&&this._typeahead?.handleKey(t);return}this._typeahead?.reset(),t.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(t){let n=this._getItemsArray(),r=typeof t=="number"?t:n.indexOf(t),o=n[r];this._activeItem.set(o??null),this._activeItemIndex=r,this._typeahead?.setCurrentSelectedItemIndex(r)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(t){this._wrap?this._setActiveInWrapMode(t):this._setActiveInDefaultMode(t)}_setActiveInWrapMode(t){let n=this._getItemsArray();for(let r=1;r<=n.length;r++){let o=(this._activeItemIndex+t*r+n.length)%n.length,i=n[o];if(!this._skipPredicateFn(i)){this.setActiveItem(o);return}}}_setActiveInDefaultMode(t){this._setActiveItemByIndex(this._activeItemIndex+t,t)}_setActiveItemByIndex(t,n){let r=this._getItemsArray();if(r[t]){for(;this._skipPredicateFn(r[t]);)if(t+=n,!r[t])return;this.setActiveItem(t)}}_getItemsArray(){return es(this._items)?this._items():this._items instanceof Bn?this._items.toArray():this._items}_itemsChanged(t){this._typeahead?.setItems(t);let n=this._activeItem();if(n){let r=t.indexOf(n);r>-1&&r!==this._activeItemIndex&&(this._activeItemIndex=r,this._typeahead?.setCurrentSelectedItemIndex(r))}}};var ku=class extends sr{setActiveItem(t){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(t),this.activeItem&&this.activeItem.setActiveStyles()}};var Fu=class extends sr{_origin="program";setFocusOrigin(t){return this._origin=t,this}setActiveItem(t){super.setActiveItem(t),this.activeItem&&this.activeItem.focus(this._origin)}};var Lg=" ";function l0(e,t,n){let r=Gs(e,t);n=n.trim(),!r.some(o=>o.trim()===n)&&(r.push(n),e.setAttribute(t,r.join(Lg)))}function u0(e,t,n){let r=Gs(e,t);n=n.trim();let o=r.filter(i=>i!==n);o.length?e.setAttribute(t,o.join(Lg)):e.removeAttribute(t)}function Gs(e,t){return e.getAttribute(t)?.match(/\S+/g)??[]}var jg="cdk-describedby-message",zs="cdk-describedby-host",Lu=0,tj=(()=>{class e{_platform=m(pe);_document=m(F);_messageRegistry=new Map;_messagesContainer=null;_id=`${Lu++}`;constructor(){m(Et).load(Vs),this._id=m(Rt)+"-"+Lu++}describe(n,r,o){if(!this._canBeDescribed(n,r))return;let i=Pu(r,o);typeof r!="string"?(Pg(r,this._id),this._messageRegistry.set(i,{messageElement:r,referenceCount:0})):this._messageRegistry.has(i)||this._createMessageElement(r,o),this._isElementDescribedByMessage(n,i)||this._addMessageReference(n,i)}removeDescription(n,r,o){if(!r||!this._isElementNode(n))return;let i=Pu(r,o);if(this._isElementDescribedByMessage(n,i)&&this._removeMessageReference(n,i),typeof r=="string"){let s=this._messageRegistry.get(i);s&&s.referenceCount===0&&this._deleteMessageElement(i)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let n=this._document.querySelectorAll(`[${zs}="${this._id}"]`);for(let r=0;r<n.length;r++)this._removeCdkDescribedByReferenceIds(n[r]),n[r].removeAttribute(zs);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(n,r){let o=this._document.createElement("div");Pg(o,this._id),o.textContent=n,r&&o.setAttribute("role",r),this._createMessagesContainer(),this._messagesContainer.appendChild(o),this._messageRegistry.set(Pu(n,r),{messageElement:o,referenceCount:0})}_deleteMessageElement(n){this._messageRegistry.get(n)?.messageElement?.remove(),this._messageRegistry.delete(n)}_createMessagesContainer(){if(this._messagesContainer)return;let n="cdk-describedby-message-container",r=this._document.querySelectorAll(`.${n}[platform="server"]`);for(let i=0;i<r.length;i++)r[i].remove();let o=this._document.createElement("div");o.style.visibility="hidden",o.classList.add(n),o.classList.add("cdk-visually-hidden"),this._platform.isBrowser||o.setAttribute("platform","server"),this._document.body.appendChild(o),this._messagesContainer=o}_removeCdkDescribedByReferenceIds(n){let r=Gs(n,"aria-describedby").filter(o=>o.indexOf(jg)!=0);n.setAttribute("aria-describedby",r.join(" "))}_addMessageReference(n,r){let o=this._messageRegistry.get(r);l0(n,"aria-describedby",o.messageElement.id),n.setAttribute(zs,this._id),o.referenceCount++}_removeMessageReference(n,r){let o=this._messageRegistry.get(r);o.referenceCount--,u0(n,"aria-describedby",o.messageElement.id),n.removeAttribute(zs)}_isElementDescribedByMessage(n,r){let o=Gs(n,"aria-describedby"),i=this._messageRegistry.get(r),s=i&&i.messageElement.id;return!!s&&o.indexOf(s)!=-1}_canBeDescribed(n,r){if(!this._isElementNode(n))return!1;if(r&&typeof r=="object")return!0;let o=r==null?"":`${r}`.trim(),i=n.getAttribute("aria-label");return o?!i||i.trim()!==o:!1}_isElementNode(n){return n.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Pu(e,t){return typeof e=="string"?`${t||""}/${e}`:e}function Pg(e,t){e.id||(e.id=`${jg}-${t}-${Lu++}`)}var go=function(e){return e[e.NORMAL=0]="NORMAL",e[e.NEGATED=1]="NEGATED",e[e.INVERTED=2]="INVERTED",e}(go||{}),Ws,vn;function lj(){if(vn==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return vn=!1,vn;if("scrollBehavior"in document.documentElement.style)vn=!0;else{let e=Element.prototype.scrollTo;e?vn=!/\{\s*\[native code\]\s*\}/.test(e.toString()):vn=!1}}return vn}function uj(){if(typeof document!="object"||!document)return go.NORMAL;if(Ws==null){let e=document.createElement("div"),t=e.style;e.dir="rtl",t.width="1px",t.overflow="auto",t.visibility="hidden",t.pointerEvents="none",t.position="absolute";let n=document.createElement("div"),r=n.style;r.width="2px",r.height="1px",e.appendChild(n),document.body.appendChild(e),Ws=go.NORMAL,e.scrollLeft===0&&(e.scrollLeft=1,Ws=e.scrollLeft===0?go.NEGATED:go.INVERTED),e.remove()}return Ws}function fj(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}var ar,Bg=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function mj(){if(ar)return ar;if(typeof document!="object"||!document)return ar=new Set(Bg),ar;let e=document.createElement("input");return ar=new Set(Bg.filter(t=>(e.setAttribute("type",t),e.type===t))),ar}function d0(e){return e!=null&&`${e}`!="false"}function _j(e){return e==null?"":typeof e=="string"?e:`${e}px`}function wj(e,t=/\s+/){let n=[];if(e!=null){let r=Array.isArray(e)?e:`${e}`.split(t);for(let o of r){let i=`${o}`.trim();i&&n.push(i)}}return n}var Pe=function(e){return e[e.FADING_IN=0]="FADING_IN",e[e.VISIBLE=1]="VISIBLE",e[e.FADING_OUT=2]="FADING_OUT",e[e.HIDDEN=3]="HIDDEN",e}(Pe||{}),ju=class{_renderer;element;config;_animationForciblyDisabledThroughCss;state=Pe.HIDDEN;constructor(t,n,r,o=!1){this._renderer=t,this.element=n,this.config=r,this._animationForciblyDisabledThroughCss=o}fadeOut(){this._renderer.fadeOutRipple(this)}},Vg=ir({passive:!0,capture:!0}),Bu=class{_events=new Map;addHandler(t,n,r,o){let i=this._events.get(n);if(i){let s=i.get(r);s?s.add(o):i.set(r,new Set([o]))}else this._events.set(n,new Map([[r,new Set([o])]])),t.runOutsideAngular(()=>{document.addEventListener(n,this._delegateEventHandler,Vg)})}removeHandler(t,n,r){let o=this._events.get(t);if(!o)return;let i=o.get(n);i&&(i.delete(r),i.size===0&&o.delete(n),o.size===0&&(this._events.delete(t),document.removeEventListener(t,this._delegateEventHandler,Vg)))}_delegateEventHandler=t=>{let n=Ge(t);n&&this._events.get(t.type)?.forEach((r,o)=>{(o===n||o.contains(n))&&r.forEach(i=>i.handleEvent(t))})}},bo={enterDuration:225,exitDuration:150},f0=800,Hg=ir({passive:!0,capture:!0}),Ug=["mousedown","touchstart"],$g=["mouseup","mouseleave","touchend","touchcancel"],p0=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["ng-component"]],hostAttrs:["mat-ripple-style-loader",""],decls:0,vars:0,template:function(r,o){},styles:[`.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}
`],encapsulation:2,changeDetection:0})}return e})(),yo=class e{_target;_ngZone;_platform;_containerElement;_triggerElement;_isPointerDown=!1;_activeRipples=new Map;_mostRecentTransientRipple;_lastTouchStartEvent;_pointerUpEventsRegistered=!1;_containerRect;static _eventManager=new Bu;constructor(t,n,r,o,i){this._target=t,this._ngZone=n,this._platform=o,o.isBrowser&&(this._containerElement=at(r)),i&&i.get(Et).load(p0)}fadeInRipple(t,n,r={}){let o=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),i=k(k({},bo),r.animation);r.centered&&(t=o.left+o.width/2,n=o.top+o.height/2);let s=r.radius||m0(t,n,o),a=t-o.left,c=n-o.top,l=i.enterDuration,u=document.createElement("div");u.classList.add("mat-ripple-element"),u.style.left=`${a-s}px`,u.style.top=`${c-s}px`,u.style.height=`${s*2}px`,u.style.width=`${s*2}px`,r.color!=null&&(u.style.backgroundColor=r.color),u.style.transitionDuration=`${l}ms`,this._containerElement.appendChild(u);let d=window.getComputedStyle(u),p=d.transitionProperty,f=d.transitionDuration,h=p==="none"||f==="0s"||f==="0s, 0s"||o.width===0&&o.height===0,g=new ju(this,u,r,h);u.style.transform="scale3d(1, 1, 1)",g.state=Pe.FADING_IN,r.persistent||(this._mostRecentTransientRipple=g);let v=null;return!h&&(l||i.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let w=()=>{v&&(v.fallbackTimer=null),clearTimeout(Z),this._finishRippleTransition(g)},U=()=>this._destroyRipple(g),Z=setTimeout(U,l+100);u.addEventListener("transitionend",w),u.addEventListener("transitioncancel",U),v={onTransitionEnd:w,onTransitionCancel:U,fallbackTimer:Z}}),this._activeRipples.set(g,v),(h||!l)&&this._finishRippleTransition(g),g}fadeOutRipple(t){if(t.state===Pe.FADING_OUT||t.state===Pe.HIDDEN)return;let n=t.element,r=k(k({},bo),t.config.animation);n.style.transitionDuration=`${r.exitDuration}ms`,n.style.opacity="0",t.state=Pe.FADING_OUT,(t._animationForciblyDisabledThroughCss||!r.exitDuration)&&this._finishRippleTransition(t)}fadeOutAll(){this._getActiveRipples().forEach(t=>t.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(t=>{t.config.persistent||t.fadeOut()})}setupTriggerEvents(t){let n=at(t);!this._platform.isBrowser||!n||n===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=n,Ug.forEach(r=>{e._eventManager.addHandler(this._ngZone,r,n,this)}))}handleEvent(t){t.type==="mousedown"?this._onMousedown(t):t.type==="touchstart"?this._onTouchStart(t):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{$g.forEach(n=>{this._triggerElement.addEventListener(n,this,Hg)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(t){t.state===Pe.FADING_IN?this._startFadeOutTransition(t):t.state===Pe.FADING_OUT&&this._destroyRipple(t)}_startFadeOutTransition(t){let n=t===this._mostRecentTransientRipple,{persistent:r}=t.config;t.state=Pe.VISIBLE,!r&&(!n||!this._isPointerDown)&&t.fadeOut()}_destroyRipple(t){let n=this._activeRipples.get(t)??null;this._activeRipples.delete(t),this._activeRipples.size||(this._containerRect=null),t===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),t.state=Pe.HIDDEN,n!==null&&(t.element.removeEventListener("transitionend",n.onTransitionEnd),t.element.removeEventListener("transitioncancel",n.onTransitionCancel),n.fallbackTimer!==null&&clearTimeout(n.fallbackTimer)),t.element.remove()}_onMousedown(t){let n=fo(t),r=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+f0;!this._target.rippleDisabled&&!n&&!r&&(this._isPointerDown=!0,this.fadeInRipple(t.clientX,t.clientY,this._target.rippleConfig))}_onTouchStart(t){if(!this._target.rippleDisabled&&!po(t)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let n=t.changedTouches;if(n)for(let r=0;r<n.length;r++)this.fadeInRipple(n[r].clientX,n[r].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(t=>{let n=t.state===Pe.VISIBLE||t.config.terminateOnPointerUp&&t.state===Pe.FADING_IN;!t.config.persistent&&n&&t.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let t=this._triggerElement;t&&(Ug.forEach(n=>e._eventManager.removeHandler(n,t,this)),this._pointerUpEventsRegistered&&($g.forEach(n=>t.removeEventListener(n,this,Hg)),this._pointerUpEventsRegistered=!1))}};function m0(e,t,n){let r=Math.max(Math.abs(e-n.left),Math.abs(e-n.right)),o=Math.max(Math.abs(t-n.top),Math.abs(t-n.bottom));return Math.sqrt(r*r+o*o)}var Vu=new y("mat-ripple-global-options"),Lj=(()=>{class e{_elementRef=m(ee);_animationMode=m(Qn,{optional:!0});color;unbounded;centered;radius=0;animation;get disabled(){return this._disabled}set disabled(n){n&&this.fadeOutAllNonPersistent(),this._disabled=n,this._setupTriggerEventsIfEnabled()}_disabled=!1;get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(n){this._trigger=n,this._setupTriggerEventsIfEnabled()}_trigger;_rippleRenderer;_globalOptions;_isInitialized=!1;constructor(){let n=m(O),r=m(pe),o=m(Vu,{optional:!0}),i=m(J);this._globalOptions=o||{},this._rippleRenderer=new yo(this,n,this._elementRef,r,i)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:k(k(k({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(n,r=0,o){return typeof n=="number"?this._rippleRenderer.fadeInRipple(n,r,k(k({},this.rippleConfig),o)):this._rippleRenderer.fadeInRipple(0,0,k(k({},this.rippleConfig),n))}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(r,o){r&2&&Se("mat-ripple-unbounded",o.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return e})();var h0={capture:!0},g0=["focus","mousedown","mouseenter","touchstart"],Hu="mat-ripple-loader-uninitialized",Uu="mat-ripple-loader-class-name",zg="mat-ripple-loader-centered",qs="mat-ripple-loader-disabled",Gg=(()=>{class e{_document=m(F);_animationMode=m(Qn,{optional:!0});_globalRippleOptions=m(Vu,{optional:!0});_platform=m(pe);_ngZone=m(O);_injector=m(J);_eventCleanups;_hosts=new Map;constructor(){let n=m(nt).createRenderer(null,null);this._eventCleanups=this._ngZone.runOutsideAngular(()=>g0.map(r=>bn(n,this._document,r,this._onInteraction,h0)))}ngOnDestroy(){let n=this._hosts.keys();for(let r of n)this.destroyRipple(r);this._eventCleanups.forEach(r=>r())}configureRipple(n,r){n.setAttribute(Hu,this._globalRippleOptions?.namespace??""),(r.className||!n.hasAttribute(Uu))&&n.setAttribute(Uu,r.className||""),r.centered&&n.setAttribute(zg,""),r.disabled&&n.setAttribute(qs,"")}setDisabled(n,r){let o=this._hosts.get(n);o?(o.target.rippleDisabled=r,!r&&!o.hasSetUpEvents&&(o.hasSetUpEvents=!0,o.renderer.setupTriggerEvents(n))):r?n.setAttribute(qs,""):n.removeAttribute(qs)}_onInteraction=n=>{let r=Ge(n);if(r instanceof HTMLElement){let o=r.closest(`[${Hu}="${this._globalRippleOptions?.namespace??""}"]`);o&&this._createRipple(o)}};_createRipple(n){if(!this._document||this._hosts.has(n))return;n.querySelector(".mat-ripple")?.remove();let r=this._document.createElement("span");r.classList.add("mat-ripple",n.getAttribute(Uu)),n.append(r);let o=this._animationMode==="NoopAnimations",i=this._globalRippleOptions,s=o?0:i?.animation?.enterDuration??bo.enterDuration,a=o?0:i?.animation?.exitDuration??bo.exitDuration,c={rippleDisabled:o||i?.disabled||n.hasAttribute(qs),rippleConfig:{centered:n.hasAttribute(zg),terminateOnPointerUp:i?.terminateOnPointerUp,animation:{enterDuration:s,exitDuration:a}}},l=new yo(c,this._ngZone,r,this._platform,this._injector),u=!c.rippleDisabled;u&&l.setupTriggerEvents(n),this._hosts.set(n,{target:c,renderer:l,hasSetUpEvents:u}),n.removeAttribute(Hu)}destroyRipple(n){let r=this._hosts.get(n);r&&(r.renderer._removeTriggerEvents(),this._hosts.delete(n))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Wg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["structural-styles"]],decls:0,vars:0,template:function(r,o){},styles:[`.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}
`],encapsulation:2,changeDetection:0})}return e})();var b0=["mat-icon-button",""],y0=["*"];var v0=new y("MAT_BUTTON_CONFIG");var D0=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}],Zs=(()=>{class e{_elementRef=m(ee);_ngZone=m(O);_animationMode=m(Qn,{optional:!0});_focusMonitor=m(js);_rippleLoader=m(Gg);_isFab=!1;color;get disableRipple(){return this._disableRipple}set disableRipple(n){this._disableRipple=n,this._updateRippleDisabled()}_disableRipple=!1;get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._updateRippleDisabled()}_disabled=!1;ariaDisabled;disabledInteractive;constructor(){m(Et).load(Wg);let n=m(v0,{optional:!0}),r=this._elementRef.nativeElement,o=r.classList;this.disabledInteractive=n?.disabledInteractive??!1,this.color=n?.color??null,this._rippleLoader?.configureRipple(r,{className:"mat-mdc-button-ripple"});for(let{attribute:i,mdcClasses:s}of D0)r.hasAttribute(i)&&o.add(...s)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(n="program",r){n?this._focusMonitor.focusVia(this._elementRef.nativeElement,n,r):this._elementRef.nativeElement.focus(r)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",ke],disabled:[2,"disabled","disabled",ke],ariaDisabled:[2,"aria-disabled","ariaDisabled",ke],disabledInteractive:[2,"disabledInteractive","disabledInteractive",ke]}})}return e})();var _0=(()=>{class e extends Zs{constructor(){super(),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["button","mat-icon-button",""]],hostVars:14,hostBindings:function(r,o){r&2&&(fn("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Zr(o.color?"mat-"+o.color:""),Se("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[er],attrs:b0,ngContentSelectors:y0,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,o){r&1&&(Ft(),yt(0,"span",0),we(1),yt(2,"span",1)(3,"span",2))},styles:[`.mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:""}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return e})();var E0=new y("cdk-dir-doc",{providedIn:"root",factory:w0});function w0(){return m(F)}var I0=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function qg(e){let t=e?.toLowerCase()||"";return t==="auto"&&typeof navigator<"u"&&navigator?.language?I0.test(navigator.language)?"rtl":"ltr":t==="rtl"?"rtl":"ltr"}var C0=(()=>{class e{value="ltr";change=new ye;constructor(){let n=m(E0,{optional:!0});if(n){let r=n.body?n.body.dir:null,o=n.documentElement?n.documentElement.dir:null;this.value=qg(r||o||"ltr")}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var $u=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({})}return e})();var wt=(()=>{class e{constructor(){m(Us)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[$u,$u]})}return e})();var Zg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[wt,wt]})}return e})();var M0=["mat-button",""],Kg=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],Qg=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var T0=["mat-mini-fab",""],x0=`.mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:""}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}
`,NB=(()=>{class e extends Zs{static \u0275fac=(()=>{let n;return function(o){return(n||(n=wl(e)))(o||e)}})();static \u0275cmp=Ee({type:e,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(r,o){r&2&&(fn("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Zr(o.color?"mat-"+o.color:""),Se("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[er],attrs:M0,ngContentSelectors:Qg,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,o){r&1&&(Ft(Kg),yt(0,"span",0),we(1),pn(2,"span",1),we(3,1),mn(),we(4,2),yt(5,"span",2)(6,"span",3)),r&2&&Se("mdc-button__ripple",!o._isFab)("mdc-fab__ripple",o._isFab)},styles:[`.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:""}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return e})();var S0=new y("mat-mdc-fab-default-options",{providedIn:"root",factory:Xg});function Xg(){return{color:"accent"}}var Yg=Xg();var AB=(()=>{class e extends Zs{_options=m(S0,{optional:!0});_isFab=!0;constructor(){super(),this._options=this._options||Yg,this.color=this._options.color||Yg.color}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["button","mat-mini-fab",""]],hostVars:14,hostBindings:function(r,o){r&2&&(fn("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Zr(o.color?"mat-"+o.color:""),Se("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[er],attrs:T0,ngContentSelectors:Qg,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,o){r&1&&(Ft(Kg),yt(0,"span",0),we(1),pn(2,"span",1),we(3,1),mn(),we(4,2),yt(5,"span",2)(6,"span",3)),r&2&&Se("mdc-button__ripple",!o._isFab)("mdc-fab__ripple",o._isFab)},styles:[x0],encapsulation:2,changeDetection:0})}return e})();var RB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[wt,Zg,wt]})}return e})();var N0=["*"];var A0=[[["","mat-card-avatar",""],["","matCardAvatar",""]],[["mat-card-title"],["mat-card-subtitle"],["","mat-card-title",""],["","mat-card-subtitle",""],["","matCardTitle",""],["","matCardSubtitle",""]],"*"],R0=["[mat-card-avatar], [matCardAvatar]",`mat-card-title, mat-card-subtitle,
      [mat-card-title], [mat-card-subtitle],
      [matCardTitle], [matCardSubtitle]`,"*"],O0=new y("MAT_CARD_CONFIG"),UB=(()=>{class e{appearance;constructor(){let n=m(O0,{optional:!0});this.appearance=n?.appearance||"raised"}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["mat-card"]],hostAttrs:[1,"mat-mdc-card","mdc-card"],hostVars:4,hostBindings:function(r,o){r&2&&Se("mat-mdc-card-outlined",o.appearance==="outlined")("mdc-card--outlined",o.appearance==="outlined")},inputs:{appearance:"appearance"},exportAs:["matCard"],ngContentSelectors:N0,decls:1,vars:0,template:function(r,o){r&1&&(Ft(),we(0))},styles:[`.mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:"";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:""}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}
`],encapsulation:2,changeDetection:0})}return e})(),$B=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-title"],["","mat-card-title",""],["","matCardTitle",""]],hostAttrs:[1,"mat-mdc-card-title"]})}return e})();var zB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-content"]],hostAttrs:[1,"mat-mdc-card-content"]})}return e})(),GB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-subtitle"],["","mat-card-subtitle",""],["","matCardSubtitle",""]],hostAttrs:[1,"mat-mdc-card-subtitle"]})}return e})(),WB=(()=>{class e{align="start";static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-actions"]],hostAttrs:[1,"mat-mdc-card-actions","mdc-card__actions"],hostVars:2,hostBindings:function(r,o){r&2&&Se("mat-mdc-card-actions-align-end",o.align==="end")},inputs:{align:"align"},exportAs:["matCardActions"]})}return e})(),qB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Ee({type:e,selectors:[["mat-card-header"]],hostAttrs:[1,"mat-mdc-card-header"],ngContentSelectors:R0,decls:4,vars:0,consts:[[1,"mat-mdc-card-header-text"]],template:function(r,o){r&1&&(Ft(A0),we(0),pn(1,"div",0),we(2,1),mn(),we(3,2))},encapsulation:2,changeDetection:0})}return e})();var ZB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[wt,wt]})}return e})();var KB={production:!1,supabaseUrl:"https://fiaqzhajyfkjbwushngn.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v8UgUwEetoAIwSqMbYSU77pK8ACM7Wryl2BCebW80ZQ",squareApplicationId:"sandbox-sq0idb-e6g3DAdm9fjgWfBGyXyk_g",squareLocationId:"sq0idp-6i0oiAmtrJ-bKkU9PIk9uQ",squareAccessToken:"EAAAl-JzTB0lwXa7T2FV7NJrF2gK80wgPwlrl3RfH3joiIH3A1HEq57Ak_og5KqY",stripePublishableKey:"pk_live_51QXPoUKBQR9HiWVf963rrIm0mm1h9v0RanSMS3u4t3OrQh6930W67JsmVn2E7EVR6eBzLhMfejvfJL0umcIbukFW00ELXE0ANo",stripeSecretKey:"",googleMapsApiKey:"AIzaSyDNfNjbZAkM9p_SmPwc_SZLDyKNMw07bc8",twilioAccountSid:"**********************************",twilioAuthToken:"60bdb78931f35cce4e8f47c3fba3f8ad",twilioPhoneNumber:"+***********"};export{Y as a,ob as b,N as c,ra as d,oa as e,B as f,$t as g,lr as h,zt as i,Ae as j,In as k,mb as l,hb as m,ct as n,rb as o,bb as p,X as q,sa as r,Wt as s,dr as t,Cn as u,Tb as v,xb as w,fr as x,Sb as y,Nb as z,Ab as A,Me as B,Rb as C,md as D,aa as E,qt as F,pr as G,mr as H,ca as I,la as J,Fb as K,ua as L,Pb as M,Lb as N,jb as O,fa as P,Bb as Q,hr as R,pa as S,ma as T,Mn as U,Vb as V,ha as W,D as X,_f as Y,b as Z,oe as _,vO as $,y as aa,R as ba,C as ca,m as da,il as ea,Nf as fa,jr as ga,_y as ha,Oe as ia,zi as ja,qi as ka,DO as la,_O as ma,EO as na,wO as oa,wl as pa,yp as qa,J as ra,Fd as sa,Xi as ta,Zn as ua,ye as va,O as wa,Je as xa,IO as ya,ee as za,es as Aa,Ml as Ba,Bn as Ca,ot as Da,Qn as Ea,dn as Fa,$v as Ga,Nl as Ha,it as Ia,dD as Ja,CO as Ka,MO as La,St as Ma,nt as Na,Gr as Oa,W as Pa,OO as Qa,kt as Ra,FO as Sa,Un as Ta,yE as Ua,vE as Va,Ee as Wa,ie as Xa,q as Ya,Wr as Za,er as _a,jO as $a,jE as ab,HE as bb,ds as cb,BO as db,UE as eb,Nt as fb,fn as gb,ow as hb,iw as ib,Se as jb,Zr as kb,VO as lb,HO as mb,UO as nb,$O as ob,zO as pb,pn as qb,mn as rb,yt as sb,Jm as tb,eh as ub,vw as vb,GO as wb,_w as xb,Sw as yb,WO as zb,Ft as Ab,we as Bb,Ow as Cb,qO as Db,ZO as Eb,YO as Fb,KO as Gb,QO as Hb,XO as Ib,JO as Jb,ek as Kb,Fw as Lb,rh as Mb,Pw as Nb,Lw as Ob,tk as Pb,jw as Qb,Vw as Rb,Gw as Sb,nk as Tb,rk as Ub,ok as Vb,ik as Wb,sk as Xb,ak as Yb,ck as Zb,lk as _b,uk as $b,dk as ac,fk as bc,Yr as cc,Xl as dc,Jl as ec,ke as fc,dh as gc,tu as hc,pk as ic,nu as jc,ph as kc,mk as lc,F as mc,vt as nc,mI as oc,tr as pc,yh as qc,vh as rc,yI as sc,BI as tc,jh as uc,VI as vc,Bh as wc,HI as xc,UI as yc,$I as zc,WI as Ac,ZI as Bc,YI as Cc,XI as Dc,Uh as Ec,mu as Fc,SF as Gc,Eu as Hc,fC as Ic,ag as Jc,BC as Kc,e1 as Lc,VC as Mc,Tu as Nc,Ge as Oc,bn as Pc,pe as Qc,ir as Rc,Su as Sc,mg as Tc,at as Uc,js as Vc,UC as Wc,Et as Xc,Vs as Yc,Au as Zc,Eg as _c,nL as $c,Cg as ad,Sg as bd,Ag as cd,r0 as dd,i0 as ed,s0 as fd,a0 as gd,Fg as hd,ku as id,Fu as jd,l0 as kd,u0 as ld,tj as md,go as nd,lj as od,uj as pd,fj as qd,mj as rd,d0 as sd,_j as td,wj as ud,yo as vd,Vu as wd,Lj as xd,Gg as yd,Wg as zd,_0 as Ad,C0 as Bd,$u as Cd,wt as Dd,Zg as Ed,NB as Fd,S0 as Gd,AB as Hd,RB as Id,KB as Jd,UB as Kd,$B as Ld,zB as Md,GB as Nd,WB as Od,qB as Pd,ZB as Qd};
