import{b as Ge}from"./chunk-Q62AX3L3.js";import{c as H,d as z,e as G,g as Ie,j as De,k as Ve,l as Te,m as Oe,n as Ae,o as Fe,p as Be,q as Ue,r as Ne,s as je,t as $e,u as qe,w as He,x as ze}from"./chunk-WJOJBTBX.js";import{b as Re,c as we,d as Ee,f as Pe}from"./chunk-O2B7MD3G.js";import{a as ye,b as Me}from"./chunk-Q5CX2EZL.js";import{a as ke}from"./chunk-CTHHBRRU.js";import"./chunk-JFL7URHX.js";import{a as q,b as D}from"./chunk-NXDGXY44.js";import"./chunk-RAIBPYI3.js";import{a as j,b as $}from"./chunk-6WJZ7JAD.js";import"./chunk-FMN32A34.js";import"./chunk-H3RPDCYZ.js";import{c as se}from"./chunk-XGZOMXTW.js";import{C as Ce,F as be,H as xe,I as Se,J as V,b as ce,d as h,f as de,g as me,k as pe,n as ue,o as fe,s as _e,u as ge,w as he,x as ve}from"./chunk-UDNA73AU.js";import{Ad as N,Ba as J,Bc as le,Ec as R,Fd as k,Id as I,Ja as K,Kb as o,Kd as T,La as a,Lb as b,Ld as O,Mb as U,Md as A,Pa as g,Pd as F,Qd as w,Wa as M,Wb as Q,Yb as ee,Z as ne,ab as d,ca as re,g as ie,hb as s,la as x,ma as S,qb as n,rb as r,sb as f,tb as v,tc as ae,ub as C,va as oe,vc as P,wb as y,yb as _,zb as u}from"./chunk-L5P5FOLI.js";import"./chunk-X5YLR3NI.js";import{a as E,b as B,i as m}from"./chunk-ODN5LVDJ.js";var L=class i{constructor(t){this.authService=t;this.supabase=t.supabase,this.initializeMockVehicles()}supabase;vehiclesSubject=new ie([]);vehicles$=this.vehiclesSubject.asObservable();initializeMockVehicles(){let t=[{id:"1",driver_id:"driver1",make:"Toyota",model:"Camry",year:2020,color:"Silver",license_plate:"ABC123",capacity:4,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"2",driver_id:"driver2",make:"Honda",model:"Accord",year:2019,color:"Black",license_plate:"XYZ789",capacity:5,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}];this.vehiclesSubject.next(t)}getDriverVehicles(t){return m(this,null,function*(){try{return this.vehiclesSubject.value.filter(e=>e.driver_id===t)}catch(e){return console.error("Error fetching driver vehicles:",e),[]}})}addVehicle(t){return m(this,null,function*(){try{let e=B(E({},t),{id:Math.random().toString(36).substring(2,9),created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),l=this.vehiclesSubject.value;return this.vehiclesSubject.next([...l,e]),e}catch(e){return console.error("Error adding vehicle:",e),null}})}updateVehicle(t,e){return m(this,null,function*(){try{let c=this.vehiclesSubject.value.map(p=>p.id===t?B(E(E({},p),e),{updated_at:new Date().toISOString()}):p);return this.vehiclesSubject.next(c),!0}catch(l){return console.error("Error updating vehicle:",l),!1}})}deleteVehicle(t){return m(this,null,function*(){try{let l=this.vehiclesSubject.value.filter(c=>c.id!==t);return this.vehiclesSubject.next(l),!0}catch(e){return console.error("Error deleting vehicle:",e),!1}})}static \u0275fac=function(e){return new(e||i)(re(V))};static \u0275prov=ne({token:i,factory:i.\u0275fac,providedIn:"root"})};function Qe(i,t){i&1&&(n(0,"mat-error"),o(1," Full name is required "),r())}function et(i,t){i&1&&(n(0,"mat-error"),o(1," Phone number is required "),r())}var W=class i{constructor(t,e,l,c){this.fb=t;this.authService=e;this.vehicleService=l;this.snackBar=c;this.profileForm=this.fb.group({full_name:["",h.required],phone:["",h.required]}),this.vehicleForm=this.fb.group({make:["",h.required],model:["",h.required],year:["",[h.required,h.min(1990),h.max(this.currentYear)]],color:["",h.required],license_plate:["",h.required],capacity:[4,[h.required,h.min(1),h.max(10)]]})}profileForm;vehicleForm;vehicles=[];currentUser=null;editingVehicle=null;currentYear=new Date().getFullYear();ngOnInit(){this.loadUserProfile(),this.loadVehicles()}loadUserProfile(){return m(this,null,function*(){try{this.currentUser=yield this.authService.getCurrentUser(),this.currentUser&&this.profileForm.patchValue({full_name:this.currentUser.full_name||"",phone:this.currentUser.phone||""})}catch(t){console.error("Error loading user profile:",t),this.snackBar.open("Failed to load profile","Close",{duration:3e3})}})}loadVehicles(){return m(this,null,function*(){try{this.currentUser&&(this.vehicles=yield this.vehicleService.getDriverVehicles(this.currentUser.id))}catch(t){console.error("Error loading vehicles:",t),this.snackBar.open("Failed to load vehicles","Close",{duration:3e3})}})}updateProfile(){return m(this,null,function*(){if(!this.profileForm.invalid)try{if(yield this.authService.updateProfile(this.profileForm.value))this.snackBar.open("Profile updated successfully","Close",{duration:3e3});else throw new Error("Failed to update profile")}catch(t){console.error("Error updating profile:",t),this.snackBar.open("Failed to update profile","Close",{duration:3e3})}})}editVehicle(t){this.editingVehicle=t,this.vehicleForm.patchValue({make:t.make,model:t.model,year:t.year,color:t.color,license_plate:t.license_plate,capacity:t.capacity})}cancelEdit(){this.editingVehicle=null,this.vehicleForm.reset({capacity:4})}saveVehicle(){return m(this,null,function*(){if(!(this.vehicleForm.invalid||!this.currentUser))try{if(this.editingVehicle)if(yield this.vehicleService.updateVehicle(this.editingVehicle.id,this.vehicleForm.value))this.snackBar.open("Vehicle updated successfully","Close",{duration:3e3}),this.cancelEdit(),this.loadVehicles();else throw new Error("Failed to update vehicle");else{let t=B(E({},this.vehicleForm.value),{driver_id:this.currentUser.id});if(yield this.vehicleService.addVehicle(t))this.snackBar.open("Vehicle added successfully","Close",{duration:3e3}),this.vehicleForm.reset({capacity:4}),this.loadVehicles();else throw new Error("Failed to add vehicle")}}catch(t){console.error("Error saving vehicle:",t),this.snackBar.open("Failed to save vehicle","Close",{duration:3e3})}})}deleteVehicle(t){return m(this,null,function*(){try{if(yield this.vehicleService.deleteVehicle(t))this.snackBar.open("Vehicle deleted successfully","Close",{duration:3e3}),this.loadVehicles();else throw new Error("Failed to delete vehicle")}catch(e){console.error("Error deleting vehicle:",e),this.snackBar.open("Failed to delete vehicle","Close",{duration:3e3})}})}static \u0275fac=function(e){return new(e||i)(g(_e),g(V),g(L),g(j))};static \u0275cmp=M({type:i,selectors:[["app-driver-profile"]],decls:19,vars:4,consts:[[1,"profile-container"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","full_name","placeholder","Enter your full name"],[4,"ngIf"],["matInput","","formControlName","phone","placeholder","Enter your phone number"],["mat-raised-button","","color","primary","type","submit",3,"disabled"]],template:function(e,l){if(e&1&&(n(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),o(4,"Driver Profile"),r()(),n(5,"mat-card-content")(6,"form",1),_("ngSubmit",function(){return l.updateProfile()}),n(7,"mat-form-field",2)(8,"mat-label"),o(9,"Full Name"),r(),f(10,"input",3),d(11,Qe,2,0,"mat-error",4),r(),n(12,"mat-form-field",2)(13,"mat-label"),o(14,"Phone Number"),r(),f(15,"input",5),d(16,et,2,0,"mat-error",4),r(),n(17,"button",6),o(18," Update Profile "),r()()()()()),e&2){let c,p;a(6),s("formGroup",l.profileForm),a(5),s("ngIf",(c=l.profileForm.get("full_name"))==null?null:c.hasError("required")),a(5),s("ngIf",(p=l.profileForm.get("phone"))==null?null:p.hasError("required")),a(),s("disabled",l.profileForm.invalid||l.profileForm.pristine)}},dependencies:[R,P,ge,pe,ce,de,me,ue,fe,w,T,A,F,O,be,Ce,he,ve,Se,xe,I,k,$,Pe,D],styles:[".profile-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.vehicle-card[_ngcontent-%COMP%]{margin-top:20px}.vehicle-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 0;border-bottom:1px solid #eee}.vehicle-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.vehicle-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-weight:500}.vehicle-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;color:#666}.vehicle-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.divider[_ngcontent-%COMP%]{margin:16px 0}.vehicle-form[_ngcontent-%COMP%]{margin-top:16px}.form-row[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:8px}.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1}.form-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-top:16px}.no-vehicles[_ngcontent-%COMP%]{padding:16px 0;color:#666;font-style:italic}"]})};function it(i,t){if(i&1&&(n(0,"div",18)(1,"p")(2,"strong"),o(3,"Distance:"),r(),o(4),r(),n(5,"p")(6,"strong"),o(7,"Estimated Time:"),r(),o(8),r()()),i&2){let e=u(2);a(4),U(" ",e.ride.distance_miles," miles"),a(4),U(" ",e.ride.duration_minutes," minutes")}}function nt(i,t){if(i&1){let e=y();n(0,"div",1)(1,"mat-card",2)(2,"mat-card-header")(3,"mat-card-title"),o(4,"Navigation"),r(),n(5,"button",3),_("click",function(){x(e);let c=u();return S(c.closeNavigation())}),n(6,"mat-icon"),o(7,"close"),r()()(),n(8,"mat-card-content")(9,"div",4)(10,"div",5)(11,"div",6)(12,"mat-icon",7),o(13,"location_on"),r(),n(14,"div",8)(15,"span",9),o(16,"Pickup Location:"),r(),n(17,"span",10),o(18),r()()(),n(19,"div",6)(20,"mat-icon",11),o(21,"flag"),r(),n(22,"div",8)(23,"span",9),o(24,"Dropoff Location:"),r(),n(25,"span",10),o(26),r()()()(),f(27,"app-map-display",12),d(28,it,9,2,"div",13),n(29,"div",14)(30,"a",15)(31,"button",16)(32,"mat-icon"),o(33,"navigation"),r(),o(34," Navigate to Pickup "),r()(),n(35,"a",15)(36,"button",17)(37,"mat-icon"),o(38,"navigation"),r(),o(39," Navigate to Dropoff "),r()()()()()()()}if(i&2){let e=u();a(18),b(e.ride.pickup_location),a(8),b(e.ride.dropoff_location),a(),s("origin",e.ride.pickup_location)("destination",e.ride.dropoff_location),a(),s("ngIf",e.ride.distance_miles&&e.ride.duration_minutes),a(2),s("href",e.googleMapsPickupUrl,K),a(5),s("href",e.googleMapsDropoffUrl,K)}}var X=class i{constructor(t){this.locationService=t}ride=null;close=new oe;googleMapsPickupUrl="";googleMapsDropoffUrl="";ngOnInit(){this.generateNavigationLinks()}generateNavigationLinks(){this.ride&&(this.googleMapsPickupUrl=this.locationService.getGoogleMapsUrl(this.ride.pickup_location),this.googleMapsDropoffUrl=this.locationService.getGoogleMapsUrl(this.ride.dropoff_location))}closeNavigation(){this.close.emit()}static \u0275fac=function(e){return new(e||i)(g(ye))};static \u0275cmp=M({type:i,selectors:[["app-ride-navigation"]],inputs:{ride:"ride"},outputs:{close:"close"},decls:1,vars:1,consts:[["class","navigation-overlay",4,"ngIf"],[1,"navigation-overlay"],[1,"navigation-card"],["mat-icon-button","",1,"close-button",3,"click"],[1,"navigation-details"],[1,"location-info"],[1,"location-item"],[1,"location-icon","pickup"],[1,"location-text"],[1,"location-label"],[1,"location-value"],[1,"location-icon","dropoff"],[3,"origin","destination"],["class","route-info",4,"ngIf"],[1,"navigation-links"],["target","_blank",1,"nav-link",3,"href"],["mat-raised-button","","color","primary"],["mat-raised-button","","color","accent"],[1,"route-info"]],template:function(e,l){e&1&&d(0,nt,40,7,"div",0),e&2&&s("ngIf",l.ride)},dependencies:[R,P,w,T,A,F,O,I,k,N,D,q,Me],styles:[".navigation-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.navigation-card[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;overflow-y:auto}.close-button[_ngcontent-%COMP%]{position:absolute;right:8px;top:8px}.navigation-details[_ngcontent-%COMP%]{padding:16px 0}.location-info[_ngcontent-%COMP%]{margin-bottom:24px}.location-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:16px}.location-icon[_ngcontent-%COMP%]{margin-right:16px;color:#3f51b5}.location-icon.pickup[_ngcontent-%COMP%]{color:#4caf50}.location-icon.dropoff[_ngcontent-%COMP%]{color:#f44336}.location-text[_ngcontent-%COMP%]{display:flex;flex-direction:column}.location-label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.location-value[_ngcontent-%COMP%]{color:#666}.route-info[_ngcontent-%COMP%]{background-color:#f5f5f5;border-radius:4px;padding:16px;margin-bottom:24px}.route-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}.navigation-links[_ngcontent-%COMP%]{display:flex;justify-content:space-around;flex-wrap:wrap;gap:16px}.nav-link[_ngcontent-%COMP%]{text-decoration:none}"]})};function ot(i,t){i&1&&(n(0,"div",11),f(1,"mat-spinner",12),n(2,"p"),o(3,"Loading rides..."),r()())}function at(i,t){i&1&&(n(0,"div",13)(1,"p"),o(2,"No available ride requests at this time."),r()())}function lt(i,t){i&1&&(n(0,"th",24),o(1,"Pickup"),r())}function st(i,t){if(i&1&&(n(0,"td",25),o(1),r()),i&2){let e=t.$implicit;a(),b(e.pickup_location)}}function ct(i,t){i&1&&(n(0,"th",24),o(1,"Destination"),r())}function dt(i,t){if(i&1&&(n(0,"td",25),o(1),r()),i&2){let e=t.$implicit;a(),b(e.dropoff_location)}}function mt(i,t){i&1&&(n(0,"th",24),o(1,"Time"),r())}function pt(i,t){if(i&1&&(n(0,"td",25),o(1),Q(2,"date"),r()),i&2){let e=t.$implicit;a(),b(ee(2,1,e.pickup_time,"short"))}}function ut(i,t){i&1&&(n(0,"th",24),o(1,"Fare"),r())}function ft(i,t){if(i&1&&(n(0,"td",25),o(1),r()),i&2){let e=t.$implicit;a(),b(e.fare?"$"+e.fare:"TBD")}}function _t(i,t){i&1&&(n(0,"th",24),o(1,"Actions"),r())}function gt(i,t){if(i&1){let e=y();n(0,"td",25)(1,"button",26),_("click",function(){let c=x(e).$implicit,p=u(2);return S(p.acceptRide(c.id))}),o(2," Accept "),r()()}}function ht(i,t){i&1&&f(0,"tr",27)}function vt(i,t){i&1&&f(0,"tr",28)}function Ct(i,t){if(i&1&&(n(0,"table",14),v(1,15),d(2,lt,2,0,"th",16)(3,st,2,1,"td",17),C(),v(4,18),d(5,ct,2,0,"th",16)(6,dt,2,1,"td",17),C(),v(7,19),d(8,mt,2,0,"th",16)(9,pt,3,4,"td",17),C(),v(10,20),d(11,ut,2,0,"th",16)(12,ft,2,1,"td",17),C(),v(13,21),d(14,_t,2,0,"th",16)(15,gt,3,0,"td",17),C(),d(16,ht,1,0,"tr",22)(17,vt,1,0,"tr",23),r()),i&2){let e=u();s("dataSource",e.availableRides()),a(16),s("matHeaderRowDef",e.availableColumns),a(),s("matRowDefColumns",e.availableColumns)}}function bt(i,t){i&1&&(n(0,"div",11),f(1,"mat-spinner",12),n(2,"p"),o(3,"Loading rides..."),r()())}function xt(i,t){i&1&&(n(0,"div",13)(1,"p"),o(2,"You don't have any assigned rides."),r()())}function St(i,t){i&1&&(n(0,"th",24),o(1,"Pickup"),r())}function yt(i,t){if(i&1&&(n(0,"td",25),o(1),r()),i&2){let e=t.$implicit;a(),b(e.pickup_location)}}function Mt(i,t){i&1&&(n(0,"th",24),o(1,"Destination"),r())}function Rt(i,t){if(i&1&&(n(0,"td",25),o(1),r()),i&2){let e=t.$implicit;a(),b(e.dropoff_location)}}function wt(i,t){i&1&&(n(0,"th",24),o(1,"Time"),r())}function Et(i,t){if(i&1&&(n(0,"td",25),o(1),Q(2,"date"),r()),i&2){let e=t.$implicit;a(),b(ee(2,1,e.pickup_time,"short"))}}function Pt(i,t){i&1&&(n(0,"th",24),o(1,"Status"),r())}function kt(i,t){if(i&1&&(n(0,"td",25)(1,"span",30),o(2),r()()),i&2){let e=t.$implicit;a(),s("ngClass","status-"+e.status),a(),U(" ",e.status," ")}}function It(i,t){i&1&&(n(0,"th",24),o(1,"Fare"),r())}function Dt(i,t){if(i&1&&(n(0,"td",25),o(1),r()),i&2){let e=t.$implicit;a(),b(e.fare?"$"+e.fare:"TBD")}}function Vt(i,t){i&1&&(n(0,"th",24),o(1,"Actions"),r())}function Tt(i,t){if(i&1){let e=y();n(0,"button",26),_("click",function(){x(e);let c=u().$implicit,p=u(2);return S(p.startRide(c.id))}),o(1," Start Ride "),r()}}function Ot(i,t){if(i&1){let e=y();n(0,"button",35),_("click",function(){x(e);let c=u().$implicit,p=u(2);return S(p.completeRide(c.id))}),o(1," Complete "),r()}}function At(i,t){if(i&1){let e=y();n(0,"button",36),_("click",function(){x(e);let c=u().$implicit,p=u(2);return S(p.showNavigation(c))}),n(1,"mat-icon"),o(2,"navigation"),r()()}}function Ft(i,t){if(i&1){let e=y();n(0,"td",25),d(1,Tt,2,0,"button",31)(2,Ot,2,0,"button",32)(3,At,3,0,"button",33),n(4,"button",34),_("click",function(){let c=x(e).$implicit,p=u(2);return S(p.viewRideDetails(c.id))}),n(5,"mat-icon"),o(6,"visibility"),r()()()}if(i&2){let e=t.$implicit;a(),s("ngIf",e.status==="assigned"),a(),s("ngIf",e.status==="in-progress"),a(),s("ngIf",e.status!=="completed")}}function Bt(i,t){i&1&&f(0,"tr",27)}function Ut(i,t){i&1&&f(0,"tr",28)}function Nt(i,t){if(i&1&&(n(0,"table",14),v(1,15),d(2,St,2,0,"th",16)(3,yt,2,1,"td",17),C(),v(4,18),d(5,Mt,2,0,"th",16)(6,Rt,2,1,"td",17),C(),v(7,19),d(8,wt,2,0,"th",16)(9,Et,3,4,"td",17),C(),v(10,29),d(11,Pt,2,0,"th",16)(12,kt,3,2,"td",17),C(),v(13,20),d(14,It,2,0,"th",16)(15,Dt,2,1,"td",17),C(),v(16,21),d(17,Vt,2,0,"th",16)(18,Ft,7,3,"td",17),C(),d(19,Bt,1,0,"tr",22)(20,Ut,1,0,"tr",23),r()),i&2){let e=u();s("dataSource",e.myRides()),a(19),s("matHeaderRowDef",e.myRidesColumns),a(),s("matRowDefColumns",e.myRidesColumns)}}function jt(i,t){if(i&1){let e=y();n(0,"app-ride-navigation",37),_("close",function(){x(e);let c=u();return S(c.selectedRide=null)}),r()}if(i&2){let e=u();s("ride",e.selectedRide)}}function $t(i,t){if(i&1){let e=y();n(0,"div",38)(1,"app-ride-detail",39),_("rideUpdated",function(c){x(e);let p=u();return S(p.onRideUpdated(c))}),r()()}if(i&2){let e=u();a(),s("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}var Z=class i{constructor(t,e,l,c,p){this.rideService=t;this.authService=e;this.messageService=l;this.router=c;this.snackBar=p}currentUser=null;selectedRide=null;selectedRideId=null;loading=!1;ridesSubscription=null;availableRides=J([]);myRides=J([]);availableColumns=["pickup_location","dropoff_location","pickup_time","actions"];myRidesColumns=["pickup_location","dropoff_location","pickup_time","status","actions"];ngOnInit(){return m(this,null,function*(){yield this.loadCurrentUser(),this.loadRides()})}ngOnDestroy(){this.ridesSubscription&&this.ridesSubscription.unsubscribe()}initializeRides(){return m(this,null,function*(){try{yield this.loadRides(),this.setupRideSubscription(),console.log("Rides initialized successfully")}catch(t){console.error("Error initializing rides:",t),this.snackBar.open("Failed to initialize rides","Close",{duration:3e3})}})}loadCurrentUser(){return m(this,null,function*(){try{if(this.loading=!0,this.currentUser=yield this.authService.getCurrentUser(),this.currentUser)yield this.initializeRides();else throw new Error("User not authenticated")}catch(t){console.error("Error loading current user:",t),this.snackBar.open("Failed to load user information","Close",{duration:3e3})}finally{this.loading=!1}})}fetchRides(){return m(this,null,function*(){if(this.currentUser)try{let[t,e]=yield Promise.all([this.rideService.getAvailableRides(),this.rideService.getDriverRides(this.currentUser.id)]);this.availableRides.set(t),this.myRides.set(e)}catch(t){throw console.error("Error fetching rides:",t),t}})}setupRideSubscription(){this.currentUser&&(this.ridesSubscription=this.rideService.rides$.subscribe(t=>{this.currentUser&&(this.availableRides.set(t.filter(e=>e.status==="requested")),this.myRides.set(t.filter(e=>e.driver_id===this.currentUser.id)))}))}loadRides(){return m(this,null,function*(){if(this.currentUser){console.log("Loading rides..."),this.loading=!0;try{let[t,e]=yield Promise.all([this.rideService.getAvailableRides(),this.rideService.getDriverRides(this.currentUser.id)]);this.availableRides.set(t),this.myRides.set(e),console.log("Available Rides:",this.availableRides()),console.log("My Rides:",this.myRides())}catch(t){console.error("Error loading rides:",t),this.snackBar.open(t.message||"Failed to load rides","Close",{duration:3e3})}finally{this.loading=!1}}})}acceptRide(t){return m(this,null,function*(){if(this.currentUser)try{yield this.rideService.acceptRide(t,this.currentUser.id),this.snackBar.open("Ride accepted successfully","Close",{duration:3e3})}catch(e){console.error("Error accepting ride:",e),this.snackBar.open(e.message||"Failed to accept ride","Close",{duration:3e3})}})}startRide(t){return m(this,null,function*(){try{yield this.rideService.startRide(t),this.snackBar.open("Ride started successfully","Close",{duration:3e3})}catch(e){console.error("Error starting ride:",e),this.snackBar.open(e.message||"Failed to start ride","Close",{duration:3e3})}})}completeRide(t){return m(this,null,function*(){try{yield this.rideService.completeRide(t),this.snackBar.open("Ride completed successfully","Close",{duration:3e3})}catch(e){console.error("Error completing ride:",e),this.snackBar.open(e.message||"Failed to complete ride","Close",{duration:3e3})}})}showNavigation(t){this.selectedRide=t}closeNavigation(){this.selectedRide=null}openChat(t){return m(this,null,function*(){try{let e=yield this.messageService.getOrCreateThreadForRide(t);yield this.router.navigate(["/dashboard","driver","messages",e.id])}catch(e){console.error("Error opening chat:",e),this.snackBar.open(e.message||"Failed to open chat","Close",{duration:3e3})}})}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null,this.loadRides()}onRideUpdated(t){this.loadRides()}static \u0275fac=function(e){return new(e||i)(g(Re),g(V),g(ke),g(se),g(j))};static \u0275cmp=M({type:i,selectors:[["app-ride-assignments"]],decls:26,vars:11,consts:[[1,"assignments-container"],["label","Available Rides",3,"tabIndex"],[1,"table-container"],[1,"header-with-actions"],["mat-icon-button","","color","primary","matTooltip","Refresh rides",3,"click"],["class","loading-container",4,"ngIf"],["class","no-rides",4,"ngIf"],["mat-table","","class","ride-table",3,"dataSource",4,"ngIf"],["label","My Rides",3,"tabIndex","disabled"],[3,"ride","close",4,"ngIf"],["class","ride-detail-overlay",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"no-rides"],["mat-table","",1,"ride-table",3,"dataSource"],["matColumnDef","pickup_location"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","dropoff_location"],["matColumnDef","pickup_time"],["matColumnDef","fare"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["mat-header-cell",""],["mat-cell",""],["mat-raised-button","","color","primary",3,"click"],["mat-header-row",""],["mat-row",""],["matColumnDef","status"],[1,"status-chip",3,"ngClass"],["mat-raised-button","","color","primary",3,"click",4,"ngIf"],["mat-raised-button","","color","accent",3,"click",4,"ngIf"],["mat-icon-button","","color","primary",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","View Details",3,"click"],["mat-raised-button","","color","accent",3,"click"],["mat-icon-button","","color","primary",3,"click"],[3,"close","ride"],[1,"ride-detail-overlay"],[3,"rideUpdated","rideId","onClose"]],template:function(e,l){e&1&&(n(0,"div",0)(1,"mat-tab-group")(2,"mat-tab",1)(3,"div",2)(4,"div",3)(5,"h3"),o(6,"Available Ride Requests"),r(),n(7,"button",4),_("click",function(){return l.loadRides()}),n(8,"mat-icon"),o(9,"refresh"),r()()(),d(10,ot,4,0,"div",5)(11,at,3,0,"div",6)(12,Ct,18,3,"table",7),r()(),n(13,"mat-tab",8)(14,"div",2)(15,"div",3)(16,"h3"),o(17,"My Assigned Rides"),r(),n(18,"button",4),_("click",function(){return l.loadRides()}),n(19,"mat-icon"),o(20,"refresh"),r()()(),d(21,bt,4,0,"div",5)(22,xt,3,0,"div",6)(23,Nt,21,3,"table",7),r()()(),d(24,jt,1,1,"app-ride-navigation",9)(25,$t,2,2,"div",10),r()),e&2&&(a(2),s("tabIndex",0),a(8),s("ngIf",l.loading),a(),s("ngIf",!l.loading&&l.availableRides.length===0),a(),s("ngIf",!l.loading&&l.availableRides().length>0),a(),s("tabIndex",1)("disabled",l.myRides().length===0),a(8),s("ngIf",l.loading),a(),s("ngIf",!l.loading&&l.myRides().length===0),a(),s("ngIf",!l.loading&&l.myRides().length>0),a(),s("ngIf",l.selectedRide),a(),s("ngIf",l.selectedRideId))},dependencies:[R,ae,P,le,w,G,H,z,qe,Ve,Oe,Ue,Ae,Te,Ne,Fe,Be,je,$e,De,I,k,N,D,q,$,Ge,ze,He,Ee,we,X,Ie],styles:[".assignments-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto}.table-container[_ngcontent-%COMP%]{margin:20px}.ride-table[_ngcontent-%COMP%]{width:100%}.no-rides[_ngcontent-%COMP%]{padding:20px;text-align:center;color:#666;font-style:italic}.status-chip[_ngcontent-%COMP%]{border-radius:16px;padding:4px 12px;color:#fff;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#ff9800}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3}.status-in-progress[_ngcontent-%COMP%]{background-color:#673ab7}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336}.header-with-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px;color:#666}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:10px}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}"]})};var Ze=class i{static \u0275fac=function(e){return new(e||i)};static \u0275cmp=M({type:i,selectors:[["app-driver"]],decls:13,vars:0,consts:[[1,"dashboard-container"],[1,"welcome-card"],["label","Ride Assignments"],["label","Driver Profile"]],template:function(e,l){e&1&&(n(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),o(4,"Driver Dashboard"),r()(),n(5,"mat-card-content")(6,"p"),o(7,"Welcome to your driver dashboard! Here you can manage your profile, vehicle information, and ride assignments."),r()()(),n(8,"mat-tab-group")(9,"mat-tab",2),f(10,"app-ride-assignments"),r(),n(11,"mat-tab",3),f(12,"app-driver-profile"),r()()())},dependencies:[R,w,T,A,F,O,G,H,z,W,Z],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.welcome-card[_ngcontent-%COMP%]{margin-bottom:20px}ul[_ngcontent-%COMP%]{list-style-type:none;padding:0;margin:16px 0}li[_ngcontent-%COMP%]{padding:8px 0}"]})};export{Ze as DriverComponent};
