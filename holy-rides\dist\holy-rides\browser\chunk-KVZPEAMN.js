import{a as V,c as J,d as K}from"./chunk-XN7QUBD6.js";import"./chunk-TVEQ2QHA.js";import"./chunk-RAIBPYI3.js";import"./chunk-FMN32A34.js";import"./chunk-H3RPDCYZ.js";import{c as R,d as I}from"./chunk-XGZOMXTW.js";import{C as H,F as $,H as Q,I as W,J as X,b as T,d as p,f as B,g as G,k,n as D,o as j,s as A,u as L,w as U,x as z}from"./chunk-UDNA73AU.js";import{Ec as O,Fd as q,Id as N,Kb as i,Kd as Y,La as a,Lb as S,Ld as Z,Mb as x,Md as ee,Pa as g,Pd as te,Qd as re,Wa as F,ab as s,hb as l,qb as r,rb as e,sb as u,uc as E,vc as w,yb as y,zb as b}from"./chunk-L5P5FOLI.js";import"./chunk-X5YLR3NI.js";import{i as P}from"./chunk-ODN5LVDJ.js";function ie(t,n){t&1&&(r(0,"mat-error"),i(1,"Full name is required"),e())}function oe(t,n){t&1&&(r(0,"mat-error"),i(1,"Email is required"),e())}function ae(t,n){t&1&&(r(0,"mat-error"),i(1,"Please enter a valid email"),e())}function le(t,n){t&1&&(r(0,"mat-error"),i(1,"Password is required"),e())}function me(t,n){t&1&&(r(0,"mat-error"),i(1,"Password must be at least 6 characters"),e())}function se(t,n){t&1&&(r(0,"mat-error"),i(1,"Password confirmation is required"),e())}function pe(t,n){t&1&&(r(0,"mat-error"),i(1,"Passwords do not match"),e())}function ue(t,n){t&1&&(r(0,"mat-error"),i(1,"Phone number is required"),e())}function de(t,n){if(t&1&&(r(0,"mat-option",16),i(1),e()),t&2){let m=n.$implicit;l("value",m.value),a(),x(" ",m.label," ")}}function ge(t,n){t&1&&(r(0,"mat-error"),i(1,"Role is required"),e())}function ce(t,n){if(t&1&&(r(0,"div",17),i(1),e()),t&2){let m=b();a(),S(m.error)}}var ne=class t{constructor(n,m,o){this.formBuilder=n;this.router=m;this.authService=o;this.registerForm=this.formBuilder.group({full_name:["",[p.required]],email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]],confirmPassword:["",[p.required]],phone:["",[p.required]],role:["",[p.required]]},{validators:this.passwordMatchValidator})}registerForm;error="";loading=!1;roles=[{value:"rider",label:"Rider"},{value:"driver",label:"Driver"}];passwordMatchValidator(n){return n.get("password")?.value===n.get("confirmPassword")?.value?null:{mismatch:!0}}onSubmit(){return P(this,null,function*(){if(!this.registerForm.invalid){this.loading=!0,this.error="";try{let{error:n}=yield this.authService.register(this.registerForm.value.email,this.registerForm.value.password,this.registerForm.value.role,this.registerForm.value.phone,this.registerForm.value.full_name);if(n){this.error=n.message;return}yield new Promise(o=>setTimeout(o,1e3));let{error:m}=yield this.authService.login(this.registerForm.value.email,this.registerForm.value.password);if(m){this.error=m.message;return}yield this.router.navigate(["/auth/profile"])}catch(n){this.error=n.message}finally{this.loading=!1}}})}static \u0275fac=function(m){return new(m||t)(g(A),g(R),g(X))};static \u0275cmp=F({type:t,selectors:[["app-register"]],decls:48,vars:14,consts:[[1,"register-container"],[3,"ngSubmit","formGroup"],["appearance","outline"],["matInput","","type","text","formControlName","full_name","placeholder","Enter your full name"],[4,"ngIf"],["matInput","","type","email","formControlName","email","placeholder","Enter your email"],["matInput","","type","password","formControlName","password","placeholder","Enter your password"],["matInput","","type","password","formControlName","confirmPassword","placeholder","Confirm your password"],["matInput","","type","tel","formControlName","phone","placeholder","Enter your phone number"],["formControlName","role"],[3,"value",4,"ngFor","ngForOf"],["class","error-message",4,"ngIf"],[1,"button-container"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"links"],["routerLink","/auth/login"],[3,"value"],[1,"error-message"]],template:function(m,o){if(m&1&&(r(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),i(4,"Register"),e()(),r(5,"mat-card-content")(6,"form",1),y("ngSubmit",function(){return o.onSubmit()}),r(7,"mat-form-field",2)(8,"mat-label"),i(9,"Full Name"),e(),u(10,"input",3),s(11,ie,2,0,"mat-error",4),e(),r(12,"mat-form-field",2)(13,"mat-label"),i(14,"Email"),e(),u(15,"input",5),s(16,oe,2,0,"mat-error",4)(17,ae,2,0,"mat-error",4),e(),r(18,"mat-form-field",2)(19,"mat-label"),i(20,"Password"),e(),u(21,"input",6),s(22,le,2,0,"mat-error",4)(23,me,2,0,"mat-error",4),e(),r(24,"mat-form-field",2)(25,"mat-label"),i(26,"Confirm Password"),e(),u(27,"input",7),s(28,se,2,0,"mat-error",4)(29,pe,2,0,"mat-error",4),e(),r(30,"mat-form-field",2)(31,"mat-label"),i(32,"Phone Number"),e(),u(33,"input",8),s(34,ue,2,0,"mat-error",4),e(),r(35,"mat-form-field",2)(36,"mat-label"),i(37,"Role"),e(),r(38,"mat-select",9),s(39,de,2,2,"mat-option",10),e(),s(40,ge,2,0,"mat-error",4),e(),s(41,ce,2,1,"div",11),r(42,"div",12)(43,"button",13),i(44),e()(),r(45,"div",14)(46,"a",15),i(47,"Already have an account? Login"),e()()()()()()),m&2){let d,c,f,_,h,v,C,M;a(6),l("formGroup",o.registerForm),a(5),l("ngIf",(d=o.registerForm.get("full_name"))==null||d.errors==null?null:d.errors.required),a(5),l("ngIf",(c=o.registerForm.get("email"))==null||c.errors==null?null:c.errors.required),a(),l("ngIf",(f=o.registerForm.get("email"))==null||f.errors==null?null:f.errors.email),a(5),l("ngIf",(_=o.registerForm.get("password"))==null||_.errors==null?null:_.errors.required),a(),l("ngIf",(h=o.registerForm.get("password"))==null||h.errors==null?null:h.errors.minlength),a(5),l("ngIf",(v=o.registerForm.get("confirmPassword"))==null||v.errors==null?null:v.errors.required),a(),l("ngIf",o.registerForm.errors==null?null:o.registerForm.errors.mismatch),a(5),l("ngIf",(C=o.registerForm.get("phone"))==null||C.errors==null?null:C.errors.required),a(5),l("ngForOf",o.roles),a(),l("ngIf",(M=o.registerForm.get("role"))==null||M.errors==null?null:M.errors.required),a(),l("ngIf",o.error),a(2),l("disabled",o.registerForm.invalid||o.loading),a(),x(" ",o.loading?"Registering...":"Register"," ")}},dependencies:[O,E,w,L,k,T,B,G,D,j,$,H,U,z,W,Q,N,q,re,Y,ee,te,Z,K,J,V,I],styles:[".register-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:100vh;padding:20px}.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;margin-bottom:30px}.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{width:100px;height:100px;margin-bottom:10px}.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#3f51b5;margin:0}.register-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px}.register-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.register-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{margin-top:24px;display:flex;justify-content:center}.register-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;padding:8px}.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]{margin-top:16px;text-align:center}.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.register-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#f44336;text-align:center;margin:8px 0}"]})};export{ne as RegisterComponent};
