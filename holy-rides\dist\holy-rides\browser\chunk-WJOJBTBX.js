import{a as we,b as wa,e as xe,f as ke,g as xa}from"./chunk-O2B7MD3G.js";import{a as ya,b as Ca}from"./chunk-Q5CX2EZL.js";import{a as At,b as Ot}from"./chunk-NXDGXY44.js";import{a as be}from"./chunk-RAIBPYI3.js";import{a as me,b as he}from"./chunk-6WJZ7JAD.js";import{a as Xi,b as Zi,d as Ji,e as Ke,g as ia,h as aa,i as le,j as na,k as oa,p as de,s as ra}from"./chunk-FMN32A34.js";import{a as ta,b as ce,c as ea,d as Wt}from"./chunk-H3RPDCYZ.js";import{C as ge,D as va,F as ve,H as ye,I as Ce,J as lt,a as sa,b as pe,d as ft,f as ue,g as ca,i as la,k as da,l as ma,m as ha,n as Xe,o as pa,p as ua,s as fa,u as fe,v as _a,w as _e,x as ga,y as ba}from"./chunk-UDNA73AU.js";import{$b as Ni,$c as qi,Ab as Z,Ad as Yi,B as Ei,Bb as F,Bc as Hi,Bd as ut,Ca as Zt,Cc as Vi,Db as I,Dd as J,Ea as yt,Eb as L,Ec as Ft,Ed as Ki,F as Pi,Fb as g,Fd as re,Gb as b,Ha as rt,Id as se,Jb as Qt,Kb as h,Kd as Lt,La as d,Lb as N,Ld as Bt,Ma as X,Mb as $,Md as Nt,Nb as ee,Nd as Ht,Oa as Ve,Pa as Q,Pc as qe,Pd as Vt,Qc as Rt,Qd as zt,R as Fi,Ra as st,Rc as zi,S as ht,Sb as A,Sc as Ge,T as Xt,Tc as ji,U as D,Vc as Ut,Wa as k,Wb as Ue,Wc as Qi,Xa as ct,Xb as Li,Xc as qt,Y as Ai,Ya as v,Yb as Bi,Yc as Ui,Z as bt,_ as nt,_a as R,a as xt,aa as B,ab as y,c as Si,ca as vt,da as s,dc as et,ec as It,f as j,fc as C,fd as Gi,g as K,gb as U,gc as Dt,gd as ae,hb as p,hd as ne,i as Ti,ib as Jt,jb as P,jd as oe,k as gt,ka as kt,kb as Tt,la as w,m as Ii,ma as x,mb as q,mc as Mt,md as Wi,na as Oi,nb as ze,ob as je,pa as H,pb as Qe,q as Yt,qb as c,r as Kt,ra as pt,rb as l,sa as He,sb as O,sd as We,tc as Et,uc as ie,va as S,vb as G,vc as Pt,wa as ot,wb as W,wd as $e,x as Mi,xb as te,xd as Ye,yb as T,yd as $i,z as at,za as E,zb as f,zd as Gt}from"./chunk-L5P5FOLI.js";import{a as wt,b as Ri,i as _}from"./chunk-ODN5LVDJ.js";var De=class a{constructor(n){this.authService=n;this.supabase=n.supabase,this.loadActivePricing()}supabase;pricingSubject=new K(null);pricing$=this.pricingSubject.asObservable();loadActivePricing(){return _(this,null,function*(){try{let{data:n,error:t}=yield this.supabase.from("ride_pricing").select("*").eq("is_active",!0).order("created_at",{ascending:!1}).limit(1).single();if(t)throw t;return this.pricingSubject.next(n),n}catch(n){return console.error("Error loading active pricing:",n),null}})}getAllPricing(){return _(this,null,function*(){try{let{data:n,error:t}=yield this.supabase.from("ride_pricing").select("*").order("created_at",{ascending:!1});if(t)throw t;return n||[]}catch(n){return console.error("Error loading pricing configurations:",n),[]}})}createPricing(n){return _(this,null,function*(){try{if(n.is_active){let{error:i}=yield this.supabase.from("ride_pricing").update({is_active:!1}).eq("is_active",!0);if(i)throw i}let{data:t,error:e}=yield this.supabase.from("ride_pricing").insert([n]).select().single();if(e)throw e;return n.is_active&&this.pricingSubject.next(t),t}catch(t){return console.error("Error creating pricing configuration:",t),null}})}updatePricing(n,t){return _(this,null,function*(){try{if(t.is_active){let{error:o}=yield this.supabase.from("ride_pricing").update({is_active:!1}).neq("id",n).eq("is_active",!0);if(o)throw o}let{data:e,error:i}=yield this.supabase.from("ride_pricing").update(t).eq("id",n).select().single();if(i)throw i;return e.is_active&&this.pricingSubject.next(e),e}catch(e){return console.error("Error updating pricing configuration:",e),null}})}setActiveStatus(n,t){return _(this,null,function*(){try{if(t){let{error:i}=yield this.supabase.from("ride_pricing").update({is_active:!1}).neq("id",n);if(i)throw i}let{error:e}=yield this.supabase.from("ride_pricing").update({is_active:t}).eq("id",n);if(e)throw e;return yield this.loadActivePricing(),!0}catch(e){return console.error("Error setting active status:",e),!1}})}deletePricing(n){return _(this,null,function*(){try{let{error:t}=yield this.supabase.from("ride_pricing").delete().eq("id",n);if(t)throw t;return yield this.loadActivePricing(),!0}catch(t){return console.error("Error deleting pricing configuration:",t),!1}})}static \u0275fac=function(t){return new(t||a)(vt(lt))};static \u0275prov=bt({token:a,factory:a.\u0275fac,providedIn:"root"})};var Se=class a{constructor(n,t,e){this.authService=n;this.locationService=t;this.ridePricingService=e;this.supabase=n.supabase}supabase;payoutsSubject=new K([]);payouts$=this.payoutsSubject.asObservable();calculateFare(n,t){return _(this,null,function*(){let e=yield this.ridePricingService.loadActivePricing();console.log(e);let i=e?.base_fare??5,o=e?.distance_rate??1.5,r=e?.time_rate??.25;return+(i+n*o+t*r)})}estimateFare(n,t){return _(this,null,function*(){try{let e=yield this.locationService.calculateRoute(n,t);console.log(e);let i=yield this.calculateFare(e.distance,e.duration);return console.log(i),{fare:i,routeInfo:e}}catch(e){console.error("Error estimating fare:",e);let i=10,o=20;return{fare:yield this.calculateFare(i,o),routeInfo:{distance:i,duration:o}}}})}createPaymentIntent(n,t){return _(this,null,function*(){yield new Promise(o=>setTimeout(o,800));let e=`sq_${Math.random().toString(36).substring(2,15)}`,i=`pmt_${Math.random().toString(36).substring(2,15)}`;return yield this.updateRidePaymentDetails(n,{payment_id:i,payment_status:"pending",amount:t}),{clientSecret:e,paymentId:i}})}processPayment(n,t){return _(this,null,function*(){try{yield new Promise(i=>setTimeout(i,1e3));let e=Math.random()>.1;if(e){yield this.updateRidePaymentStatus(n,"completed");let i=yield this.getRide(n);i&&i.driver_id&&i.amount&&(yield this.createDriverPayout(i.driver_id,n,i.amount,"amount"))}else yield this.updateRidePaymentStatus(n,"failed");return e}catch(e){return console.error("Error processing payment:",e),yield this.updateRidePaymentStatus(n,"failed"),!1}})}processRefund(n){return _(this,null,function*(){try{yield new Promise(e=>setTimeout(e,1e3));let t=Math.random()>.1;if(t){yield this.updateRidePaymentStatus(n,"refunded");let e=yield this.getRide(n);e&&e.driver_id&&(yield this.updateDriverPayoutStatus(e.driver_id,n,"failed"))}return t}catch(t){return console.error("Error processing refund:",t),!1}})}getRide(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("rides").select("*").eq("id",n).single();if(e)throw e;return t}catch(t){return console.error("Error fetching ride:",t),null}})}updateRidePaymentDetails(n,t){return _(this,null,function*(){try{let{error:e}=yield this.supabase.from("rides").update(Ri(wt({},t),{updated_at:new Date().toISOString()})).eq("id",n);if(e)throw e;return!0}catch(e){return console.error("Error updating ride payment details:",e),!1}})}updateRidePaymentStatus(n,t){return _(this,null,function*(){return this.updateRidePaymentDetails(n,{payment_status:t})})}createDriverPayout(n,t,e,i="amount",o){return _(this,null,function*(){try{let r={driver_id:n,ride_id:t,fare:e,status:"pending",payout_type:i};i==="percentage"&&o&&(r.percentage=o);let{error:m}=yield this.supabase.from("driver_payouts").insert([r]);if(m)throw m;return yield this.getDriverPayouts(n),!0}catch(r){return console.error("Error creating driver payout:",r),!1}})}updateDriverPayoutStatus(n,t,e){return _(this,null,function*(){try{let{error:i}=yield this.supabase.from("driver_payouts").update({status:e,updated_at:new Date().toISOString()}).eq("driver_id",n).eq("ride_id",t);if(i)throw i;return yield this.getDriverPayouts(n),!0}catch(i){return console.error("Error updating driver payout status:",i),!1}})}updateDriverPayoutAmount(n,t,e,i="amount",o){return _(this,null,function*(){try{let r={amount:e,payout_type:i,updated_at:new Date().toISOString()};i==="percentage"&&o&&(r.percentage=o);let{error:m}=yield this.supabase.from("driver_payouts").update(r).eq("driver_id",n).eq("ride_id",t);if(m)throw m;return yield this.getDriverPayouts(n),!0}catch(r){return console.error("Error updating driver payout amount:",r),!1}})}getDriverPayouts(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("driver_payouts").select("*").eq("driver_id",n).order("created_at",{ascending:!1});if(e)throw e;return this.payoutsSubject.next(t),t}catch(t){return console.error("Error fetching driver payouts:",t),[]}})}getDriverTotalEarnings(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("driver_payouts").select("amount").eq("driver_id",n).eq("status","paid");if(e)throw e;return t.reduce((i,o)=>i+o.amount,0)}catch(t){return console.error("Error calculating driver earnings:",t),0}})}getDriverPendingEarnings(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("driver_payouts").select("amount").eq("driver_id",n).eq("status","pending");if(e)throw e;return t.reduce((i,o)=>i+o.amount,0)}catch(t){return console.error("Error calculating pending earnings:",t),0}})}static \u0275fac=function(t){return new(t||a)(vt(lt),vt(ya),vt(De))};static \u0275prov=bt({token:a,factory:a.\u0275fac,providedIn:"root"})};var ii=["*"];function cn(a,n){a&1&&F(0)}var ln=["tabListContainer"],dn=["tabList"],mn=["tabListInner"],hn=["nextPaginator"],pn=["previousPaginator"],un=["content"];function fn(a,n){}var _n=["tabBodyWrapper"],gn=["tabHeader"];function bn(a,n){}function vn(a,n){if(a&1&&y(0,bn,0,0,"ng-template",12),a&2){let t=f().$implicit;p("cdkPortalOutlet",t.templateLabel)}}function yn(a,n){if(a&1&&h(0),a&2){let t=f().$implicit;N(t.textLabel)}}function Cn(a,n){if(a&1){let t=W();c(0,"div",7,2),T("click",function(){let i=w(t),o=i.$implicit,r=i.$index,m=f(),u=Qt(1);return x(m._handleClick(o,u,r))})("cdkFocusChange",function(i){let o=w(t).$index,r=f();return x(r._tabFocusChanged(i,o))}),O(2,"span",8)(3,"div",9),c(4,"span",10)(5,"span",11),y(6,vn,1,1,null,12)(7,yn,1,1),l()()()}if(a&2){let t=n.$implicit,e=n.$index,i=Qt(1),o=f();Tt(t.labelClass),P("mdc-tab--active",o.selectedIndex===e),p("id",o._getTabLabelId(t,e))("disabled",t.disabled)("fitInkBarToContent",o.fitInkBarToContent),U("tabIndex",o._getTabIndex(e))("aria-posinset",e+1)("aria-setsize",o._tabs.length)("aria-controls",o._getTabContentId(e))("aria-selected",o.selectedIndex===e)("aria-label",t.ariaLabel||null)("aria-labelledby",!t.ariaLabel&&t.ariaLabelledby?t.ariaLabelledby:null),d(3),p("matRippleTrigger",i)("matRippleDisabled",t.disabled||o.disableRipple),d(3),q(t.templateLabel?6:7)}}function wn(a,n){a&1&&F(0)}function xn(a,n){if(a&1){let t=W();c(0,"mat-tab-body",13),T("_onCentered",function(){w(t);let i=f();return x(i._removeTabBodyWrapperHeight())})("_onCentering",function(i){w(t);let o=f();return x(o._setTabBodyWrapperHeight(i))})("_beforeCentering",function(i){w(t);let o=f();return x(o._bodyCentered(i))}),l()}if(a&2){let t=n.$implicit,e=n.$index,i=f();Tt(t.bodyClass),p("id",i._getTabContentId(e))("content",t.content)("position",t.position)("animationDuration",i.animationDuration)("preserveContent",i.preserveContent),U("tabindex",i.contentTabIndex!=null&&i.selectedIndex===e?i.contentTabIndex:null)("aria-labelledby",i._getTabLabelId(t,e))("aria-hidden",i.selectedIndex!==e)}}var kn=new B("MatTabContent"),Dn=(()=>{class a{template=s(X);constructor(){}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","matTabContent",""]],features:[A([{provide:kn,useExisting:a}])]})}return a})(),Rn=new B("MatTabLabel"),Ta=new B("MAT_TAB"),Sn=(()=>{class a extends Ji{_closestTab=s(Ta,{optional:!0});static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","mat-tab-label",""],["","matTabLabel",""]],features:[A([{provide:Rn,useExisting:a}]),R]})}return a})(),Ia=new B("MAT_TAB_GROUP"),ai=(()=>{class a{_viewContainerRef=s(st);_closestTabGroup=s(Ia,{optional:!0});disabled=!1;get templateLabel(){return this._templateLabel}set templateLabel(t){this._setTemplateLabelInput(t)}_templateLabel;_explicitContent=void 0;_implicitContent;textLabel="";ariaLabel;ariaLabelledby;labelClass;bodyClass;id=null;_contentPortal=null;get content(){return this._contentPortal}_stateChanges=new j;position=null;origin=null;isActive=!1;constructor(){s(qt).load(Gt)}ngOnChanges(t){(t.hasOwnProperty("textLabel")||t.hasOwnProperty("disabled"))&&this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete()}ngOnInit(){this._contentPortal=new Zi(this._explicitContent||this._implicitContent,this._viewContainerRef)}_setTemplateLabelInput(t){t&&t._closestTab===this&&(this._templateLabel=t)}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["mat-tab"]],contentQueries:function(e,i,o){if(e&1&&(I(o,Sn,5),I(o,Dn,7,X)),e&2){let r;g(r=b())&&(i.templateLabel=r.first),g(r=b())&&(i._explicitContent=r.first)}},viewQuery:function(e,i){if(e&1&&L(X,7),e&2){let o;g(o=b())&&(i._implicitContent=o.first)}},hostAttrs:["hidden",""],hostVars:1,hostBindings:function(e,i){e&2&&U("id",null)},inputs:{disabled:[2,"disabled","disabled",C],textLabel:[0,"label","textLabel"],ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],labelClass:"labelClass",bodyClass:"bodyClass",id:"id"},exportAs:["matTab"],features:[A([{provide:Ta,useExisting:a}]),kt],ngContentSelectors:ii,decls:1,vars:0,template:function(e,i){e&1&&(Z(),y(0,cn,1,0,"ng-template"))},encapsulation:2})}return a})(),Ze="mdc-tab-indicator--active",ka="mdc-tab-indicator--no-transition",Je=class{_items;_currentItem;constructor(n){this._items=n}hide(){this._items.forEach(n=>n.deactivateInkBar()),this._currentItem=void 0}alignToElement(n){let t=this._items.find(i=>i.elementRef.nativeElement===n),e=this._currentItem;if(t!==e&&(e?.deactivateInkBar(),t)){let i=e?.elementRef.nativeElement.getBoundingClientRect?.();t.activateInkBar(i),this._currentItem=t}}},Tn=(()=>{class a{_elementRef=s(E);_inkBarElement;_inkBarContentElement;_fitToContent=!1;get fitInkBarToContent(){return this._fitToContent}set fitInkBarToContent(t){this._fitToContent!==t&&(this._fitToContent=t,this._inkBarElement&&this._appendInkBarElement())}activateInkBar(t){let e=this._elementRef.nativeElement;if(!t||!e.getBoundingClientRect||!this._inkBarContentElement){e.classList.add(Ze);return}let i=e.getBoundingClientRect(),o=t.width/i.width,r=t.left-i.left;e.classList.add(ka),this._inkBarContentElement.style.setProperty("transform",`translateX(${r}px) scaleX(${o})`),e.getBoundingClientRect(),e.classList.remove(ka),e.classList.add(Ze),this._inkBarContentElement.style.setProperty("transform","")}deactivateInkBar(){this._elementRef.nativeElement.classList.remove(Ze)}ngOnInit(){this._createInkBarElement()}ngOnDestroy(){this._inkBarElement?.remove(),this._inkBarElement=this._inkBarContentElement=null}_createInkBarElement(){let t=this._elementRef.nativeElement.ownerDocument||document,e=this._inkBarElement=t.createElement("span"),i=this._inkBarContentElement=t.createElement("span");e.className="mdc-tab-indicator",i.className="mdc-tab-indicator__content mdc-tab-indicator__content--underline",e.appendChild(this._inkBarContentElement),this._appendInkBarElement()}_appendInkBarElement(){this._inkBarElement;let t=this._fitToContent?this._elementRef.nativeElement.querySelector(".mdc-tab__content"):this._elementRef.nativeElement;t.appendChild(this._inkBarElement)}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,inputs:{fitInkBarToContent:[2,"fitInkBarToContent","fitInkBarToContent",C]}})}return a})();var Ma=(()=>{class a extends Tn{elementRef=s(E);disabled=!1;focus(){this.elementRef.nativeElement.focus()}getOffsetLeft(){return this.elementRef.nativeElement.offsetLeft}getOffsetWidth(){return this.elementRef.nativeElement.offsetWidth}static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","matTabLabelWrapper",""]],hostVars:3,hostBindings:function(e,i){e&2&&(U("aria-disabled",!!i.disabled),P("mat-mdc-tab-disabled",i.disabled))},inputs:{disabled:[2,"disabled","disabled",C]},features:[R]})}return a})(),Da={passive:!0},In=650,Mn=100,En=(()=>{class a{_elementRef=s(E);_changeDetectorRef=s(et);_viewportRuler=s(le);_dir=s(ut,{optional:!0});_ngZone=s(ot);_platform=s(Rt);_sharedResizeObserver=s(_a);_injector=s(pt);_renderer=s(Ve);_animationMode=s(yt,{optional:!0});_eventCleanups;_scrollDistance=0;_selectedIndexChanged=!1;_destroyed=new j;_showPaginationControls=!1;_disableScrollAfter=!0;_disableScrollBefore=!0;_tabLabelCount;_scrollDistanceChanged;_keyManager;_currentTextContent;_stopScrolling=new j;disablePagination=!1;get selectedIndex(){return this._selectedIndex}set selectedIndex(t){let e=isNaN(t)?0:t;this._selectedIndex!=e&&(this._selectedIndexChanged=!0,this._selectedIndex=e,this._keyManager&&this._keyManager.updateActiveItem(e))}_selectedIndex=0;selectFocusedIndex=new S;indexFocused=new S;constructor(){this._eventCleanups=this._ngZone.runOutsideAngular(()=>[this._renderer.listen(this._elementRef.nativeElement,"mouseleave",()=>this._stopInterval())])}ngAfterViewInit(){this._eventCleanups.push(qe(this._renderer,this._previousPaginator.nativeElement,"touchstart",()=>this._handlePaginatorPress("before"),Da),qe(this._renderer,this._nextPaginator.nativeElement,"touchstart",()=>this._handlePaginatorPress("after"),Da))}ngAfterContentInit(){let t=this._dir?this._dir.change:gt("ltr"),e=this._sharedResizeObserver.observe(this._elementRef.nativeElement).pipe(Pi(32),D(this._destroyed)),i=this._viewportRuler.change(150).pipe(D(this._destroyed)),o=()=>{this.updatePagination(),this._alignInkBarToSelectedTab()};this._keyManager=new oe(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap().skipPredicate(()=>!1),this._keyManager.updateActiveItem(this._selectedIndex),rt(o,{injector:this._injector}),at(t,i,e,this._items.changes,this._itemsResized()).pipe(D(this._destroyed)).subscribe(()=>{this._ngZone.run(()=>{Promise.resolve().then(()=>{this._scrollDistance=Math.max(0,Math.min(this._getMaxScrollDistance(),this._scrollDistance)),o()})}),this._keyManager.withHorizontalOrientation(this._getLayoutDirection())}),this._keyManager.change.subscribe(r=>{this.indexFocused.emit(r),this._setTabFocus(r)})}_itemsResized(){return typeof ResizeObserver!="function"?Ti:this._items.changes.pipe(ht(this._items),Xt(t=>new Si(e=>this._ngZone.runOutsideAngular(()=>{let i=new ResizeObserver(o=>e.next(o));return t.forEach(o=>i.observe(o.elementRef.nativeElement)),()=>{i.disconnect()}}))),Fi(1),Ei(t=>t.some(e=>e.contentRect.width>0&&e.contentRect.height>0)))}ngAfterContentChecked(){this._tabLabelCount!=this._items.length&&(this.updatePagination(),this._tabLabelCount=this._items.length,this._changeDetectorRef.markForCheck()),this._selectedIndexChanged&&(this._scrollToLabel(this._selectedIndex),this._checkScrollingControls(),this._alignInkBarToSelectedTab(),this._selectedIndexChanged=!1,this._changeDetectorRef.markForCheck()),this._scrollDistanceChanged&&(this._updateTabScrollPosition(),this._scrollDistanceChanged=!1,this._changeDetectorRef.markForCheck())}ngOnDestroy(){this._eventCleanups.forEach(t=>t()),this._keyManager?.destroy(),this._destroyed.next(),this._destroyed.complete(),this._stopScrolling.complete()}_handleKeydown(t){if(!ne(t))switch(t.keyCode){case 13:case 32:if(this.focusIndex!==this.selectedIndex){let e=this._items.get(this.focusIndex);e&&!e.disabled&&(this.selectFocusedIndex.emit(this.focusIndex),this._itemSelected(t))}break;default:this._keyManager.onKeydown(t)}}_onContentChanges(){let t=this._elementRef.nativeElement.textContent;t!==this._currentTextContent&&(this._currentTextContent=t||"",this._ngZone.run(()=>{this.updatePagination(),this._alignInkBarToSelectedTab(),this._changeDetectorRef.markForCheck()}))}updatePagination(){this._checkPaginationEnabled(),this._checkScrollingControls(),this._updateTabScrollPosition()}get focusIndex(){return this._keyManager?this._keyManager.activeItemIndex:0}set focusIndex(t){!this._isValidIndex(t)||this.focusIndex===t||!this._keyManager||this._keyManager.setActiveItem(t)}_isValidIndex(t){return this._items?!!this._items.toArray()[t]:!0}_setTabFocus(t){if(this._showPaginationControls&&this._scrollToLabel(t),this._items&&this._items.length){this._items.toArray()[t].focus();let e=this._tabListContainer.nativeElement;this._getLayoutDirection()=="ltr"?e.scrollLeft=0:e.scrollLeft=e.scrollWidth-e.offsetWidth}}_getLayoutDirection(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}_updateTabScrollPosition(){if(this.disablePagination)return;let t=this.scrollDistance,e=this._getLayoutDirection()==="ltr"?-t:t;this._tabList.nativeElement.style.transform=`translateX(${Math.round(e)}px)`,(this._platform.TRIDENT||this._platform.EDGE)&&(this._tabListContainer.nativeElement.scrollLeft=0)}get scrollDistance(){return this._scrollDistance}set scrollDistance(t){this._scrollTo(t)}_scrollHeader(t){let e=this._tabListContainer.nativeElement.offsetWidth,i=(t=="before"?-1:1)*e/3;return this._scrollTo(this._scrollDistance+i)}_handlePaginatorClick(t){this._stopInterval(),this._scrollHeader(t)}_scrollToLabel(t){if(this.disablePagination)return;let e=this._items?this._items.toArray()[t]:null;if(!e)return;let i=this._tabListContainer.nativeElement.offsetWidth,{offsetLeft:o,offsetWidth:r}=e.elementRef.nativeElement,m,u;this._getLayoutDirection()=="ltr"?(m=o,u=m+r):(u=this._tabListInner.nativeElement.offsetWidth-o,m=u-r);let M=this.scrollDistance,V=this.scrollDistance+i;m<M?this.scrollDistance-=M-m:u>V&&(this.scrollDistance+=Math.min(u-V,m-M))}_checkPaginationEnabled(){if(this.disablePagination)this._showPaginationControls=!1;else{let t=this._tabListInner.nativeElement.scrollWidth,e=this._elementRef.nativeElement.offsetWidth,i=t-e>=5;i||(this.scrollDistance=0),i!==this._showPaginationControls&&(this._showPaginationControls=i,this._changeDetectorRef.markForCheck())}}_checkScrollingControls(){this.disablePagination?this._disableScrollAfter=this._disableScrollBefore=!0:(this._disableScrollBefore=this.scrollDistance==0,this._disableScrollAfter=this.scrollDistance==this._getMaxScrollDistance(),this._changeDetectorRef.markForCheck())}_getMaxScrollDistance(){let t=this._tabListInner.nativeElement.scrollWidth,e=this._tabListContainer.nativeElement.offsetWidth;return t-e||0}_alignInkBarToSelectedTab(){let t=this._items&&this._items.length?this._items.toArray()[this.selectedIndex]:null,e=t?t.elementRef.nativeElement:null;e?this._inkBar.alignToElement(e):this._inkBar.hide()}_stopInterval(){this._stopScrolling.next()}_handlePaginatorPress(t,e){e&&e.button!=null&&e.button!==0||(this._stopInterval(),Mi(In,Mn).pipe(D(at(this._stopScrolling,this._destroyed))).subscribe(()=>{let{maxScrollDistance:i,distance:o}=this._scrollHeader(t);(o===0||o>=i)&&this._stopInterval()}))}_scrollTo(t){if(this.disablePagination)return{maxScrollDistance:0,distance:0};let e=this._getMaxScrollDistance();return this._scrollDistance=Math.max(0,Math.min(e,t)),this._scrollDistanceChanged=!0,this._checkScrollingControls(),{maxScrollDistance:e,distance:this._scrollDistance}}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,inputs:{disablePagination:[2,"disablePagination","disablePagination",C],selectedIndex:[2,"selectedIndex","selectedIndex",Dt]},outputs:{selectFocusedIndex:"selectFocusedIndex",indexFocused:"indexFocused"}})}return a})(),Pn=(()=>{class a extends En{_items;_tabListContainer;_tabList;_tabListInner;_nextPaginator;_previousPaginator;_inkBar;ariaLabel;ariaLabelledby;disableRipple=!1;ngAfterContentInit(){this._inkBar=new Je(this._items),super.ngAfterContentInit()}_itemSelected(t){t.preventDefault()}static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275cmp=k({type:a,selectors:[["mat-tab-header"]],contentQueries:function(e,i,o){if(e&1&&I(o,Ma,4),e&2){let r;g(r=b())&&(i._items=r)}},viewQuery:function(e,i){if(e&1&&(L(ln,7),L(dn,7),L(mn,7),L(hn,5),L(pn,5)),e&2){let o;g(o=b())&&(i._tabListContainer=o.first),g(o=b())&&(i._tabList=o.first),g(o=b())&&(i._tabListInner=o.first),g(o=b())&&(i._nextPaginator=o.first),g(o=b())&&(i._previousPaginator=o.first)}},hostAttrs:[1,"mat-mdc-tab-header"],hostVars:4,hostBindings:function(e,i){e&2&&P("mat-mdc-tab-header-pagination-controls-enabled",i._showPaginationControls)("mat-mdc-tab-header-rtl",i._getLayoutDirection()=="rtl")},inputs:{ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],disableRipple:[2,"disableRipple","disableRipple",C]},features:[R],ngContentSelectors:ii,decls:13,vars:10,consts:[["previousPaginator",""],["tabListContainer",""],["tabList",""],["tabListInner",""],["nextPaginator",""],["mat-ripple","",1,"mat-mdc-tab-header-pagination","mat-mdc-tab-header-pagination-before",3,"click","mousedown","touchend","matRippleDisabled"],[1,"mat-mdc-tab-header-pagination-chevron"],[1,"mat-mdc-tab-label-container",3,"keydown"],["role","tablist",1,"mat-mdc-tab-list",3,"cdkObserveContent"],[1,"mat-mdc-tab-labels"],["mat-ripple","",1,"mat-mdc-tab-header-pagination","mat-mdc-tab-header-pagination-after",3,"mousedown","click","touchend","matRippleDisabled"]],template:function(e,i){if(e&1){let o=W();Z(),c(0,"div",5,0),T("click",function(){return w(o),x(i._handlePaginatorClick("before"))})("mousedown",function(m){return w(o),x(i._handlePaginatorPress("before",m))})("touchend",function(){return w(o),x(i._stopInterval())}),O(2,"div",6),l(),c(3,"div",7,1),T("keydown",function(m){return w(o),x(i._handleKeydown(m))}),c(5,"div",8,2),T("cdkObserveContent",function(){return w(o),x(i._onContentChanges())}),c(7,"div",9,3),F(9),l()()(),c(10,"div",10,4),T("mousedown",function(m){return w(o),x(i._handlePaginatorPress("after",m))})("click",function(){return w(o),x(i._handlePaginatorClick("after"))})("touchend",function(){return w(o),x(i._stopInterval())}),O(12,"div",6),l()}e&2&&(P("mat-mdc-tab-header-pagination-disabled",i._disableScrollBefore),p("matRippleDisabled",i._disableScrollBefore||i.disableRipple),d(3),P("_mat-animation-noopable",i._animationMode==="NoopAnimations"),d(2),U("aria-label",i.ariaLabel||null)("aria-labelledby",i.ariaLabelledby||null),d(5),P("mat-mdc-tab-header-pagination-disabled",i._disableScrollAfter),p("matRippleDisabled",i._disableScrollAfter||i.disableRipple))},dependencies:[Ye,qi],styles:[`.mat-mdc-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mdc-tab-indicator .mdc-tab-indicator__content{transition-duration:var(--mat-tab-animation-duration, 250ms)}.mat-mdc-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:rgba(0,0,0,0);touch-action:none;box-sizing:content-box;outline:0}.mat-mdc-tab-header-pagination::-moz-focus-inner{border:0}.mat-mdc-tab-header-pagination .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-controls-enabled .mat-mdc-tab-header-pagination{display:flex}.mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after{padding-left:4px}.mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before,.mat-mdc-tab-header-pagination-after{padding-right:4px}.mat-mdc-tab-header-rtl .mat-mdc-tab-header-pagination-before .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-header-pagination-after .mat-mdc-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-mdc-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px;border-color:var(--mat-tab-header-pagination-icon-color, var(--mat-sys-on-surface))}.mat-mdc-tab-header-pagination-disabled{box-shadow:none;cursor:default;pointer-events:none}.mat-mdc-tab-header-pagination-disabled .mat-mdc-tab-header-pagination-chevron{opacity:.4}.mat-mdc-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable .mat-mdc-tab-list{transition:none}.mat-mdc-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1;border-bottom-style:solid;border-bottom-width:var(--mat-tab-header-divider-height, 1px);border-bottom-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-group-inverted-header .mat-mdc-tab-label-container{border-bottom:none;border-top-style:solid;border-top-width:var(--mat-tab-header-divider-height, 1px);border-top-color:var(--mat-tab-header-divider-color, var(--mat-sys-surface-variant))}.mat-mdc-tab-labels{display:flex;flex:1 0 auto}[mat-align-tabs=center]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-mdc-tab-header .mat-mdc-tab-labels{justify-content:flex-end}.cdk-drop-list .mat-mdc-tab-labels,.mat-mdc-tab-labels.cdk-drop-list{min-height:var(--mdc-secondary-navigation-tab-container-height, 48px)}.mat-mdc-tab::before{margin:5px}@media(forced-colors: active){.mat-mdc-tab[aria-disabled=true]{color:GrayText}}
`],encapsulation:2})}return a})(),Fn=new B("MAT_TABS_CONFIG"),Ra=(()=>{class a extends Ke{_host=s(ti);_centeringSub=xt.EMPTY;_leavingSub=xt.EMPTY;constructor(){super()}ngOnInit(){super.ngOnInit(),this._centeringSub=this._host._beforeCentering.pipe(ht(this._host._isCenterPosition())).subscribe(t=>{this._host._content&&t&&!this.hasAttached()&&this.attach(this._host._content)}),this._leavingSub=this._host._afterLeavingCenter.subscribe(()=>{this._host.preserveContent||this.detach()})}ngOnDestroy(){super.ngOnDestroy(),this._centeringSub.unsubscribe(),this._leavingSub.unsubscribe()}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","matTabBodyHost",""]],features:[R]})}return a})(),ti=(()=>{class a{_elementRef=s(E);_dir=s(ut,{optional:!0});_ngZone=s(ot);_injector=s(pt);_renderer=s(Ve);_animationsModule=s(yt,{optional:!0});_eventCleanups;_initialized;_fallbackTimer;_positionIndex;_dirChangeSubscription=xt.EMPTY;_position;_previousPosition;_onCentering=new S;_beforeCentering=new S;_afterLeavingCenter=new S;_onCentered=new S(!0);_portalHost;_contentElement;_content;animationDuration="500ms";preserveContent=!1;set position(t){this._positionIndex=t,this._computePositionAnimationState()}constructor(){if(this._dir){let t=s(et);this._dirChangeSubscription=this._dir.change.subscribe(e=>{this._computePositionAnimationState(e),t.markForCheck()})}}ngOnInit(){this._bindTransitionEvents(),this._position==="center"&&(this._setActiveClass(!0),rt(()=>this._onCentering.emit(this._elementRef.nativeElement.clientHeight),{injector:this._injector})),this._initialized=!0}ngOnDestroy(){clearTimeout(this._fallbackTimer),this._eventCleanups?.forEach(t=>t()),this._dirChangeSubscription.unsubscribe()}_bindTransitionEvents(){this._ngZone.runOutsideAngular(()=>{let t=this._elementRef.nativeElement,e=i=>{i.target===this._contentElement?.nativeElement&&(this._elementRef.nativeElement.classList.remove("mat-tab-body-animating"),i.type==="transitionend"&&this._transitionDone())};this._eventCleanups=[this._renderer.listen(t,"transitionstart",i=>{i.target===this._contentElement?.nativeElement&&(this._elementRef.nativeElement.classList.add("mat-tab-body-animating"),this._transitionStarted())}),this._renderer.listen(t,"transitionend",e),this._renderer.listen(t,"transitioncancel",e)]})}_transitionStarted(){clearTimeout(this._fallbackTimer);let t=this._position==="center";this._beforeCentering.emit(t),t&&this._onCentering.emit(this._elementRef.nativeElement.clientHeight)}_transitionDone(){this._position==="center"?this._onCentered.emit():this._previousPosition==="center"&&this._afterLeavingCenter.emit()}_setActiveClass(t){this._elementRef.nativeElement.classList.toggle("mat-mdc-tab-body-active",t)}_getLayoutDirection(){return this._dir&&this._dir.value==="rtl"?"rtl":"ltr"}_isCenterPosition(){return this._positionIndex===0}_computePositionAnimationState(t=this._getLayoutDirection()){this._previousPosition=this._position,this._positionIndex<0?this._position=t=="ltr"?"left":"right":this._positionIndex>0?this._position=t=="ltr"?"right":"left":this._position="center",this._animationsDisabled()?this._simulateTransitionEvents():this._initialized&&(this._position==="center"||this._previousPosition==="center")&&(clearTimeout(this._fallbackTimer),this._fallbackTimer=this._ngZone.runOutsideAngular(()=>setTimeout(()=>this._simulateTransitionEvents(),100)))}_simulateTransitionEvents(){this._transitionStarted(),rt(()=>this._transitionDone(),{injector:this._injector})}_animationsDisabled(){return this._animationsModule==="NoopAnimations"||this.animationDuration==="0ms"||this.animationDuration==="0s"}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["mat-tab-body"]],viewQuery:function(e,i){if(e&1&&(L(Ra,5),L(un,5)),e&2){let o;g(o=b())&&(i._portalHost=o.first),g(o=b())&&(i._contentElement=o.first)}},hostAttrs:[1,"mat-mdc-tab-body"],hostVars:1,hostBindings:function(e,i){e&2&&U("inert",i._position==="center"?null:"")},inputs:{_content:[0,"content","_content"],animationDuration:"animationDuration",preserveContent:"preserveContent",position:"position"},outputs:{_onCentering:"_onCentering",_beforeCentering:"_beforeCentering",_onCentered:"_onCentered"},decls:3,vars:6,consts:[["content",""],["cdkScrollable","",1,"mat-mdc-tab-body-content"],["matTabBodyHost",""]],template:function(e,i){e&1&&(c(0,"div",1,0),y(2,fn,0,0,"ng-template",2),l()),e&2&&P("mat-tab-body-content-left",i._position==="left")("mat-tab-body-content-right",i._position==="right")("mat-tab-body-content-can-animate",i._position==="center"||i._previousPosition==="center")},dependencies:[Ra,aa],styles:[`.mat-mdc-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-mdc-tab-body.mat-mdc-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-mdc-tab-group.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body.mat-mdc-tab-body-active{overflow-y:hidden}.mat-mdc-tab-body-content{height:100%;overflow:auto;transform:none;visibility:hidden}.mat-tab-body-animating>.mat-mdc-tab-body-content,.mat-mdc-tab-body-active>.mat-mdc-tab-body-content{visibility:visible}.mat-mdc-tab-group-dynamic-height .mat-mdc-tab-body-content{overflow:hidden}.mat-tab-body-content-can-animate{transition:transform var(--mat-tab-animation-duration) 1ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable .mat-tab-body-content-can-animate{transition:none}.mat-tab-body-content-left{transform:translate3d(-100%, 0, 0)}.mat-tab-body-content-right{transform:translate3d(100%, 0, 0)}
`],encapsulation:2})}return a})(),Ea=(()=>{class a{_elementRef=s(E);_changeDetectorRef=s(et);_ngZone=s(ot);_tabsSubscription=xt.EMPTY;_tabLabelSubscription=xt.EMPTY;_tabBodySubscription=xt.EMPTY;_diAnimationsDisabled=s(yt,{optional:!0})==="NoopAnimations";_allTabs;_tabBodies;_tabBodyWrapper;_tabHeader;_tabs=new Zt;_indexToSelect=0;_lastFocusedTabIndex=null;_tabBodyWrapperHeight=0;color;get fitInkBarToContent(){return this._fitInkBarToContent}set fitInkBarToContent(t){this._fitInkBarToContent=t,this._changeDetectorRef.markForCheck()}_fitInkBarToContent=!1;stretchTabs=!0;alignTabs=null;dynamicHeight=!1;get selectedIndex(){return this._selectedIndex}set selectedIndex(t){this._indexToSelect=isNaN(t)?null:t}_selectedIndex=null;headerPosition="above";get animationDuration(){return this._animationDuration}set animationDuration(t){let e=t+"";this._animationDuration=/^\d+$/.test(e)?t+"ms":e}_animationDuration;get contentTabIndex(){return this._contentTabIndex}set contentTabIndex(t){this._contentTabIndex=isNaN(t)?null:t}_contentTabIndex;disablePagination=!1;disableRipple=!1;preserveContent=!1;get backgroundColor(){return this._backgroundColor}set backgroundColor(t){let e=this._elementRef.nativeElement.classList;e.remove("mat-tabs-with-background",`mat-background-${this.backgroundColor}`),t&&e.add("mat-tabs-with-background",`mat-background-${t}`),this._backgroundColor=t}_backgroundColor;ariaLabel;ariaLabelledby;selectedIndexChange=new S;focusChange=new S;animationDone=new S;selectedTabChange=new S(!0);_groupId;_isServer=!s(Rt).isBrowser;constructor(){let t=s(Fn,{optional:!0});this._groupId=s(ae).getId("mat-tab-group-"),this.animationDuration=t&&t.animationDuration?t.animationDuration:"500ms",this.disablePagination=t&&t.disablePagination!=null?t.disablePagination:!1,this.dynamicHeight=t&&t.dynamicHeight!=null?t.dynamicHeight:!1,t?.contentTabIndex!=null&&(this.contentTabIndex=t.contentTabIndex),this.preserveContent=!!t?.preserveContent,this.fitInkBarToContent=t&&t.fitInkBarToContent!=null?t.fitInkBarToContent:!1,this.stretchTabs=t&&t.stretchTabs!=null?t.stretchTabs:!0,this.alignTabs=t&&t.alignTabs!=null?t.alignTabs:null}ngAfterContentChecked(){let t=this._indexToSelect=this._clampTabIndex(this._indexToSelect);if(this._selectedIndex!=t){let e=this._selectedIndex==null;if(!e){this.selectedTabChange.emit(this._createChangeEvent(t));let i=this._tabBodyWrapper.nativeElement;i.style.minHeight=i.clientHeight+"px"}Promise.resolve().then(()=>{this._tabs.forEach((i,o)=>i.isActive=o===t),e||(this.selectedIndexChange.emit(t),this._tabBodyWrapper.nativeElement.style.minHeight="")})}this._tabs.forEach((e,i)=>{e.position=i-t,this._selectedIndex!=null&&e.position==0&&!e.origin&&(e.origin=t-this._selectedIndex)}),this._selectedIndex!==t&&(this._selectedIndex=t,this._lastFocusedTabIndex=null,this._changeDetectorRef.markForCheck())}ngAfterContentInit(){this._subscribeToAllTabChanges(),this._subscribeToTabLabels(),this._tabsSubscription=this._tabs.changes.subscribe(()=>{let t=this._clampTabIndex(this._indexToSelect);if(t===this._selectedIndex){let e=this._tabs.toArray(),i;for(let o=0;o<e.length;o++)if(e[o].isActive){this._indexToSelect=this._selectedIndex=o,this._lastFocusedTabIndex=null,i=e[o];break}!i&&e[t]&&Promise.resolve().then(()=>{e[t].isActive=!0,this.selectedTabChange.emit(this._createChangeEvent(t))})}this._changeDetectorRef.markForCheck()})}ngAfterViewInit(){this._tabBodySubscription=this._tabBodies.changes.subscribe(()=>this._bodyCentered(!0))}_subscribeToAllTabChanges(){this._allTabs.changes.pipe(ht(this._allTabs)).subscribe(t=>{this._tabs.reset(t.filter(e=>e._closestTabGroup===this||!e._closestTabGroup)),this._tabs.notifyOnChanges()})}ngOnDestroy(){this._tabs.destroy(),this._tabsSubscription.unsubscribe(),this._tabLabelSubscription.unsubscribe(),this._tabBodySubscription.unsubscribe()}realignInkBar(){this._tabHeader&&this._tabHeader._alignInkBarToSelectedTab()}updatePagination(){this._tabHeader&&this._tabHeader.updatePagination()}focusTab(t){let e=this._tabHeader;e&&(e.focusIndex=t)}_focusChanged(t){this._lastFocusedTabIndex=t,this.focusChange.emit(this._createChangeEvent(t))}_createChangeEvent(t){let e=new ei;return e.index=t,this._tabs&&this._tabs.length&&(e.tab=this._tabs.toArray()[t]),e}_subscribeToTabLabels(){this._tabLabelSubscription&&this._tabLabelSubscription.unsubscribe(),this._tabLabelSubscription=at(...this._tabs.map(t=>t._stateChanges)).subscribe(()=>this._changeDetectorRef.markForCheck())}_clampTabIndex(t){return Math.min(this._tabs.length-1,Math.max(t||0,0))}_getTabLabelId(t,e){return t.id||`${this._groupId}-label-${e}`}_getTabContentId(t){return`${this._groupId}-content-${t}`}_setTabBodyWrapperHeight(t){if(!this.dynamicHeight||!this._tabBodyWrapperHeight){this._tabBodyWrapperHeight=t;return}let e=this._tabBodyWrapper.nativeElement;e.style.height=this._tabBodyWrapperHeight+"px",this._tabBodyWrapper.nativeElement.offsetHeight&&(e.style.height=t+"px")}_removeTabBodyWrapperHeight(){let t=this._tabBodyWrapper.nativeElement;this._tabBodyWrapperHeight=t.clientHeight,t.style.height="",this._ngZone.run(()=>this.animationDone.emit())}_handleClick(t,e,i){e.focusIndex=i,t.disabled||(this.selectedIndex=i)}_getTabIndex(t){let e=this._lastFocusedTabIndex??this.selectedIndex;return t===e?0:-1}_tabFocusChanged(t,e){t&&t!=="mouse"&&t!=="touch"&&(this._tabHeader.focusIndex=e)}_bodyCentered(t){t&&this._tabBodies?.forEach((e,i)=>e._setActiveClass(i===this._selectedIndex))}_animationsDisabled(){return this._diAnimationsDisabled||this.animationDuration==="0"||this.animationDuration==="0ms"}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["mat-tab-group"]],contentQueries:function(e,i,o){if(e&1&&I(o,ai,5),e&2){let r;g(r=b())&&(i._allTabs=r)}},viewQuery:function(e,i){if(e&1&&(L(_n,5),L(gn,5),L(ti,5)),e&2){let o;g(o=b())&&(i._tabBodyWrapper=o.first),g(o=b())&&(i._tabHeader=o.first),g(o=b())&&(i._tabBodies=o)}},hostAttrs:[1,"mat-mdc-tab-group"],hostVars:11,hostBindings:function(e,i){e&2&&(U("mat-align-tabs",i.alignTabs),Tt("mat-"+(i.color||"primary")),Jt("--mat-tab-animation-duration",i.animationDuration),P("mat-mdc-tab-group-dynamic-height",i.dynamicHeight)("mat-mdc-tab-group-inverted-header",i.headerPosition==="below")("mat-mdc-tab-group-stretch-tabs",i.stretchTabs))},inputs:{color:"color",fitInkBarToContent:[2,"fitInkBarToContent","fitInkBarToContent",C],stretchTabs:[2,"mat-stretch-tabs","stretchTabs",C],alignTabs:[0,"mat-align-tabs","alignTabs"],dynamicHeight:[2,"dynamicHeight","dynamicHeight",C],selectedIndex:[2,"selectedIndex","selectedIndex",Dt],headerPosition:"headerPosition",animationDuration:"animationDuration",contentTabIndex:[2,"contentTabIndex","contentTabIndex",Dt],disablePagination:[2,"disablePagination","disablePagination",C],disableRipple:[2,"disableRipple","disableRipple",C],preserveContent:[2,"preserveContent","preserveContent",C],backgroundColor:"backgroundColor",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"]},outputs:{selectedIndexChange:"selectedIndexChange",focusChange:"focusChange",animationDone:"animationDone",selectedTabChange:"selectedTabChange"},exportAs:["matTabGroup"],features:[A([{provide:Ia,useExisting:a}])],ngContentSelectors:ii,decls:9,vars:8,consts:[["tabHeader",""],["tabBodyWrapper",""],["tabNode",""],[3,"indexFocused","selectFocusedIndex","selectedIndex","disableRipple","disablePagination","aria-label","aria-labelledby"],["role","tab","matTabLabelWrapper","","cdkMonitorElementFocus","",1,"mdc-tab","mat-mdc-tab","mat-focus-indicator",3,"id","mdc-tab--active","class","disabled","fitInkBarToContent"],[1,"mat-mdc-tab-body-wrapper"],["role","tabpanel",3,"id","class","content","position","animationDuration","preserveContent"],["role","tab","matTabLabelWrapper","","cdkMonitorElementFocus","",1,"mdc-tab","mat-mdc-tab","mat-focus-indicator",3,"click","cdkFocusChange","id","disabled","fitInkBarToContent"],[1,"mdc-tab__ripple"],["mat-ripple","",1,"mat-mdc-tab-ripple",3,"matRippleTrigger","matRippleDisabled"],[1,"mdc-tab__content"],[1,"mdc-tab__text-label"],[3,"cdkPortalOutlet"],["role","tabpanel",3,"_onCentered","_onCentering","_beforeCentering","id","content","position","animationDuration","preserveContent"]],template:function(e,i){if(e&1){let o=W();Z(),c(0,"mat-tab-header",3,0),T("indexFocused",function(m){return w(o),x(i._focusChanged(m))})("selectFocusedIndex",function(m){return w(o),x(i.selectedIndex=m)}),je(2,Cn,8,17,"div",4,ze),l(),y(4,wn,1,0),c(5,"div",5,1),je(7,xn,1,10,"mat-tab-body",6,ze),l()}e&2&&(p("selectedIndex",i.selectedIndex||0)("disableRipple",i.disableRipple)("disablePagination",i.disablePagination)("aria-label",i.ariaLabel)("aria-labelledby",i.ariaLabelledby),d(2),Qe(i._tabs),d(2),q(i._isServer?4:-1),d(),P("_mat-animation-noopable",i._animationsDisabled()),d(2),Qe(i._tabs))},dependencies:[Pn,Ma,Qi,Ye,Ke,ti],styles:[`.mdc-tab{min-width:90px;padding:0 24px;display:flex;flex:1 0 auto;justify-content:center;box-sizing:border-box;border:none;outline:none;text-align:center;white-space:nowrap;cursor:pointer;z-index:1}.mdc-tab__content{display:flex;align-items:center;justify-content:center;height:inherit;pointer-events:none}.mdc-tab__text-label{transition:150ms color linear;display:inline-block;line-height:1;z-index:2}.mdc-tab--active .mdc-tab__text-label{transition-delay:100ms}._mat-animation-noopable .mdc-tab__text-label{transition:none}.mdc-tab-indicator{display:flex;position:absolute;top:0;left:0;justify-content:center;width:100%;height:100%;pointer-events:none;z-index:1}.mdc-tab-indicator__content{transition:var(--mat-tab-animation-duration, 250ms) transform cubic-bezier(0.4, 0, 0.2, 1);transform-origin:left;opacity:0}.mdc-tab-indicator__content--underline{align-self:flex-end;box-sizing:border-box;width:100%;border-top-style:solid}.mdc-tab-indicator--active .mdc-tab-indicator__content{opacity:1}._mat-animation-noopable .mdc-tab-indicator__content,.mdc-tab-indicator--no-transition .mdc-tab-indicator__content{transition:none}.mat-mdc-tab-ripple.mat-mdc-tab-ripple{position:absolute;top:0;left:0;bottom:0;right:0;pointer-events:none}.mat-mdc-tab{-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:none;background:none;height:var(--mdc-secondary-navigation-tab-container-height, 48px);font-family:var(--mat-tab-header-label-text-font, var(--mat-sys-title-small-font));font-size:var(--mat-tab-header-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-tab-header-label-text-tracking, var(--mat-sys-title-small-tracking));line-height:var(--mat-tab-header-label-text-line-height, var(--mat-sys-title-small-line-height));font-weight:var(--mat-tab-header-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-tab.mdc-tab{flex-grow:0}.mat-mdc-tab .mdc-tab-indicator__content--underline{border-color:var(--mdc-tab-indicator-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mdc-tab-indicator-active-indicator-height, 2px);border-radius:var(--mdc-tab-indicator-active-indicator-shape, 0)}.mat-mdc-tab:hover .mdc-tab__text-label{color:var(--mat-tab-header-inactive-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab:focus .mdc-tab__text-label{color:var(--mat-tab-header-inactive-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__text-label{color:var(--mat-tab-header-active-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active .mdc-tab__ripple::before,.mat-mdc-tab.mdc-tab--active .mat-ripple-element{background-color:var(--mat-tab-header-active-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab__text-label{color:var(--mat-tab-header-active-hover-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:hover .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-hover-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab__text-label{color:var(--mat-tab-header-active-focus-label-text-color, var(--mat-sys-on-surface))}.mat-mdc-tab.mdc-tab--active:focus .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-active-focus-indicator-color, var(--mat-sys-primary))}.mat-mdc-tab.mat-mdc-tab-disabled{opacity:.4;pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__content{pointer-events:none}.mat-mdc-tab.mat-mdc-tab-disabled .mdc-tab__ripple::before,.mat-mdc-tab.mat-mdc-tab-disabled .mat-ripple-element{background-color:var(--mat-tab-header-disabled-ripple-color)}.mat-mdc-tab .mdc-tab__ripple::before{content:"";display:block;position:absolute;top:0;left:0;right:0;bottom:0;opacity:0;pointer-events:none;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-inactive-label-text-color, var(--mat-sys-on-surface));display:inline-flex;align-items:center}.mat-mdc-tab .mdc-tab__content{position:relative;pointer-events:auto}.mat-mdc-tab:hover .mdc-tab__ripple::before{opacity:.04}.mat-mdc-tab.cdk-program-focused .mdc-tab__ripple::before,.mat-mdc-tab.cdk-keyboard-focused .mdc-tab__ripple::before{opacity:.12}.mat-mdc-tab .mat-ripple-element{opacity:.12;background-color:var(--mat-tab-header-inactive-ripple-color, var(--mat-sys-on-surface))}.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs>.mat-mdc-tab-header .mat-mdc-tab{flex-grow:1}.mat-mdc-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination{background-color:var(--mat-tab-header-with-background-background-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mat-mdc-tab .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background.mat-primary>.mat-mdc-tab-header .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background:not(.mat-primary)>.mat-mdc-tab-header .mat-mdc-tab:not(.mdc-tab--active) .mdc-tab-indicator__content--underline{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-focus-indicator::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-focus-indicator::before{border-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mdc-tab__ripple::before,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-ripple-element,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mdc-tab__ripple::before{background-color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header .mat-mdc-tab-header-pagination-chevron,.mat-mdc-tab-group.mat-tabs-with-background>.mat-mdc-tab-header-pagination .mat-mdc-tab-header-pagination-chevron{color:var(--mat-tab-header-with-background-foreground-color)}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header{flex-direction:column-reverse}.mat-mdc-tab-group.mat-mdc-tab-group-inverted-header .mdc-tab-indicator__content--underline{align-self:flex-start}.mat-mdc-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-mdc-tab-body-wrapper._mat-animation-noopable{transition:none !important;animation:none !important}
`],encapsulation:2})}return a})(),ei=class{index;tab};var Pa=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=ct({type:a});static \u0275inj=nt({imports:[J,J]})}return a})();var Ct=class a{constructor(n){this.authService=n;this.supabase=n.supabase}supabase;ratingsSubject=new K([]);ratings$=this.ratingsSubject.asObservable();submitRating(n,t,e,i,o){return _(this,null,function*(){try{let{data:r,error:m}=yield this.supabase.from("ratings").insert([{ride_id:n,rater_id:t,rated_id:e,rating:i,feedback:o}]).select().single();if(m)throw m;let u=this.ratingsSubject.value;return this.ratingsSubject.next([...u,r]),r}catch(r){throw console.error("Error submitting rating:",r),r}})}hasUserRated(n,t,e){return _(this,null,function*(){try{let{data:i,error:o}=yield this.supabase.from("ratings").select("id").eq("ride_id",n).eq("rater_id",t).eq("rated_id",e);if(o)throw o;return i.length>0}catch(i){return console.error("Error checking if user has rated:",i),!1}})}getRatingsByUser(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("ratings").select("*").eq("rater_id",n).order("created_at",{ascending:!1});if(e)throw e;return t}catch(t){return console.error("Error fetching ratings by user:",t),[]}})}getRatingsForUser(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("ratings").select("*").eq("rater_id",n).order("created_at",{ascending:!1});if(e)throw e;return t}catch(t){return console.error("Error fetching ratings for user:",t),[]}})}getUserRatingSummary(n){return _(this,null,function*(){try{let t=yield this.getRatingsForUser(n),e=t.length,i=e>0?t.reduce((r,m)=>r+m.rating,0)/e:0,o=t.slice(0,5);return{averageRating:i,totalRatings:e,recentRatings:o}}catch(t){return console.error("Error getting user rating summary:",t),{averageRating:0,totalRatings:0,recentRatings:[]}}})}getRatingsForRide(n){return _(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.from("ratings").select("*").eq("ride_id",n);if(e)throw e;return t}catch(t){return console.error("Error fetching ratings for ride:",t),[]}})}static \u0275fac=function(t){return new(t||a)(vt(lt))};static \u0275prov=bt({token:a,factory:a.\u0275fac,providedIn:"root"})};function Vn(a,n){if(a&1){let t=W();c(0,"button",9),T("click",function(){let i=w(t).index,o=f(2);return x(o.selectRating(i+1))}),c(1,"mat-icon"),h(2),l()()}if(a&2){let t=n.index,e=f(2);p("color",t<e.selectedRating?"accent":""),d(2),N(t<e.selectedRating?"star":"star_border")}}function zn(a,n){if(a&1){let t=W();c(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),h(3),l(),c(4,"mat-card-subtitle"),h(5),l()(),c(6,"mat-card-content")(7,"form",1),T("ngSubmit",function(){w(t);let i=f();return x(i.onSubmit())}),c(8,"div",2),y(9,Vn,3,2,"button",3),l(),c(10,"mat-form-field",4)(11,"mat-label"),h(12,"Feedback (optional)"),l(),O(13,"textarea",5),l(),c(14,"div",6)(15,"button",7),T("click",function(){w(t);let i=f();return x(i.onCancel())}),h(16,"Cancel"),l(),c(17,"button",8),h(18," Submit Rating "),l()()()()()}if(a&2){let t=f();d(3),$("Rate ",t.userToRate.full_name||t.userToRate.email,""),d(2),ee(" How was your ride from ",t.ride.pickup_location," to ",t.ride.dropoff_location,"? "),d(2),p("formGroup",t.ratingForm),d(2),p("ngForOf",t.stars),d(8),p("disabled",t.ratingForm.invalid||t.submitting)}}var Ie=class a{constructor(n,t,e,i){this.fb=n;this.ratingService=t;this.authService=e;this.snackBar=i}ride;userToRate;ratingSubmitted=new S;ratingCancelled=new S;ratingForm;stars=[1,2,3,4,5];selectedRating=0;submitting=!1;currentUser=null;ngOnInit(){this.initForm(),this.loadCurrentUser()}loadCurrentUser(){return _(this,null,function*(){this.currentUser=yield this.authService.getCurrentUser()})}initForm(){this.ratingForm=this.fb.group({rating:[0,[ft.required,ft.min(1),ft.max(5)]],feedback:[""]})}selectRating(n){this.selectedRating=n,this.ratingForm.patchValue({rating:n})}onSubmit(){return _(this,null,function*(){if(!(this.ratingForm.invalid||!this.currentUser)){this.submitting=!0;try{if(yield this.ratingService.hasUserRated(this.ride.id,this.currentUser.id,this.userToRate.id)){this.snackBar.open("You have already rated this ride","Close",{duration:3e3}),this.ratingSubmitted.emit(!1);return}yield this.ratingService.submitRating(this.ride.id,this.currentUser.id,this.userToRate.id,this.ratingForm.value.rating,this.ratingForm.value.feedback),this.snackBar.open("Rating submitted successfully","Close",{duration:3e3}),this.ratingSubmitted.emit(!0)}catch(n){console.error("Error submitting rating:",n),this.snackBar.open("Failed to submit rating","Close",{duration:3e3}),this.ratingSubmitted.emit(!1)}finally{this.submitting=!1}}})}onCancel(){this.ratingCancelled.emit()}static \u0275fac=function(t){return new(t||a)(Q(fa),Q(Ct),Q(lt),Q(me))};static \u0275cmp=k({type:a,selectors:[["app-rating-form"]],inputs:{ride:"ride",userToRate:"userToRate"},outputs:{ratingSubmitted:"ratingSubmitted",ratingCancelled:"ratingCancelled"},decls:1,vars:1,consts:[[4,"ngIf"],[3,"ngSubmit","formGroup"],[1,"star-rating"],["type","button","mat-icon-button","",3,"color","click",4,"ngFor","ngForOf"],["appearance","outline",1,"feedback-field"],["matInput","","formControlName","feedback","rows","4","placeholder","Share your experience..."],[1,"form-actions"],["mat-button","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["type","button","mat-icon-button","",3,"click","color"]],template:function(t,e){t&1&&y(0,zn,19,6,"mat-card",0),t&2&&p("ngIf",e.ride&&e.userToRate)},dependencies:[Ft,ie,Pt,fe,da,pe,ue,ca,Xe,pa,se,re,Yi,zt,Lt,Nt,Vt,Ht,Bt,ve,ge,_e,Ce,ye,Ot,At,he],styles:[".star-rating[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:20px 0}.feedback-field[_ngcontent-%COMP%]{width:100%}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;margin-top:20px}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:10px}"]})};function Un(a,n){if(a&1&&(c(0,"mat-card-subtitle"),h(1),Ue(2,"number"),l()),a&2){let t=f(2);d(),ee(" ",Bi(2,2,t.ratingSummary.averageRating,"1.1-1")," stars from ",t.ratingSummary.totalRatings," ratings ")}}function qn(a,n){a&1&&(c(0,"mat-card-subtitle"),h(1," No ratings yet "),l())}function Gn(a,n){if(a&1&&(c(0,"mat-icon",5),h(1),l()),a&2){let t=n.$implicit;p("ngClass",t),d(),$(" ",t==="full"?"star":t==="half"?"star_half":"star_border"," ")}}function Wn(a,n){a&1&&O(0,"mat-divider",6)}function $n(a,n){a&1&&(c(0,"mat-icon",17),h(1," star "),l())}function Yn(a,n){if(a&1&&(c(0,"p",18),h(1),l()),a&2){let t=f().$implicit;d(),N(t.feedback)}}function Kn(a,n){if(a&1&&(c(0,"div",9)(1,"div",10)(2,"div",11),y(3,$n,2,0,"mat-icon",12),l(),c(4,"div",13)(5,"span",14),h(6),l(),c(7,"span",15),h(8),Ue(9,"date"),l()()(),y(10,Yn,2,1,"p",16),l()),a&2){let t=n.$implicit,e=f(3);d(3),p("ngForOf",e.getFullStars(t.rating)),d(3),N(e.getRaterName(t.rater_id)),d(2),N(Li(9,4,t.created_at)),d(2),p("ngIf",t.feedback)}}function Xn(a,n){if(a&1&&(c(0,"div",7)(1,"h3"),h(2,"Recent Feedback"),l(),y(3,Kn,11,6,"div",8),l()),a&2){let t=f(2);d(3),p("ngForOf",t.ratingSummary.recentRatings)}}function Zn(a,n){if(a&1&&(c(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),h(3,"Ratings & Feedback"),l(),y(4,Un,3,5,"mat-card-subtitle",0)(5,qn,2,0,"mat-card-subtitle",0),l(),c(6,"mat-card-content")(7,"div",1),y(8,Gn,2,2,"mat-icon",2),l(),y(9,Wn,1,0,"mat-divider",3)(10,Xn,4,1,"div",4),l()()),a&2){let t=f();d(4),p("ngIf",t.ratingSummary.totalRatings>0),d(),p("ngIf",t.ratingSummary.totalRatings===0),d(3),p("ngForOf",t.getStars(t.ratingSummary.averageRating)),d(),p("ngIf",t.ratingSummary.recentRatings.length>0),d(),p("ngIf",t.ratingSummary.recentRatings.length>0)}}var Me=class a{constructor(n,t){this.ratingService=n;this.userService=t}userId;ratingSummary=null;raterNames={};ngOnInit(){this.loadRatingSummary()}loadRatingSummary(){return _(this,null,function*(){if(this.userId)try{if(this.ratingSummary=yield this.ratingService.getUserRatingSummary(this.userId),this.ratingSummary.recentRatings.length>0){let n=this.ratingSummary.recentRatings.map(e=>e.rater_id),t=[...new Set(n)];for(let e of t){let i=yield this.userService.getUserById(e);i&&(this.raterNames[e]=i.full_name||i.email)}}}catch(n){console.error("Error loading rating summary:",n)}})}getStars(n){let t=[],e=Math.floor(n),i=n%1>=.5;for(let o=0;o<e;o++)t.push("full");for(i&&t.push("half");t.length<5;)t.push("empty");return t}getFullStars(n){return Array(n).fill(0)}getRaterName(n){return this.raterNames[n]||"Anonymous"}static \u0275fac=function(t){return new(t||a)(Q(Ct),Q(we))};static \u0275cmp=k({type:a,selectors:[["app-rating-display"]],inputs:{userId:"userId"},decls:1,vars:1,consts:[[4,"ngIf"],[1,"rating-stars"],[3,"ngClass",4,"ngFor","ngForOf"],["class","rating-divider",4,"ngIf"],["class","recent-ratings",4,"ngIf"],[3,"ngClass"],[1,"rating-divider"],[1,"recent-ratings"],["class","rating-item",4,"ngFor","ngForOf"],[1,"rating-item"],[1,"rating-header"],[1,"rating-stars","small"],["class","small-icon",4,"ngFor","ngForOf"],[1,"rating-info"],[1,"rating-user"],[1,"rating-date"],["class","rating-feedback",4,"ngIf"],[1,"small-icon"],[1,"rating-feedback"]],template:function(t,e){t&1&&y(0,Zn,11,5,"mat-card",0),t&2&&p("ngIf",e.ratingSummary)},dependencies:[Ft,Et,ie,Pt,Vi,Hi,zt,Lt,Nt,Vt,Ht,Bt,Ot,At,ke,xe],styles:[".rating-stars[_ngcontent-%COMP%]{display:flex;margin:10px 0}.rating-stars.small[_ngcontent-%COMP%]{margin:0}.full[_ngcontent-%COMP%], .half[_ngcontent-%COMP%]{color:#ffc107}.empty[_ngcontent-%COMP%]{color:#e0e0e0}.small-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#ffc107}.rating-divider[_ngcontent-%COMP%]{margin:20px 0}.recent-ratings[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:16px;font-weight:500}.rating-item[_ngcontent-%COMP%]{margin-bottom:20px;padding-bottom:10px;border-bottom:1px solid #f0f0f0}.rating-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px}.rating-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end}.rating-user[_ngcontent-%COMP%]{font-weight:500}.rating-date[_ngcontent-%COMP%]{font-size:.8em;color:#757575}.rating-feedback[_ngcontent-%COMP%]{margin:0;font-style:italic;color:#555}"]})};var Jn=["userPaginator"],to=["userSort"];function eo(a,n){if(a&1&&(c(0,"div",17)(1,"span",4),h(2,"Fare:"),l(),c(3,"span",6),h(4),l()()),a&2){let t=f(2);d(4),$("$",t.ride.fare,"")}}function io(a,n){if(a&1&&(c(0,"div",3)(1,"span",4),h(2,"Payment Status:"),l(),c(3,"span",5),h(4),l()()),a&2){let t=f(2);d(3),p("ngClass","payment-"+t.ride.payment_status),d(),$(" ",t.ride.payment_status," ")}}function ao(a,n){if(a&1){let t=W();c(0,"div",18)(1,"button",19),T("click",function(){w(t);let i=f(2);return x(i.markPaymentCompleted())}),c(2,"mat-icon"),h(3,"check_circle"),l(),h(4),l()()}if(a&2){let t=f(2);d(),p("disabled",t.updatingPaymentStatus),d(3),$(" ",t.updatingPaymentStatus?"Updating...":"Mark Payment Completed"," ")}}function no(a,n){a&1&&(c(0,"mat-error"),h(1,"Fare is required"),l())}function oo(a,n){a&1&&(c(0,"mat-error"),h(1,"Fare must be greater than or equal to 0"),l())}function ro(a,n){a&1&&(c(0,"mat-error"),h(1,"Fare must be a valid number with up to 2 decimal places"),l())}function so(a,n){if(a&1){let t=W();c(0,"div",20)(1,"span",4),h(2,"Fare:"),l(),c(3,"div",21)(4,"mat-form-field",22)(5,"mat-label"),h(6,"Set Fare"),l(),O(7,"input",23),c(8,"span",24),h(9,"$\xA0"),l(),y(10,no,2,0,"mat-error",1)(11,oo,2,0,"mat-error",1)(12,ro,2,0,"mat-error",1),l(),c(13,"button",19),T("click",function(){w(t);let i=f(2);return x(i.updateFare())}),c(14,"mat-icon"),h(15,"save"),l(),h(16),l()()()}if(a&2){let t=f(2);d(7),p("formControl",t.fareControl),d(3),p("ngIf",t.fareControl.hasError("required")),d(),p("ngIf",t.fareControl.hasError("min")),d(),p("ngIf",t.fareControl.hasError("pattern")),d(),p("disabled",t.fareControl.invalid||t.updatingFare),d(3),$(" ",t.updatingFare?"Saving...":"Save"," ")}}function co(a,n){if(a&1&&(c(0,"div",3)(1,"span",4),h(2,"Distance:"),l(),c(3,"span",6),h(4),l()()),a&2){let t=f(2);d(4),$("",t.ride.distance_miles," miles")}}function lo(a,n){if(a&1&&(c(0,"div",3)(1,"span",4),h(2,"Duration:"),l(),c(3,"span",6),h(4),l()()),a&2){let t=f(2);d(4),$("",t.ride.duration_minutes," minutes")}}function mo(a,n){if(a&1&&(c(0,"div",25),O(1,"app-map-display",26),l()),a&2){let t=f(2);d(),p("origin",t.ride.pickup_location)("destination",t.ride.dropoff_location)}}function ho(a,n){if(a&1&&(c(0,"mat-tab",27),O(1,"app-ride-chat",28),l()),a&2){let t=f(2);d(),p("rideId",t.ride.id)}}function po(a,n){if(a&1){let t=W();c(0,"div")(1,"app-rating-form",32),T("ratingSubmitted",function(i){w(t);let o=f(3);return x(o.onRatingSubmitted(i))})("ratingCancelled",function(){w(t);let i=f(3);return x(i.onRatingCancelled())}),l()()}if(a&2){let t=f(3);d(),p("ride",t.ride)("userToRate",t.otherUser)}}function uo(a,n){a&1&&(c(0,"div",33)(1,"mat-icon",34),h(2,"check_circle"),l(),c(3,"p"),h(4,"You've already rated this ride. Thank you for your feedback!"),l()())}function fo(a,n){if(a&1&&(c(0,"mat-tab",29)(1,"div",30),y(2,po,2,2,"div",31)(3,uo,5,0,"ng-template",null,0,Ni),l()()),a&2){let t=Qt(4),e=f(2);d(2),p("ngIf",!e.hasRated)("ngIfElse",t)}}function _o(a,n){if(a&1&&(c(0,"mat-tab",35),O(1,"app-rating-display",36),l()),a&2){let t=f(2);d(),p("userId",t.otherUser.id)}}function go(a,n){if(a&1&&(c(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),h(3,"Ride Details"),l(),c(4,"mat-card-subtitle"),h(5),l()(),c(6,"mat-card-content")(7,"div",2)(8,"div",3)(9,"span",4),h(10,"Status:"),l(),c(11,"span",5),h(12),l()(),c(13,"div",3)(14,"span",4),h(15,"Pickup:"),l(),c(16,"span",6),h(17),l()(),c(18,"div",3)(19,"span",4),h(20,"Destination:"),l(),c(21,"span",6),h(22),l()(),c(23,"div",3)(24,"span",4),h(25,"Rider:"),l(),c(26,"span",6),h(27),l()(),c(28,"div",3)(29,"span",4),h(30,"Driver:"),l(),c(31,"span",6),h(32),l()(),c(33,"div",3)(34,"span",4),h(35,"Pickup Time:"),l(),c(36,"span",6),h(37),l()(),y(38,eo,5,1,"div",7)(39,io,5,2,"div",8)(40,ao,5,2,"div",9),l(),y(41,so,17,6,"div",10)(42,co,5,1,"div",8)(43,lo,5,1,"div",8),O(44,"mat-divider",11),c(45,"mat-tab-group")(46,"mat-tab",12),y(47,mo,2,2,"div",13),l(),y(48,ho,2,1,"mat-tab",14)(49,fo,5,2,"mat-tab",15)(50,_o,2,1,"mat-tab",16),l()()()),a&2){let t=f();d(5),N(t.formatDate(t.ride.created_at)),d(6),p("ngClass","status-"+t.ride.status),d(),$(" ",t.formatStatus(t.ride.status)," "),d(5),N(t.ride.pickup_location),d(5),N(t.ride.dropoff_location),d(5),N(t.getUserName(t.ride.rider_id)),d(5),N(t.ride.driver_id?t.getUserName(t.ride.driver_id):"No driver assigned"),d(5),N(t.formatDate(t.ride.pickup_time)),d(),p("ngIf",t.ride.fare&&(t.isAdmin||t.isRider)&&!t.isDriver),d(),p("ngIf",t.ride.payment_status),d(),p("ngIf",t.isAdmin&&t.ride.status==="completed"&&(!t.ride.payment_status||t.ride.payment_status!=="completed")),d(),p("ngIf",t.isAdmin&&t.ride.status==="requested"),d(),p("ngIf",t.ride.distance_miles),d(),p("ngIf",t.ride.duration_minutes),d(4),p("ngIf",t.ride.pickup_location&&t.ride.dropoff_location),d(),p("ngIf",t.ride.status!=="requested"&&t.otherUser),d(),p("ngIf",t.canRate&&t.otherUser),d(),p("ngIf",t.otherUser)}}var Ba=class a{constructor(n,t,e,i,o,r){this.rideService=n;this.userService=t;this.authService=e;this.ratingService=i;this.paymentService=o;this.snackBar=r}rideId;onClose=()=>{};paymentRequested=new S;rideUpdated=new S;loadingUsers=!1;ride=null;currentUser=null;otherUser=null;isRider=!1;isDriver=!1;canRate=!1;hasRated=!1;filteredUsers=[];userPaginator;userDisplayedColumns=["email","full_name","role","created_at","status","actions"];userRoleFilter="";userSearchTerm="";userSort;users=[];fareControl=new la(null,[ft.required,ft.min(0),ft.pattern(/^\d+(\.\d{1,2})?$/)]);isAdmin=!1;updatingFare=!1;updatingPaymentStatus=!1;ngOnInit(){this.loadRideDetails(),this.checkIfAdmin(),this.loadUsers()}checkIfAdmin(){return _(this,null,function*(){let n=yield this.authService.getUserRole();this.isAdmin=n==="admin"})}loadUsers(){return _(this,null,function*(){this.loadingUsers=!0;try{this.users=yield this.userService.getAllUsers(),this.applyUserFilters()}catch(n){console.error("Error loading users:",n),this.snackBar.open("Failed to load users","Close",{duration:3e3})}finally{this.loadingUsers=!1}})}loadRideDetails(){return _(this,null,function*(){if(this.rideId)try{if(this.currentUser=yield this.authService.getCurrentUser(),!this.currentUser||(this.ride=yield this.rideService.getRide(this.rideId),!this.ride))return;this.ride.fare!==void 0&&this.ride.fare!==null&&this.fareControl.setValue(this.ride.fare),this.isRider=this.currentUser.id===this.ride.rider_id,this.isDriver=this.ride.driver_id?this.currentUser.id===this.ride.driver_id:!1;let n=this.isRider?this.ride.driver_id:this.ride.rider_id;n&&(this.otherUser=yield this.userService.getUserById(n)),this.canRate=this.canUserRate(),this.canRate&&this.otherUser&&(this.hasRated=yield this.ratingService.hasUserRated(this.ride.id,this.currentUser.id,this.otherUser.id))}catch(n){console.error("Error loading ride details:",n),this.snackBar.open("Failed to load ride details","Close",{duration:3e3})}})}applyUserFilters(){let n=[...this.users];if(this.userRoleFilter&&(n=n.filter(t=>t.role===this.userRoleFilter)),this.userSearchTerm){let t=this.userSearchTerm.toLowerCase();n=n.filter(e=>e.email.toLowerCase().includes(t)||e.full_name&&e.full_name.toLowerCase().includes(t))}this.filteredUsers=n,this.userPaginator&&this.userPaginator.firstPage(),this.userSort&&this.userSort.sort({id:"",start:"asc",disableClear:!1})}canUserRate(){return!this.ride||!this.currentUser?!1:this.ride.status==="completed"&&(this.currentUser.id===this.ride.rider_id||this.currentUser.id===this.ride.driver_id)&&(this.isRider?!!this.ride.driver_id:!0)}formatDate(n){return new Date(n).toLocaleString()}formatStatus(n){return n.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}onRatingSubmitted(n){return _(this,null,function*(){n&&(this.hasRated=!0,this.snackBar.open("Rating submitted successfully","Close",{duration:3e3}))})}onRatingCancelled(){}getUserName(n){return n?this.users.filter(t=>t.id===n)[0].full_name||"":"N/A"}updateFare(){return _(this,null,function*(){if(!(!this.ride||this.fareControl.invalid)){this.updatingFare=!0;try{let n=this.fareControl.value;if(n===null){this.snackBar.open("Please enter a valid fare amount","Close",{duration:3e3});return}(yield this.rideService.updateRide(this.ride.id,{fare:n}))?(this.snackBar.open("Fare updated successfully","Close",{duration:3e3}),this.ride=yield this.rideService.getRide(this.ride.id),this.ride&&this.rideUpdated.emit(this.ride)):this.snackBar.open("Failed to update fare","Close",{duration:3e3})}catch(n){console.error("Error updating fare:",n),this.snackBar.open("An error occurred while updating the fare","Close",{duration:3e3})}finally{this.updatingFare=!1}}})}viewPayment(n){this.paymentRequested.emit(n)}markPaymentCompleted(){return _(this,null,function*(){if(this.ride){this.updatingPaymentStatus=!0;try{(yield this.paymentService.updateRidePaymentStatus(this.ride.id,"completed"))?(this.snackBar.open("Payment status marked as completed","Close",{duration:3e3}),this.ride=yield this.rideService.getRide(this.ride.id),this.ride&&this.rideUpdated.emit(this.ride)):this.snackBar.open("Failed to update payment status","Close",{duration:3e3})}catch(n){console.error("Error updating payment status:",n),this.snackBar.open("An error occurred while updating the payment status","Close",{duration:3e3})}finally{this.updatingPaymentStatus=!1}}})}static \u0275fac=function(t){return new(t||a)(Q(wa),Q(we),Q(lt),Q(Ct),Q(Se),Q(me))};static \u0275cmp=k({type:a,selectors:[["app-ride-detail"]],viewQuery:function(t,e){if(t&1&&(L(Jn,5),L(to,5)),t&2){let i;g(i=b())&&(e.userPaginator=i.first),g(i=b())&&(e.userSort=i.first)}},inputs:{rideId:"rideId",onClose:"onClose"},outputs:{paymentRequested:"paymentRequested",rideUpdated:"rideUpdated"},decls:1,vars:1,consts:[["alreadyRated",""],[4,"ngIf"],[1,"ride-details"],[1,"detail-row"],[1,"label"],[1,"value","status-badge",3,"ngClass"],[1,"value"],["class","detail-row","style","display: flex; align-items: center;",4,"ngIf"],["class","detail-row",4,"ngIf"],["class","detail-row","style","margin-top: 10px;",4,"ngIf"],["class","detail-row admin-fare-input","stlye","display: flex; align-items: start;",4,"ngIf"],[1,"section-divider"],["label","Map"],["class","map-container",4,"ngIf"],["label","Chat",4,"ngIf"],["label","Rate",4,"ngIf"],["label","Ratings",4,"ngIf"],[1,"detail-row",2,"display","flex","align-items","center"],[1,"detail-row",2,"margin-top","10px"],["mat-raised-button","","color","primary",3,"click","disabled"],["stlye","display: flex; align-items: start;",1,"detail-row","admin-fare-input"],[1,"admin-input-container",2,"display","flex","align-items","start"],["appearance","outline",1,"fare-input"],["matInput","","type","number","step","0.01","min","0","placeholder","Enter fare amount",3,"formControl"],["matPrefix",""],[1,"map-container"],[3,"origin","destination"],["label","Chat"],[3,"rideId"],["label","Rate"],[1,"rating-container"],[4,"ngIf","ngIfElse"],[3,"ratingSubmitted","ratingCancelled","ride","userToRate"],[1,"already-rated-message"],["color","primary"],["label","Ratings"],[3,"userId"]],template:function(t,e){t&1&&y(0,go,51,18,"mat-card",1),t&2&&p("ngIf",e.ride)},dependencies:[Ft,Et,Pt,fe,pe,ma,ue,ua,ha,zt,Lt,Nt,Vt,Ht,Bt,se,re,Ot,At,ke,xe,Pa,ai,Ea,he,Ce,ye,ge,_e,ga,ba,ve,Ie,Me,xa,Ca],styles:["mat-card[_ngcontent-%COMP%]{max-width:800px;margin:180px auto 0;padding:20px}.close-button[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px}.ride-details[_ngcontent-%COMP%]{margin:20px 0}.detail-row[_ngcontent-%COMP%]{display:flex;margin-bottom:10px}.label[_ngcontent-%COMP%]{font-weight:500;width:120px;color:#666}.value[_ngcontent-%COMP%]{flex:1}.status-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px;font-size:.9em;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#f0f0f0;color:#666}.status-assigned[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1976d2}.status-in-progress[_ngcontent-%COMP%], .status-completed[_ngcontent-%COMP%]{background-color:#e8f5e9;color:#388e3c}.status-canceled[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.payment-pending[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57c00}.payment-completed[_ngcontent-%COMP%], .payment-paid[_ngcontent-%COMP%]{background-color:#e8f5e9;color:#388e3c}.payment-failed[_ngcontent-%COMP%]{background-color:#ffebee;color:#d32f2f}.payment-refunded[_ngcontent-%COMP%]{background-color:#e0f7fa;color:#0097a7}.section-divider[_ngcontent-%COMP%]{margin:20px 0}.map-container[_ngcontent-%COMP%]{margin-top:20px}.rating-container[_ngcontent-%COMP%]{padding:20px 0}.already-rated-message[_ngcontent-%COMP%]{display:flex;align-items:center;padding:20px;background-color:#f5f5f5;border-radius:4px}.already-rated-message[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:10px}.admin-fare-input[_ngcontent-%COMP%]{margin-top:20px;display:flex;align-items:start;justify-self:start}.mdc-text-field--outlined[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%], .mdc-text-field--no-label[_ngcontent-%COMP%]   .mat-mdc-form-field-infix[_ngcontent-%COMP%]{padding-top:var(8px);padding-bottom:var(8px)}.mat-mdc-form-field-infix[_ngcontent-%COMP%]{min-height:40px!important}.admin-input-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.fare-input[_ngcontent-%COMP%]{width:150px}mat-form-field[_ngcontent-%COMP%]{width:150px}"]})};var za=["*",[["mat-chip-avatar"],["","matChipAvatar",""]],[["mat-chip-trailing-icon"],["","matChipRemove",""],["","matChipTrailingIcon",""]]],ja=["*","mat-chip-avatar, [matChipAvatar]","mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]"];function bo(a,n){a&1&&(c(0,"span",3),F(1,1),l())}function vo(a,n){a&1&&(c(0,"span",6),F(1,2),l())}function yo(a,n){a&1&&(c(0,"span",3),F(1,1),c(2,"span",8),Oi(),c(3,"svg",9),O(4,"path",10),l()()())}function Co(a,n){a&1&&(c(0,"span",6),F(1,2),l())}var wo=`.mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:"";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:"";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:""}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}
`;var Qa=["*"],xo=`.mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}
`,ci=new B("mat-chips-default-options",{providedIn:"root",factory:()=>({separatorKeyCodes:[13]})}),Na=new B("MatChipAvatar"),Ha=new B("MatChipTrailingIcon"),Va=new B("MatChipRemove"),li=new B("MatChip"),oi=(()=>{class a{_elementRef=s(E);_parentChip=s(li);isInteractive=!0;_isPrimary=!0;get disabled(){return this._disabled||this._parentChip?.disabled||!1}set disabled(t){this._disabled=t}_disabled=!1;tabIndex=-1;_allowFocusWhenDisabled=!1;_getDisabledAttribute(){return this.disabled&&!this._allowFocusWhenDisabled?"":null}_getTabindex(){return this.disabled&&!this._allowFocusWhenDisabled||!this.isInteractive?null:this.tabIndex.toString()}constructor(){s(qt).load(Gt),this._elementRef.nativeElement.nodeName==="BUTTON"&&this._elementRef.nativeElement.setAttribute("type","button")}focus(){this._elementRef.nativeElement.focus()}_handleClick(t){!this.disabled&&this.isInteractive&&this._isPrimary&&(t.preventDefault(),this._parentChip._handlePrimaryActionInteraction())}_handleKeydown(t){(t.keyCode===13||t.keyCode===32)&&!this.disabled&&this.isInteractive&&this._isPrimary&&!this._parentChip._isEditing&&(t.preventDefault(),this._parentChip._handlePrimaryActionInteraction())}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","matChipAction",""]],hostAttrs:[1,"mdc-evolution-chip__action","mat-mdc-chip-action"],hostVars:9,hostBindings:function(e,i){e&1&&T("click",function(r){return i._handleClick(r)})("keydown",function(r){return i._handleKeydown(r)}),e&2&&(U("tabindex",i._getTabindex())("disabled",i._getDisabledAttribute())("aria-disabled",i.disabled),P("mdc-evolution-chip__action--primary",i._isPrimary)("mdc-evolution-chip__action--presentational",!i.isInteractive)("mdc-evolution-chip__action--trailing",!i._isPrimary))},inputs:{isInteractive:"isInteractive",disabled:[2,"disabled","disabled",C],tabIndex:[2,"tabIndex","tabIndex",t=>t==null?-1:Dt(t)],_allowFocusWhenDisabled:"_allowFocusWhenDisabled"}})}return a})();var ri=(()=>{class a{_changeDetectorRef=s(et);_elementRef=s(E);_ngZone=s(ot);_focusMonitor=s(Ut);_globalRippleOptions=s($e,{optional:!0});_document=s(Mt);_onFocus=new j;_onBlur=new j;_isBasicChip;role=null;_hasFocusInternal=!1;_pendingFocus;_actionChanges;_animationsDisabled;_allLeadingIcons;_allTrailingIcons;_allRemoveIcons;_hasFocus(){return this._hasFocusInternal}id=s(ae).getId("mat-mdc-chip-");ariaLabel=null;ariaDescription=null;_ariaDescriptionId=`${this.id}-aria-description`;_chipListDisabled=!1;_textElement;get value(){return this._value!==void 0?this._value:this._textElement.textContent.trim()}set value(t){this._value=t}_value;color;removable=!0;highlighted=!1;disableRipple=!1;get disabled(){return this._disabled||this._chipListDisabled}set disabled(t){this._disabled=t}_disabled=!1;removed=new S;destroyed=new S;basicChipAttrName="mat-basic-chip";leadingIcon;trailingIcon;removeIcon;primaryAction;_rippleLoader=s($i);_injector=s(pt);constructor(){let t=s(qt);t.load(Gt),t.load(Ui);let e=s(yt,{optional:!0});this._animationsDisabled=e==="NoopAnimations",this._monitorFocus(),this._rippleLoader?.configureRipple(this._elementRef.nativeElement,{className:"mat-mdc-chip-ripple",disabled:this._isRippleDisabled()})}ngOnInit(){let t=this._elementRef.nativeElement;this._isBasicChip=t.hasAttribute(this.basicChipAttrName)||t.tagName.toLowerCase()===this.basicChipAttrName}ngAfterViewInit(){this._textElement=this._elementRef.nativeElement.querySelector(".mat-mdc-chip-action-label"),this._pendingFocus&&(this._pendingFocus=!1,this.focus())}ngAfterContentInit(){this._actionChanges=at(this._allLeadingIcons.changes,this._allTrailingIcons.changes,this._allRemoveIcons.changes).subscribe(()=>this._changeDetectorRef.markForCheck())}ngDoCheck(){this._rippleLoader.setDisabled(this._elementRef.nativeElement,this._isRippleDisabled())}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement),this._actionChanges?.unsubscribe(),this.destroyed.emit({chip:this}),this.destroyed.complete()}remove(){this.removable&&this.removed.emit({chip:this})}_isRippleDisabled(){return this.disabled||this.disableRipple||this._animationsDisabled||this._isBasicChip||!!this._globalRippleOptions?.disabled}_hasTrailingIcon(){return!!(this.trailingIcon||this.removeIcon)}_handleKeydown(t){(t.keyCode===8&&!t.repeat||t.keyCode===46)&&(t.preventDefault(),this.remove())}focus(){this.disabled||(this.primaryAction?this.primaryAction.focus():this._pendingFocus=!0)}_getSourceAction(t){return this._getActions().find(e=>{let i=e._elementRef.nativeElement;return i===t||i.contains(t)})}_getActions(){let t=[];return this.primaryAction&&t.push(this.primaryAction),this.removeIcon&&t.push(this.removeIcon),this.trailingIcon&&t.push(this.trailingIcon),t}_handlePrimaryActionInteraction(){}_monitorFocus(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(t=>{let e=t!==null;e!==this._hasFocusInternal&&(this._hasFocusInternal=e,e?this._onFocus.next({chip:this}):(this._changeDetectorRef.markForCheck(),setTimeout(()=>this._ngZone.run(()=>this._onBlur.next({chip:this})))))})}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["mat-basic-chip"],["","mat-basic-chip",""],["mat-chip"],["","mat-chip",""]],contentQueries:function(e,i,o){if(e&1&&(I(o,Na,5),I(o,Ha,5),I(o,Va,5),I(o,Na,5),I(o,Ha,5),I(o,Va,5)),e&2){let r;g(r=b())&&(i.leadingIcon=r.first),g(r=b())&&(i.trailingIcon=r.first),g(r=b())&&(i.removeIcon=r.first),g(r=b())&&(i._allLeadingIcons=r),g(r=b())&&(i._allTrailingIcons=r),g(r=b())&&(i._allRemoveIcons=r)}},viewQuery:function(e,i){if(e&1&&L(oi,5),e&2){let o;g(o=b())&&(i.primaryAction=o.first)}},hostAttrs:[1,"mat-mdc-chip"],hostVars:31,hostBindings:function(e,i){e&1&&T("keydown",function(r){return i._handleKeydown(r)}),e&2&&(te("id",i.id),U("role",i.role)("aria-label",i.ariaLabel),Tt("mat-"+(i.color||"primary")),P("mdc-evolution-chip",!i._isBasicChip)("mdc-evolution-chip--disabled",i.disabled)("mdc-evolution-chip--with-trailing-action",i._hasTrailingIcon())("mdc-evolution-chip--with-primary-graphic",i.leadingIcon)("mdc-evolution-chip--with-primary-icon",i.leadingIcon)("mdc-evolution-chip--with-avatar",i.leadingIcon)("mat-mdc-chip-with-avatar",i.leadingIcon)("mat-mdc-chip-highlighted",i.highlighted)("mat-mdc-chip-disabled",i.disabled)("mat-mdc-basic-chip",i._isBasicChip)("mat-mdc-standard-chip",!i._isBasicChip)("mat-mdc-chip-with-trailing-icon",i._hasTrailingIcon())("_mat-animation-noopable",i._animationsDisabled))},inputs:{role:"role",id:"id",ariaLabel:[0,"aria-label","ariaLabel"],ariaDescription:[0,"aria-description","ariaDescription"],value:"value",color:"color",removable:[2,"removable","removable",C],highlighted:[2,"highlighted","highlighted",C],disableRipple:[2,"disableRipple","disableRipple",C],disabled:[2,"disabled","disabled",C]},outputs:{removed:"removed",destroyed:"destroyed"},exportAs:["matChip"],features:[A([{provide:li,useExisting:a}])],ngContentSelectors:ja,decls:8,vars:3,consts:[[1,"mat-mdc-chip-focus-overlay"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--primary"],["matChipAction","",3,"isInteractive"],[1,"mdc-evolution-chip__graphic","mat-mdc-chip-graphic"],[1,"mdc-evolution-chip__text-label","mat-mdc-chip-action-label"],[1,"mat-mdc-chip-primary-focus-indicator","mat-focus-indicator"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--trailing"]],template:function(e,i){e&1&&(Z(za),O(0,"span",0),c(1,"span",1)(2,"span",2),y(3,bo,2,0,"span",3),c(4,"span",4),F(5),O(6,"span",5),l()()(),y(7,vo,2,0,"span",6)),e&2&&(d(2),p("isInteractive",!1),d(),q(i.leadingIcon?3:-1),d(4),q(i._hasTrailingIcon()?7:-1))},dependencies:[oi],styles:[`.mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:"";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:"";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:""}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}
`],encapsulation:2,changeDetection:0})}return a})();var ko=(()=>{class a extends ri{_defaultOptions=s(ci,{optional:!0});chipListSelectable=!0;_chipListMultiple=!1;_chipListHideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1;get selectable(){return this._selectable&&this.chipListSelectable}set selectable(t){this._selectable=t,this._changeDetectorRef.markForCheck()}_selectable=!0;get selected(){return this._selected}set selected(t){this._setSelectedState(t,!1,!0)}_selected=!1;get ariaSelected(){return this.selectable?this.selected.toString():null}basicChipAttrName="mat-basic-chip-option";selectionChange=new S;ngOnInit(){super.ngOnInit(),this.role="presentation"}select(){this._setSelectedState(!0,!1,!0)}deselect(){this._setSelectedState(!1,!1,!0)}selectViaInteraction(){this._setSelectedState(!0,!0,!0)}toggleSelected(t=!1){return this._setSelectedState(!this.selected,t,!0),this.selected}_handlePrimaryActionInteraction(){this.disabled||(this.focus(),this.selectable&&this.toggleSelected(!0))}_hasLeadingGraphic(){return this.leadingIcon?!0:!this._chipListHideSingleSelectionIndicator||this._chipListMultiple}_setSelectedState(t,e,i){t!==this.selected&&(this._selected=t,i&&this.selectionChange.emit({source:this,isUserInput:e,selected:this.selected}),this._changeDetectorRef.markForCheck())}static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275cmp=k({type:a,selectors:[["mat-basic-chip-option"],["","mat-basic-chip-option",""],["mat-chip-option"],["","mat-chip-option",""]],hostAttrs:[1,"mat-mdc-chip","mat-mdc-chip-option"],hostVars:37,hostBindings:function(e,i){e&2&&(te("id",i.id),U("tabindex",null)("aria-label",null)("aria-description",null)("role",i.role),P("mdc-evolution-chip",!i._isBasicChip)("mdc-evolution-chip--filter",!i._isBasicChip)("mdc-evolution-chip--selectable",!i._isBasicChip)("mat-mdc-chip-selected",i.selected)("mat-mdc-chip-multiple",i._chipListMultiple)("mat-mdc-chip-disabled",i.disabled)("mat-mdc-chip-with-avatar",i.leadingIcon)("mdc-evolution-chip--disabled",i.disabled)("mdc-evolution-chip--selected",i.selected)("mdc-evolution-chip--selecting",!i._animationsDisabled)("mdc-evolution-chip--with-trailing-action",i._hasTrailingIcon())("mdc-evolution-chip--with-primary-icon",i.leadingIcon)("mdc-evolution-chip--with-primary-graphic",i._hasLeadingGraphic())("mdc-evolution-chip--with-avatar",i.leadingIcon)("mat-mdc-chip-highlighted",i.highlighted)("mat-mdc-chip-with-trailing-icon",i._hasTrailingIcon()))},inputs:{selectable:[2,"selectable","selectable",C],selected:[2,"selected","selected",C]},outputs:{selectionChange:"selectionChange"},features:[A([{provide:ri,useExisting:a},{provide:li,useExisting:a}]),R],ngContentSelectors:ja,decls:10,vars:8,consts:[[1,"mat-mdc-chip-focus-overlay"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--primary"],["matChipAction","","role","option",3,"_allowFocusWhenDisabled"],[1,"mdc-evolution-chip__graphic","mat-mdc-chip-graphic"],[1,"mdc-evolution-chip__text-label","mat-mdc-chip-action-label"],[1,"mat-mdc-chip-primary-focus-indicator","mat-focus-indicator"],[1,"mdc-evolution-chip__cell","mdc-evolution-chip__cell--trailing"],[1,"cdk-visually-hidden",3,"id"],[1,"mdc-evolution-chip__checkmark"],["viewBox","-2 -3 30 30","focusable","false","aria-hidden","true",1,"mdc-evolution-chip__checkmark-svg"],["fill","none","stroke","currentColor","d","M1.73,12.91 8.1,19.28 22.79,4.59",1,"mdc-evolution-chip__checkmark-path"]],template:function(e,i){e&1&&(Z(za),O(0,"span",0),c(1,"span",1)(2,"button",2),y(3,yo,5,0,"span",3),c(4,"span",4),F(5),O(6,"span",5),l()()(),y(7,Co,2,0,"span",6),c(8,"span",7),h(9),l()),e&2&&(d(2),p("_allowFocusWhenDisabled",!0),U("aria-selected",i.ariaSelected)("aria-label",i.ariaLabel)("aria-describedby",i._ariaDescriptionId),d(),q(i._hasLeadingGraphic()?3:-1),d(4),q(i._hasTrailingIcon()?7:-1),d(),p("id",i._ariaDescriptionId),d(),N(i.ariaDescription))},dependencies:[oi],styles:[wo],encapsulation:2,changeDetection:0})}return a})();var Do=(()=>{class a{_elementRef=s(E);_changeDetectorRef=s(et);_dir=s(ut,{optional:!0});_lastDestroyedFocusedChipIndex=null;_keyManager;_destroyed=new j;_defaultRole="presentation";get chipFocusChanges(){return this._getChipStream(t=>t._onFocus)}get chipDestroyedChanges(){return this._getChipStream(t=>t.destroyed)}get chipRemovedChanges(){return this._getChipStream(t=>t.removed)}get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._syncChipsState()}_disabled=!1;get empty(){return!this._chips||this._chips.length===0}get role(){return this._explicitRole?this._explicitRole:this.empty?null:this._defaultRole}tabIndex=0;set role(t){this._explicitRole=t}_explicitRole=null;get focused(){return this._hasFocusedChip()}_chips;_chipActions=new Zt;constructor(){}ngAfterViewInit(){this._setUpFocusManagement(),this._trackChipSetChanges(),this._trackDestroyedFocusedChip()}ngOnDestroy(){this._keyManager?.destroy(),this._chipActions.destroy(),this._destroyed.next(),this._destroyed.complete()}_hasFocusedChip(){return this._chips&&this._chips.some(t=>t._hasFocus())}_syncChipsState(){this._chips?.forEach(t=>{t._chipListDisabled=this._disabled,t._changeDetectorRef.markForCheck()})}focus(){}_handleKeydown(t){this._originatesFromChip(t)&&this._keyManager.onKeydown(t)}_isValidIndex(t){return t>=0&&t<this._chips.length}_allowFocusEscape(){let t=this._elementRef.nativeElement.tabIndex;t!==-1&&(this._elementRef.nativeElement.tabIndex=-1,setTimeout(()=>this._elementRef.nativeElement.tabIndex=t))}_getChipStream(t){return this._chips.changes.pipe(ht(null),Xt(()=>at(...this._chips.map(t))))}_originatesFromChip(t){let e=t.target;for(;e&&e!==this._elementRef.nativeElement;){if(e.classList.contains("mat-mdc-chip"))return!0;e=e.parentElement}return!1}_setUpFocusManagement(){this._chips.changes.pipe(ht(this._chips)).subscribe(t=>{let e=[];t.forEach(i=>i._getActions().forEach(o=>e.push(o))),this._chipActions.reset(e),this._chipActions.notifyOnChanges()}),this._keyManager=new oe(this._chipActions).withVerticalOrientation().withHorizontalOrientation(this._dir?this._dir.value:"ltr").withHomeAndEnd().skipPredicate(t=>this._skipPredicate(t)),this.chipFocusChanges.pipe(D(this._destroyed)).subscribe(({chip:t})=>{let e=t._getSourceAction(document.activeElement);e&&this._keyManager.updateActiveItem(e)}),this._dir?.change.pipe(D(this._destroyed)).subscribe(t=>this._keyManager.withHorizontalOrientation(t))}_skipPredicate(t){return!t.isInteractive||t.disabled}_trackChipSetChanges(){this._chips.changes.pipe(ht(null),D(this._destroyed)).subscribe(()=>{this.disabled&&Promise.resolve().then(()=>this._syncChipsState()),this._redirectDestroyedChipFocus()})}_trackDestroyedFocusedChip(){this.chipDestroyedChanges.pipe(D(this._destroyed)).subscribe(t=>{let i=this._chips.toArray().indexOf(t.chip);this._isValidIndex(i)&&t.chip._hasFocus()&&(this._lastDestroyedFocusedChipIndex=i)})}_redirectDestroyedChipFocus(){if(this._lastDestroyedFocusedChipIndex!=null){if(this._chips.length){let t=Math.min(this._lastDestroyedFocusedChipIndex,this._chips.length-1),e=this._chips.toArray()[t];e.disabled?this._chips.length===1?this.focus():this._keyManager.setPreviousItemActive():e.focus()}else this.focus();this._lastDestroyedFocusedChipIndex=null}}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["mat-chip-set"]],contentQueries:function(e,i,o){if(e&1&&I(o,ri,5),e&2){let r;g(r=b())&&(i._chips=r)}},hostAttrs:[1,"mat-mdc-chip-set","mdc-evolution-chip-set"],hostVars:1,hostBindings:function(e,i){e&1&&T("keydown",function(r){return i._handleKeydown(r)}),e&2&&U("role",i.role)},inputs:{disabled:[2,"disabled","disabled",C],role:"role",tabIndex:[2,"tabIndex","tabIndex",t=>t==null?0:Dt(t)]},ngContentSelectors:Qa,decls:2,vars:0,consts:[["role","presentation",1,"mdc-evolution-chip-set__chips"]],template:function(e,i){e&1&&(Z(),c(0,"div",0),F(1),l())},styles:[`.mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}
`],encapsulation:2,changeDetection:0})}return a})(),si=class{source;value;constructor(n,t){this.source=n,this.value=t}},Ro={provide:sa,useExisting:Ai(()=>So),multi:!0},So=(()=>{class a extends Do{_onTouched=()=>{};_onChange=()=>{};_defaultRole="listbox";_defaultOptions=s(ci,{optional:!0});get multiple(){return this._multiple}set multiple(t){this._multiple=t,this._syncListboxProperties()}_multiple=!1;get selected(){let t=this._chips.toArray().filter(e=>e.selected);return this.multiple?t:t[0]}ariaOrientation="horizontal";get selectable(){return this._selectable}set selectable(t){this._selectable=t,this._syncListboxProperties()}_selectable=!0;compareWith=(t,e)=>t===e;required=!1;get hideSingleSelectionIndicator(){return this._hideSingleSelectionIndicator}set hideSingleSelectionIndicator(t){this._hideSingleSelectionIndicator=t,this._syncListboxProperties()}_hideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1;get chipSelectionChanges(){return this._getChipStream(t=>t.selectionChange)}get chipBlurChanges(){return this._getChipStream(t=>t._onBlur)}get value(){return this._value}set value(t){this._chips&&this._chips.length&&this._setSelectionByValue(t,!1),this._value=t}_value;change=new S;_chips=void 0;ngAfterContentInit(){this._chips.changes.pipe(ht(null),D(this._destroyed)).subscribe(()=>{this.value!==void 0&&Promise.resolve().then(()=>{this._setSelectionByValue(this.value,!1)}),this._syncListboxProperties()}),this.chipBlurChanges.pipe(D(this._destroyed)).subscribe(()=>this._blur()),this.chipSelectionChanges.pipe(D(this._destroyed)).subscribe(t=>{this.multiple||this._chips.forEach(e=>{e!==t.source&&e._setSelectedState(!1,!1,!1)}),t.isUserInput&&this._propagateChanges()})}focus(){if(this.disabled)return;let t=this._getFirstSelectedChip();t&&!t.disabled?t.focus():this._chips.length>0?this._keyManager.setFirstItemActive():this._elementRef.nativeElement.focus()}writeValue(t){t!=null?this.value=t:this.value=void 0}registerOnChange(t){this._onChange=t}registerOnTouched(t){this._onTouched=t}setDisabledState(t){this.disabled=t}_setSelectionByValue(t,e=!0){this._clearSelection(),Array.isArray(t)?t.forEach(i=>this._selectValue(i,e)):this._selectValue(t,e)}_blur(){this.disabled||setTimeout(()=>{this.focused||this._markAsTouched()})}_keydown(t){t.keyCode===9&&super._allowFocusEscape()}_markAsTouched(){this._onTouched(),this._changeDetectorRef.markForCheck()}_propagateChanges(){let t=null;Array.isArray(this.selected)?t=this.selected.map(e=>e.value):t=this.selected?this.selected.value:void 0,this._value=t,this.change.emit(new si(this,t)),this._onChange(t),this._changeDetectorRef.markForCheck()}_clearSelection(t){this._chips.forEach(e=>{e!==t&&e.deselect()})}_selectValue(t,e){let i=this._chips.find(o=>o.value!=null&&this.compareWith(o.value,t));return i&&(e?i.selectViaInteraction():i.select()),i}_syncListboxProperties(){this._chips&&Promise.resolve().then(()=>{this._chips.forEach(t=>{t._chipListMultiple=this.multiple,t.chipListSelectable=this._selectable,t._chipListHideSingleSelectionIndicator=this.hideSingleSelectionIndicator,t._changeDetectorRef.markForCheck()})})}_getFirstSelectedChip(){return Array.isArray(this.selected)?this.selected.length?this.selected[0]:void 0:this.selected}_skipPredicate(t){return!t.isInteractive}static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275cmp=k({type:a,selectors:[["mat-chip-listbox"]],contentQueries:function(e,i,o){if(e&1&&I(o,ko,5),e&2){let r;g(r=b())&&(i._chips=r)}},hostAttrs:[1,"mdc-evolution-chip-set","mat-mdc-chip-listbox"],hostVars:10,hostBindings:function(e,i){e&1&&T("focus",function(){return i.focus()})("blur",function(){return i._blur()})("keydown",function(r){return i._keydown(r)}),e&2&&(te("tabIndex",i.disabled||i.empty?-1:i.tabIndex),U("role",i.role)("aria-required",i.role?i.required:null)("aria-disabled",i.disabled.toString())("aria-multiselectable",i.multiple)("aria-orientation",i.ariaOrientation),P("mat-mdc-chip-list-disabled",i.disabled)("mat-mdc-chip-list-required",i.required))},inputs:{multiple:[2,"multiple","multiple",C],ariaOrientation:[0,"aria-orientation","ariaOrientation"],selectable:[2,"selectable","selectable",C],compareWith:"compareWith",required:[2,"required","required",C],hideSingleSelectionIndicator:[2,"hideSingleSelectionIndicator","hideSingleSelectionIndicator",C],value:"value"},outputs:{change:"change"},features:[A([Ro]),R],ngContentSelectors:Qa,decls:2,vars:0,consts:[["role","presentation",1,"mdc-evolution-chip-set__chips"]],template:function(e,i){e&1&&(Z(),c(0,"div",0),F(1),l())},styles:[xo],encapsulation:2,changeDetection:0})}return a})();var ic=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=ct({type:a});static \u0275inj=nt({providers:[va,{provide:ci,useValue:{separatorKeyCodes:[13]}}],imports:[J,Ki,J]})}return a})();var Eo=[[["caption"]],[["colgroup"],["col"]],"*"],Po=["caption","colgroup, col","*"];function Fo(a,n){a&1&&F(0,2)}function Ao(a,n){a&1&&(c(0,"thead",0),G(1,1),l(),c(2,"tbody",0),G(3,2)(4,3),l(),c(5,"tfoot",0),G(6,4),l())}function Oo(a,n){a&1&&G(0,1)(1,2)(2,3)(3,4)}var it=new B("CDK_TABLE");var Le=(()=>{class a{template=s(X);constructor(){}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkCellDef",""]]})}return a})(),Be=(()=>{class a{template=s(X);constructor(){}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkHeaderCellDef",""]]})}return a})(),Ga=(()=>{class a{template=s(X);constructor(){}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkFooterCellDef",""]]})}return a})(),jt=(()=>{class a{_table=s(it,{optional:!0});_hasStickyChanged=!1;get name(){return this._name}set name(t){this._setNameInput(t)}_name;get sticky(){return this._sticky}set sticky(t){t!==this._sticky&&(this._sticky=t,this._hasStickyChanged=!0)}_sticky=!1;get stickyEnd(){return this._stickyEnd}set stickyEnd(t){t!==this._stickyEnd&&(this._stickyEnd=t,this._hasStickyChanged=!0)}_stickyEnd=!1;cell;headerCell;footerCell;cssClassFriendlyName;_columnCssClassName;constructor(){}hasStickyChanged(){let t=this._hasStickyChanged;return this.resetStickyChanged(),t}resetStickyChanged(){this._hasStickyChanged=!1}_updateColumnCssClassName(){this._columnCssClassName=[`cdk-column-${this.cssClassFriendlyName}`]}_setNameInput(t){t&&(this._name=t,this.cssClassFriendlyName=t.replace(/[^a-z0-9_-]/gi,"-"),this._updateColumnCssClassName())}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkColumnDef",""]],contentQueries:function(e,i,o){if(e&1&&(I(o,Le,5),I(o,Be,5),I(o,Ga,5)),e&2){let r;g(r=b())&&(i.cell=r.first),g(r=b())&&(i.headerCell=r.first),g(r=b())&&(i.footerCell=r.first)}},inputs:{name:[0,"cdkColumnDef","name"],sticky:[2,"sticky","sticky",C],stickyEnd:[2,"stickyEnd","stickyEnd",C]},features:[A([{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:a}])]})}return a})(),Pe=class{constructor(n,t){t.nativeElement.classList.add(...n._columnCssClassName)}},Wa=(()=>{class a extends Pe{constructor(){super(s(jt),s(E))}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["cdk-header-cell"],["th","cdk-header-cell",""]],hostAttrs:["role","columnheader",1,"cdk-header-cell"],features:[R]})}return a})();var $a=(()=>{class a extends Pe{constructor(){let t=s(jt),e=s(E);super(t,e);let i=t._table?._getCellRole();i&&e.nativeElement.setAttribute("role",i)}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["cdk-cell"],["td","cdk-cell",""]],hostAttrs:[1,"cdk-cell"],features:[R]})}return a})(),Fe=class{tasks=[];endTasks=[]},Ae=new B("_COALESCED_STYLE_SCHEDULER"),mi=(()=>{class a{_currentSchedule=null;_ngZone=s(ot);constructor(){}schedule(t){this._createScheduleIfNeeded(),this._currentSchedule.tasks.push(t)}scheduleEnd(t){this._createScheduleIfNeeded(),this._currentSchedule.endTasks.push(t)}_createScheduleIfNeeded(){this._currentSchedule||(this._currentSchedule=new Fe,this._ngZone.runOutsideAngular(()=>queueMicrotask(()=>{for(;this._currentSchedule.tasks.length||this._currentSchedule.endTasks.length;){let t=this._currentSchedule;this._currentSchedule=new Fe;for(let e of t.tasks)e();for(let e of t.endTasks)e()}this._currentSchedule=null})))}static \u0275fac=function(e){return new(e||a)};static \u0275prov=bt({token:a,factory:a.\u0275fac})}return a})();var hi=(()=>{class a{template=s(X);_differs=s(It);columns;_columnsDiffer;constructor(){}ngOnChanges(t){if(!this._columnsDiffer){let e=t.columns&&t.columns.currentValue||[];this._columnsDiffer=this._differs.find(e).create(),this._columnsDiffer.diff(e)}}getColumnsDiff(){return this._columnsDiffer.diff(this.columns)}extractCellTemplate(t){return this instanceof $t?t.headerCell.template:this instanceof pi?t.footerCell.template:t.cell.template}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,features:[kt]})}return a})(),$t=(()=>{class a extends hi{_table=s(it,{optional:!0});_hasStickyChanged=!1;get sticky(){return this._sticky}set sticky(t){t!==this._sticky&&(this._sticky=t,this._hasStickyChanged=!0)}_sticky=!1;constructor(){super(s(X),s(It))}ngOnChanges(t){super.ngOnChanges(t)}hasStickyChanged(){let t=this._hasStickyChanged;return this.resetStickyChanged(),t}resetStickyChanged(){this._hasStickyChanged=!1}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkHeaderRowDef",""]],inputs:{columns:[0,"cdkHeaderRowDef","columns"],sticky:[2,"cdkHeaderRowDefSticky","sticky",C]},features:[R,kt]})}return a})(),pi=(()=>{class a extends hi{_table=s(it,{optional:!0});_hasStickyChanged=!1;get sticky(){return this._sticky}set sticky(t){t!==this._sticky&&(this._sticky=t,this._hasStickyChanged=!0)}_sticky=!1;constructor(){super(s(X),s(It))}ngOnChanges(t){super.ngOnChanges(t)}hasStickyChanged(){let t=this._hasStickyChanged;return this.resetStickyChanged(),t}resetStickyChanged(){this._hasStickyChanged=!1}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkFooterRowDef",""]],inputs:{columns:[0,"cdkFooterRowDef","columns"],sticky:[2,"cdkFooterRowDefSticky","sticky",C]},features:[R,kt]})}return a})(),Ne=(()=>{class a extends hi{_table=s(it,{optional:!0});when;constructor(){super(s(X),s(It))}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkRowDef",""]],inputs:{columns:[0,"cdkRowDefColumns","columns"],when:[0,"cdkRowDefWhen","when"]},features:[R]})}return a})(),St=(()=>{class a{_viewContainer=s(st);cells;context;static mostRecentCellOutlet=null;constructor(){a.mostRecentCellOutlet=this}ngOnDestroy(){a.mostRecentCellOutlet===this&&(a.mostRecentCellOutlet=null)}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","cdkCellOutlet",""]]})}return a})(),ui=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["cdk-header-row"],["tr","cdk-header-row",""]],hostAttrs:["role","row",1,"cdk-header-row"],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,i){e&1&&G(0,0)},dependencies:[St],encapsulation:2})}return a})();var fi=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["cdk-row"],["tr","cdk-row",""]],hostAttrs:["role","row",1,"cdk-row"],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,i){e&1&&G(0,0)},dependencies:[St],encapsulation:2})}return a})(),Ya=(()=>{class a{templateRef=s(X);_contentClassName="cdk-no-data-row";constructor(){}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["ng-template","cdkNoDataRow",""]]})}return a})(),Ua=["top","bottom","left","right"],di=class{_isNativeHtmlTable;_stickCellCss;direction;_coalescedStyleScheduler;_isBrowser;_needsPositionStickyOnElement;_positionListener;_tableInjector;_elemSizeCache=new WeakMap;_resizeObserver=globalThis?.ResizeObserver?new globalThis.ResizeObserver(n=>this._updateCachedSizes(n)):null;_updatedStickyColumnsParamsToReplay=[];_stickyColumnsReplayTimeout=null;_cachedCellWidths=[];_borderCellCss;_destroyed=!1;constructor(n,t,e,i,o=!0,r=!0,m,u){this._isNativeHtmlTable=n,this._stickCellCss=t,this.direction=e,this._coalescedStyleScheduler=i,this._isBrowser=o,this._needsPositionStickyOnElement=r,this._positionListener=m,this._tableInjector=u,this._borderCellCss={top:`${t}-border-elem-top`,bottom:`${t}-border-elem-bottom`,left:`${t}-border-elem-left`,right:`${t}-border-elem-right`}}clearStickyPositioning(n,t){(t.includes("left")||t.includes("right"))&&this._removeFromStickyColumnReplayQueue(n);let e=[];for(let i of n)i.nodeType===i.ELEMENT_NODE&&e.push(i,...Array.from(i.children));this._afterNextRender({write:()=>{for(let i of e)this._removeStickyStyle(i,t)}})}updateStickyColumns(n,t,e,i=!0,o=!0){if(!n.length||!this._isBrowser||!(t.some(tt=>tt)||e.some(tt=>tt))){this._positionListener?.stickyColumnsUpdated({sizes:[]}),this._positionListener?.stickyEndColumnsUpdated({sizes:[]});return}let r=n[0],m=r.children.length,u=this.direction==="rtl",M=u?"right":"left",V=u?"left":"right",z=t.lastIndexOf(!0),mt=e.indexOf(!0),_t,xi,ki;o&&this._updateStickyColumnReplayQueue({rows:[...n],stickyStartStates:[...t],stickyEndStates:[...e]}),this._afterNextRender({earlyRead:()=>{_t=this._getCellWidths(r,i),xi=this._getStickyStartColumnPositions(_t,t),ki=this._getStickyEndColumnPositions(_t,e)},write:()=>{for(let tt of n)for(let Y=0;Y<m;Y++){let Di=tt.children[Y];t[Y]&&this._addStickyStyle(Di,M,xi[Y],Y===z),e[Y]&&this._addStickyStyle(Di,V,ki[Y],Y===mt)}this._positionListener&&_t.some(tt=>!!tt)&&(this._positionListener.stickyColumnsUpdated({sizes:z===-1?[]:_t.slice(0,z+1).map((tt,Y)=>t[Y]?tt:null)}),this._positionListener.stickyEndColumnsUpdated({sizes:mt===-1?[]:_t.slice(mt).map((tt,Y)=>e[Y+mt]?tt:null).reverse()}))}})}stickRows(n,t,e){if(!this._isBrowser)return;let i=e==="bottom"?n.slice().reverse():n,o=e==="bottom"?t.slice().reverse():t,r=[],m=[],u=[];this._afterNextRender({earlyRead:()=>{for(let M=0,V=0;M<i.length;M++){if(!o[M])continue;r[M]=V;let z=i[M];u[M]=this._isNativeHtmlTable?Array.from(z.children):[z];let mt=this._retrieveElementSize(z).height;V+=mt,m[M]=mt}},write:()=>{let M=o.lastIndexOf(!0);for(let V=0;V<i.length;V++){if(!o[V])continue;let z=r[V],mt=V===M;for(let _t of u[V])this._addStickyStyle(_t,e,z,mt)}e==="top"?this._positionListener?.stickyHeaderRowsUpdated({sizes:m,offsets:r,elements:u}):this._positionListener?.stickyFooterRowsUpdated({sizes:m,offsets:r,elements:u})}})}updateStickyFooterContainer(n,t){this._isNativeHtmlTable&&this._afterNextRender({write:()=>{let e=n.querySelector("tfoot");e&&(t.some(i=>!i)?this._removeStickyStyle(e,["bottom"]):this._addStickyStyle(e,"bottom",0,!1))}})}destroy(){this._stickyColumnsReplayTimeout&&clearTimeout(this._stickyColumnsReplayTimeout),this._resizeObserver?.disconnect(),this._destroyed=!0}_removeStickyStyle(n,t){if(!n.classList.contains(this._stickCellCss))return;for(let i of t)n.style[i]="",n.classList.remove(this._borderCellCss[i]);Ua.some(i=>t.indexOf(i)===-1&&n.style[i])?n.style.zIndex=this._getCalculatedZIndex(n):(n.style.zIndex="",this._needsPositionStickyOnElement&&(n.style.position=""),n.classList.remove(this._stickCellCss))}_addStickyStyle(n,t,e,i){n.classList.add(this._stickCellCss),i&&n.classList.add(this._borderCellCss[t]),n.style[t]=`${e}px`,n.style.zIndex=this._getCalculatedZIndex(n),this._needsPositionStickyOnElement&&(n.style.cssText+="position: -webkit-sticky; position: sticky; ")}_getCalculatedZIndex(n){let t={top:100,bottom:10,left:1,right:1},e=0;for(let i of Ua)n.style[i]&&(e+=t[i]);return e?`${e}`:""}_getCellWidths(n,t=!0){if(!t&&this._cachedCellWidths.length)return this._cachedCellWidths;let e=[],i=n.children;for(let o=0;o<i.length;o++){let r=i[o];e.push(this._retrieveElementSize(r).width)}return this._cachedCellWidths=e,e}_getStickyStartColumnPositions(n,t){let e=[],i=0;for(let o=0;o<n.length;o++)t[o]&&(e[o]=i,i+=n[o]);return e}_getStickyEndColumnPositions(n,t){let e=[],i=0;for(let o=n.length;o>0;o--)t[o]&&(e[o]=i,i+=n[o]);return e}_retrieveElementSize(n){let t=this._elemSizeCache.get(n);if(t)return t;let e=n.getBoundingClientRect(),i={width:e.width,height:e.height};return this._resizeObserver&&(this._elemSizeCache.set(n,i),this._resizeObserver.observe(n,{box:"border-box"})),i}_updateStickyColumnReplayQueue(n){this._removeFromStickyColumnReplayQueue(n.rows),this._stickyColumnsReplayTimeout||this._updatedStickyColumnsParamsToReplay.push(n)}_removeFromStickyColumnReplayQueue(n){let t=new Set(n);for(let e of this._updatedStickyColumnsParamsToReplay)e.rows=e.rows.filter(i=>!t.has(i));this._updatedStickyColumnsParamsToReplay=this._updatedStickyColumnsParamsToReplay.filter(e=>!!e.rows.length)}_updateCachedSizes(n){let t=!1;for(let e of n){let i=e.borderBoxSize?.length?{width:e.borderBoxSize[0].inlineSize,height:e.borderBoxSize[0].blockSize}:{width:e.contentRect.width,height:e.contentRect.height};i.width!==this._elemSizeCache.get(e.target)?.width&&Lo(e.target)&&(t=!0),this._elemSizeCache.set(e.target,i)}t&&this._updatedStickyColumnsParamsToReplay.length&&(this._stickyColumnsReplayTimeout&&clearTimeout(this._stickyColumnsReplayTimeout),this._stickyColumnsReplayTimeout=setTimeout(()=>{if(!this._destroyed){for(let e of this._updatedStickyColumnsParamsToReplay)this.updateStickyColumns(e.rows,e.stickyStartStates,e.stickyEndStates,!0,!1);this._updatedStickyColumnsParamsToReplay=[],this._stickyColumnsReplayTimeout=null}},0))}_afterNextRender(n){this._tableInjector?rt(n,{injector:this._tableInjector}):this._coalescedStyleScheduler.schedule(()=>{n.earlyRead?.(),n.write()})}};function Lo(a){return["cdk-cell","cdk-header-cell","cdk-footer-cell"].some(n=>a.classList.contains(n))}var Oe=new B("CDK_SPL");var _i=(()=>{class a{viewContainer=s(st);elementRef=s(E);constructor(){let t=s(it);t._rowOutlet=this,t._outletAssigned()}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","rowOutlet",""]]})}return a})(),gi=(()=>{class a{viewContainer=s(st);elementRef=s(E);constructor(){let t=s(it);t._headerRowOutlet=this,t._outletAssigned()}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","headerRowOutlet",""]]})}return a})(),bi=(()=>{class a{viewContainer=s(st);elementRef=s(E);constructor(){let t=s(it);t._footerRowOutlet=this,t._outletAssigned()}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","footerRowOutlet",""]]})}return a})(),vi=(()=>{class a{viewContainer=s(st);elementRef=s(E);constructor(){let t=s(it);t._noDataRowOutlet=this,t._outletAssigned()}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","noDataRowOutlet",""]]})}return a})();var yi=(()=>{class a{_differs=s(It);_changeDetectorRef=s(et);_elementRef=s(E);_dir=s(ut,{optional:!0});_platform=s(Rt);_viewRepeater=s(Wt);_coalescedStyleScheduler=s(Ae);_viewportRuler=s(le);_stickyPositioningListener=s(Oe,{optional:!0,skipSelf:!0});_document=s(Mt);_data;_onDestroy=new j;_renderRows;_renderChangeSubscription;_columnDefsByName=new Map;_rowDefs;_headerRowDefs;_footerRowDefs;_dataDiffer;_defaultRowDef;_customColumnDefs=new Set;_customRowDefs=new Set;_customHeaderRowDefs=new Set;_customFooterRowDefs=new Set;_customNoDataRow;_headerRowDefChanged=!0;_footerRowDefChanged=!0;_stickyColumnStylesNeedReset=!0;_forceRecalculateCellWidths=!0;_cachedRenderRowsMap=new Map;_isNativeHtmlTable;_stickyStyler;stickyCssClass="cdk-table-sticky";needsPositionStickyOnElement=!0;_isServer;_isShowingNoDataRow=!1;_hasAllOutlets=!1;_hasInitialized=!1;_getCellRole(){if(this._cellRoleInternal===void 0){let t=this._elementRef.nativeElement.getAttribute("role");return t==="grid"||t==="treegrid"?"gridcell":"cell"}return this._cellRoleInternal}_cellRoleInternal=void 0;get trackBy(){return this._trackByFn}set trackBy(t){this._trackByFn=t}_trackByFn;get dataSource(){return this._dataSource}set dataSource(t){this._dataSource!==t&&this._switchDataSource(t)}_dataSource;get multiTemplateDataRows(){return this._multiTemplateDataRows}set multiTemplateDataRows(t){this._multiTemplateDataRows=t,this._rowOutlet&&this._rowOutlet.viewContainer.length&&(this._forceRenderDataRows(),this.updateStickyColumnStyles())}_multiTemplateDataRows=!1;get fixedLayout(){return this._fixedLayout}set fixedLayout(t){this._fixedLayout=t,this._forceRecalculateCellWidths=!0,this._stickyColumnStylesNeedReset=!0}_fixedLayout=!1;contentChanged=new S;viewChange=new K({start:0,end:Number.MAX_VALUE});_rowOutlet;_headerRowOutlet;_footerRowOutlet;_noDataRowOutlet;_contentColumnDefs;_contentRowDefs;_contentHeaderRowDefs;_contentFooterRowDefs;_noDataRow;_injector=s(pt);constructor(){s(new He("role"),{optional:!0})||this._elementRef.nativeElement.setAttribute("role","table"),this._isServer=!this._platform.isBrowser,this._isNativeHtmlTable=this._elementRef.nativeElement.nodeName==="TABLE",this._dataDiffer=this._differs.find([]).create((e,i)=>this.trackBy?this.trackBy(i.dataIndex,i.data):i)}ngOnInit(){this._setupStickyStyler(),this._viewportRuler.change().pipe(D(this._onDestroy)).subscribe(()=>{this._forceRecalculateCellWidths=!0})}ngAfterContentInit(){this._hasInitialized=!0}ngAfterContentChecked(){this._canRender()&&this._render()}ngOnDestroy(){this._stickyStyler?.destroy(),[this._rowOutlet?.viewContainer,this._headerRowOutlet?.viewContainer,this._footerRowOutlet?.viewContainer,this._cachedRenderRowsMap,this._customColumnDefs,this._customRowDefs,this._customHeaderRowDefs,this._customFooterRowDefs,this._columnDefsByName].forEach(t=>{t?.clear()}),this._headerRowDefs=[],this._footerRowDefs=[],this._defaultRowDef=null,this._onDestroy.next(),this._onDestroy.complete(),ce(this.dataSource)&&this.dataSource.disconnect(this)}renderRows(){this._renderRows=this._getAllRenderRows();let t=this._dataDiffer.diff(this._renderRows);if(!t){this._updateNoDataRow(),this.contentChanged.next();return}let e=this._rowOutlet.viewContainer;this._viewRepeater.applyChanges(t,e,(i,o,r)=>this._getEmbeddedViewArgs(i.item,r),i=>i.item.data,i=>{i.operation===ea.INSERTED&&i.context&&this._renderCellTemplateForItem(i.record.item.rowDef,i.context)}),this._updateRowIndexContext(),t.forEachIdentityChange(i=>{let o=e.get(i.currentIndex);o.context.$implicit=i.item.data}),this._updateNoDataRow(),this.contentChanged.next(),this.updateStickyColumnStyles()}addColumnDef(t){this._customColumnDefs.add(t)}removeColumnDef(t){this._customColumnDefs.delete(t)}addRowDef(t){this._customRowDefs.add(t)}removeRowDef(t){this._customRowDefs.delete(t)}addHeaderRowDef(t){this._customHeaderRowDefs.add(t),this._headerRowDefChanged=!0}removeHeaderRowDef(t){this._customHeaderRowDefs.delete(t),this._headerRowDefChanged=!0}addFooterRowDef(t){this._customFooterRowDefs.add(t),this._footerRowDefChanged=!0}removeFooterRowDef(t){this._customFooterRowDefs.delete(t),this._footerRowDefChanged=!0}setNoDataRow(t){this._customNoDataRow=t}updateStickyHeaderRowStyles(){let t=this._getRenderedRows(this._headerRowOutlet);if(this._isNativeHtmlTable){let i=qa(this._headerRowOutlet,"thead");i&&(i.style.display=t.length?"":"none")}let e=this._headerRowDefs.map(i=>i.sticky);this._stickyStyler.clearStickyPositioning(t,["top"]),this._stickyStyler.stickRows(t,e,"top"),this._headerRowDefs.forEach(i=>i.resetStickyChanged())}updateStickyFooterRowStyles(){let t=this._getRenderedRows(this._footerRowOutlet);if(this._isNativeHtmlTable){let i=qa(this._footerRowOutlet,"tfoot");i&&(i.style.display=t.length?"":"none")}let e=this._footerRowDefs.map(i=>i.sticky);this._stickyStyler.clearStickyPositioning(t,["bottom"]),this._stickyStyler.stickRows(t,e,"bottom"),this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement,e),this._footerRowDefs.forEach(i=>i.resetStickyChanged())}updateStickyColumnStyles(){let t=this._getRenderedRows(this._headerRowOutlet),e=this._getRenderedRows(this._rowOutlet),i=this._getRenderedRows(this._footerRowOutlet);(this._isNativeHtmlTable&&!this._fixedLayout||this._stickyColumnStylesNeedReset)&&(this._stickyStyler.clearStickyPositioning([...t,...e,...i],["left","right"]),this._stickyColumnStylesNeedReset=!1),t.forEach((o,r)=>{this._addStickyColumnStyles([o],this._headerRowDefs[r])}),this._rowDefs.forEach(o=>{let r=[];for(let m=0;m<e.length;m++)this._renderRows[m].rowDef===o&&r.push(e[m]);this._addStickyColumnStyles(r,o)}),i.forEach((o,r)=>{this._addStickyColumnStyles([o],this._footerRowDefs[r])}),Array.from(this._columnDefsByName.values()).forEach(o=>o.resetStickyChanged())}_outletAssigned(){!this._hasAllOutlets&&this._rowOutlet&&this._headerRowOutlet&&this._footerRowOutlet&&this._noDataRowOutlet&&(this._hasAllOutlets=!0,this._canRender()&&this._render())}_canRender(){return this._hasAllOutlets&&this._hasInitialized}_render(){this._cacheRowDefs(),this._cacheColumnDefs(),!this._headerRowDefs.length&&!this._footerRowDefs.length&&this._rowDefs.length;let e=this._renderUpdatedColumns()||this._headerRowDefChanged||this._footerRowDefChanged;this._stickyColumnStylesNeedReset=this._stickyColumnStylesNeedReset||e,this._forceRecalculateCellWidths=e,this._headerRowDefChanged&&(this._forceRenderHeaderRows(),this._headerRowDefChanged=!1),this._footerRowDefChanged&&(this._forceRenderFooterRows(),this._footerRowDefChanged=!1),this.dataSource&&this._rowDefs.length>0&&!this._renderChangeSubscription?this._observeRenderChanges():this._stickyColumnStylesNeedReset&&this.updateStickyColumnStyles(),this._checkStickyStates()}_getAllRenderRows(){let t=[],e=this._cachedRenderRowsMap;if(this._cachedRenderRowsMap=new Map,!this._data)return t;for(let i=0;i<this._data.length;i++){let o=this._data[i],r=this._getRenderRowsForData(o,i,e.get(o));this._cachedRenderRowsMap.has(o)||this._cachedRenderRowsMap.set(o,new WeakMap);for(let m=0;m<r.length;m++){let u=r[m],M=this._cachedRenderRowsMap.get(u.data);M.has(u.rowDef)?M.get(u.rowDef).push(u):M.set(u.rowDef,[u]),t.push(u)}}return t}_getRenderRowsForData(t,e,i){return this._getRowDefs(t,e).map(r=>{let m=i&&i.has(r)?i.get(r):[];if(m.length){let u=m.shift();return u.dataIndex=e,u}else return{data:t,rowDef:r,dataIndex:e}})}_cacheColumnDefs(){this._columnDefsByName.clear(),Ee(this._getOwnDefs(this._contentColumnDefs),this._customColumnDefs).forEach(e=>{this._columnDefsByName.has(e.name),this._columnDefsByName.set(e.name,e)})}_cacheRowDefs(){this._headerRowDefs=Ee(this._getOwnDefs(this._contentHeaderRowDefs),this._customHeaderRowDefs),this._footerRowDefs=Ee(this._getOwnDefs(this._contentFooterRowDefs),this._customFooterRowDefs),this._rowDefs=Ee(this._getOwnDefs(this._contentRowDefs),this._customRowDefs);let t=this._rowDefs.filter(e=>!e.when);!this.multiTemplateDataRows&&t.length>1,this._defaultRowDef=t[0]}_renderUpdatedColumns(){let t=(r,m)=>{let u=!!m.getColumnsDiff();return r||u},e=this._rowDefs.reduce(t,!1);e&&this._forceRenderDataRows();let i=this._headerRowDefs.reduce(t,!1);i&&this._forceRenderHeaderRows();let o=this._footerRowDefs.reduce(t,!1);return o&&this._forceRenderFooterRows(),e||i||o}_switchDataSource(t){this._data=[],ce(this.dataSource)&&this.dataSource.disconnect(this),this._renderChangeSubscription&&(this._renderChangeSubscription.unsubscribe(),this._renderChangeSubscription=null),t||(this._dataDiffer&&this._dataDiffer.diff([]),this._rowOutlet&&this._rowOutlet.viewContainer.clear()),this._dataSource=t}_observeRenderChanges(){if(!this.dataSource)return;let t;ce(this.dataSource)?t=this.dataSource.connect(this):Ii(this.dataSource)?t=this.dataSource:Array.isArray(this.dataSource)&&(t=gt(this.dataSource)),this._renderChangeSubscription=t.pipe(D(this._onDestroy)).subscribe(e=>{this._data=e||[],this.renderRows()})}_forceRenderHeaderRows(){this._headerRowOutlet.viewContainer.length>0&&this._headerRowOutlet.viewContainer.clear(),this._headerRowDefs.forEach((t,e)=>this._renderRow(this._headerRowOutlet,t,e)),this.updateStickyHeaderRowStyles()}_forceRenderFooterRows(){this._footerRowOutlet.viewContainer.length>0&&this._footerRowOutlet.viewContainer.clear(),this._footerRowDefs.forEach((t,e)=>this._renderRow(this._footerRowOutlet,t,e)),this.updateStickyFooterRowStyles()}_addStickyColumnStyles(t,e){let i=Array.from(e?.columns||[]).map(m=>{let u=this._columnDefsByName.get(m);return u}),o=i.map(m=>m.sticky),r=i.map(m=>m.stickyEnd);this._stickyStyler.updateStickyColumns(t,o,r,!this._fixedLayout||this._forceRecalculateCellWidths)}_getRenderedRows(t){let e=[];for(let i=0;i<t.viewContainer.length;i++){let o=t.viewContainer.get(i);e.push(o.rootNodes[0])}return e}_getRowDefs(t,e){if(this._rowDefs.length==1)return[this._rowDefs[0]];let i=[];if(this.multiTemplateDataRows)i=this._rowDefs.filter(o=>!o.when||o.when(e,t));else{let o=this._rowDefs.find(r=>r.when&&r.when(e,t))||this._defaultRowDef;o&&i.push(o)}return i.length,i}_getEmbeddedViewArgs(t,e){let i=t.rowDef,o={$implicit:t.data};return{templateRef:i.template,context:o,index:e}}_renderRow(t,e,i,o={}){let r=t.viewContainer.createEmbeddedView(e.template,o,i);return this._renderCellTemplateForItem(e,o),r}_renderCellTemplateForItem(t,e){for(let i of this._getCellTemplates(t))St.mostRecentCellOutlet&&St.mostRecentCellOutlet._viewContainer.createEmbeddedView(i,e);this._changeDetectorRef.markForCheck()}_updateRowIndexContext(){let t=this._rowOutlet.viewContainer;for(let e=0,i=t.length;e<i;e++){let r=t.get(e).context;r.count=i,r.first=e===0,r.last=e===i-1,r.even=e%2===0,r.odd=!r.even,this.multiTemplateDataRows?(r.dataIndex=this._renderRows[e].dataIndex,r.renderIndex=e):r.index=this._renderRows[e].dataIndex}}_getCellTemplates(t){return!t||!t.columns?[]:Array.from(t.columns,e=>{let i=this._columnDefsByName.get(e);return t.extractCellTemplate(i)})}_forceRenderDataRows(){this._dataDiffer.diff([]),this._rowOutlet.viewContainer.clear(),this.renderRows()}_checkStickyStates(){let t=(e,i)=>e||i.hasStickyChanged();this._headerRowDefs.reduce(t,!1)&&this.updateStickyHeaderRowStyles(),this._footerRowDefs.reduce(t,!1)&&this.updateStickyFooterRowStyles(),Array.from(this._columnDefsByName.values()).reduce(t,!1)&&(this._stickyColumnStylesNeedReset=!0,this.updateStickyColumnStyles())}_setupStickyStyler(){let t=this._dir?this._dir.value:"ltr";this._stickyStyler=new di(this._isNativeHtmlTable,this.stickyCssClass,t,this._coalescedStyleScheduler,this._platform.isBrowser,this.needsPositionStickyOnElement,this._stickyPositioningListener,this._injector),(this._dir?this._dir.change:gt()).pipe(D(this._onDestroy)).subscribe(e=>{this._stickyStyler.direction=e,this.updateStickyColumnStyles()})}_getOwnDefs(t){return t.filter(e=>!e._table||e._table===this)}_updateNoDataRow(){let t=this._customNoDataRow||this._noDataRow;if(!t)return;let e=this._rowOutlet.viewContainer.length===0;if(e===this._isShowingNoDataRow)return;let i=this._noDataRowOutlet.viewContainer;if(e){let o=i.createEmbeddedView(t.templateRef),r=o.rootNodes[0];o.rootNodes.length===1&&r?.nodeType===this._document.ELEMENT_NODE&&(r.setAttribute("role","row"),r.classList.add(t._contentClassName))}else i.clear();this._isShowingNoDataRow=e,this._changeDetectorRef.markForCheck()}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["cdk-table"],["table","cdk-table",""]],contentQueries:function(e,i,o){if(e&1&&(I(o,Ya,5),I(o,jt,5),I(o,Ne,5),I(o,$t,5),I(o,pi,5)),e&2){let r;g(r=b())&&(i._noDataRow=r.first),g(r=b())&&(i._contentColumnDefs=r),g(r=b())&&(i._contentRowDefs=r),g(r=b())&&(i._contentHeaderRowDefs=r),g(r=b())&&(i._contentFooterRowDefs=r)}},hostAttrs:[1,"cdk-table"],hostVars:2,hostBindings:function(e,i){e&2&&P("cdk-table-fixed-layout",i.fixedLayout)},inputs:{trackBy:"trackBy",dataSource:"dataSource",multiTemplateDataRows:[2,"multiTemplateDataRows","multiTemplateDataRows",C],fixedLayout:[2,"fixedLayout","fixedLayout",C]},outputs:{contentChanged:"contentChanged"},exportAs:["cdkTable"],features:[A([{provide:it,useExisting:a},{provide:Wt,useClass:be},{provide:Ae,useClass:mi},{provide:Oe,useValue:null}])],ngContentSelectors:Po,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(e,i){e&1&&(Z(Eo),F(0),F(1,1),y(2,Fo,1,0)(3,Ao,7,0)(4,Oo,4,0)),e&2&&(d(2),q(i._isServer?2:-1),d(),q(i._isNativeHtmlTable?3:4))},dependencies:[gi,_i,vi,bi],styles:[`.cdk-table-fixed-layout{table-layout:fixed}
`],encapsulation:2})}return a})();function Ee(a,n){return a.concat(Array.from(n))}function qa(a,n){let t=n.toUpperCase(),e=a.viewContainer.element.nativeElement;for(;e;){let i=e.nodeType===1?e.nodeName:null;if(i===t)return e;if(i==="TABLE")break;e=e.parentNode}return null}var Ka=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=ct({type:a});static \u0275inj=nt({imports:[oa]})}return a})();var Bo=[[["caption"]],[["colgroup"],["col"]],"*"],No=["caption","colgroup, col","*"];function Ho(a,n){a&1&&F(0,2)}function Vo(a,n){a&1&&(c(0,"thead",0),G(1,1),l(),c(2,"tbody",2),G(3,3)(4,4),l(),c(5,"tfoot",0),G(6,5),l())}function zo(a,n){a&1&&G(0,1)(1,3)(2,4)(3,5)}var jc=(()=>{class a extends yi{stickyCssClass="mat-mdc-table-sticky";needsPositionStickyOnElement=!1;static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275cmp=k({type:a,selectors:[["mat-table"],["table","mat-table",""]],hostAttrs:[1,"mat-mdc-table","mdc-data-table__table"],hostVars:2,hostBindings:function(e,i){e&2&&P("mdc-table-fixed-layout",i.fixedLayout)},exportAs:["matTable"],features:[A([{provide:yi,useExisting:a},{provide:it,useExisting:a},{provide:Ae,useClass:mi},{provide:Wt,useClass:be},{provide:Oe,useValue:null}]),R],ngContentSelectors:No,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["role","rowgroup",1,"mdc-data-table__content"],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(e,i){e&1&&(Z(Bo),F(0),F(1,1),y(2,Ho,1,0)(3,Vo,7,0)(4,zo,4,0)),e&2&&(d(2),q(i._isServer?2:-1),d(),q(i._isNativeHtmlTable?3:4))},dependencies:[gi,_i,vi,bi],styles:[`.mat-mdc-table-sticky{position:sticky !important}mat-table{display:block}mat-header-row{min-height:var(--mat-table-header-container-height, 56px)}mat-row{min-height:var(--mat-table-row-item-container-height, 52px)}mat-footer-row{min-height:var(--mat-table-footer-container-height, 52px)}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{min-width:100%;border:0;border-spacing:0;table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color, var(--mat-sys-surface))}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell{text-align:right}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-header-headline-font, var(--mat-sys-title-small-font, Roboto, sans-serif));line-height:var(--mat-table-header-headline-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-table-header-headline-size, var(--mat-sys-title-small-size, 14px));font-weight:var(--mat-table-header-headline-weight, var(--mat-sys-title-small-weight, 500))}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-row-item-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-row-item-label-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-row-item-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, var(--mat-sys-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mat-table-footer-supporting-text-font, var(--mat-sys-body-medium-font, Roboto, sans-serif));line-height:var(--mat-table-footer-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-table-footer-supporting-text-size, var(--mat-sys-body-medium-size, 14px));font-weight:var(--mat-table-footer-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-table-footer-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking, var(--mat-sys-title-small-tracking));font-weight:inherit;line-height:inherit;box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mat-mdc-header-cell{text-align:right}.mdc-data-table__row:last-child>.mat-mdc-header-cell{border-bottom:none}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, var(--mat-sys-outline, rgba(0, 0, 0, 0.12)));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking));line-height:inherit}.mdc-data-table__row:last-child>.mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking, var(--mat-sys-body-medium-tracking))}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}
`],encapsulation:2})}return a})(),Qc=(()=>{class a extends Le{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","matCellDef",""]],features:[A([{provide:Le,useExisting:a}]),R]})}return a})(),Uc=(()=>{class a extends Be{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","matHeaderCellDef",""]],features:[A([{provide:Be,useExisting:a}]),R]})}return a})();var qc=(()=>{class a extends jt{get name(){return this._name}set name(t){this._setNameInput(t)}_updateColumnCssClassName(){super._updateColumnCssClassName(),this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`)}static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","matColumnDef",""]],inputs:{name:[0,"matColumnDef","name"]},features:[A([{provide:jt,useExisting:a},{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:a}]),R]})}return a})(),Gc=(()=>{class a extends Wa{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["mat-header-cell"],["th","mat-header-cell",""]],hostAttrs:["role","columnheader",1,"mat-mdc-header-cell","mdc-data-table__header-cell"],features:[R]})}return a})();var Wc=(()=>{class a extends $a{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["mat-cell"],["td","mat-cell",""]],hostAttrs:[1,"mat-mdc-cell","mdc-data-table__cell"],features:[R]})}return a})();var $c=(()=>{class a extends $t{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","matHeaderRowDef",""]],inputs:{columns:[0,"matHeaderRowDef","columns"],sticky:[2,"matHeaderRowDefSticky","sticky",C]},features:[A([{provide:$t,useExisting:a}]),R]})}return a})();var Yc=(()=>{class a extends Ne{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275dir=v({type:a,selectors:[["","matRowDef",""]],inputs:{columns:[0,"matRowDefColumns","columns"],when:[0,"matRowDefWhen","when"]},features:[A([{provide:Ne,useExisting:a}]),R]})}return a})(),Kc=(()=>{class a extends ui{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275cmp=k({type:a,selectors:[["mat-header-row"],["tr","mat-header-row",""]],hostAttrs:["role","row",1,"mat-mdc-header-row","mdc-data-table__header-row"],exportAs:["matHeaderRow"],features:[A([{provide:ui,useExisting:a}]),R],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,i){e&1&&G(0,0)},dependencies:[St],encapsulation:2})}return a})();var Xc=(()=>{class a extends fi{static \u0275fac=(()=>{let t;return function(i){return(t||(t=H(a)))(i||a)}})();static \u0275cmp=k({type:a,selectors:[["mat-row"],["tr","mat-row",""]],hostAttrs:["role","row",1,"mat-mdc-row","mdc-data-table__row"],exportAs:["matRow"],features:[A([{provide:fi,useExisting:a}]),R],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,i){e&1&&G(0,0)},dependencies:[St],encapsulation:2})}return a})();var Zc=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=ct({type:a});static \u0275inj=nt({imports:[J,Ka,J]})}return a})(),jo=9007199254740991,Xa=class extends ta{_data;_renderData=new K([]);_filter=new K("");_internalPageChanges=new j;_renderChangesSubscription=null;filteredData;get data(){return this._data.value}set data(n){n=Array.isArray(n)?n:[],this._data.next(n),this._renderChangesSubscription||this._filterData(n)}get filter(){return this._filter.value}set filter(n){this._filter.next(n),this._renderChangesSubscription||this._filterData(this.data)}get sort(){return this._sort}set sort(n){this._sort=n,this._updateChangeSubscription()}_sort;get paginator(){return this._paginator}set paginator(n){this._paginator=n,this._updateChangeSubscription()}_paginator;sortingDataAccessor=(n,t)=>{let e=n[t];if(ji(e)){let i=Number(e);return i<jo?i:e}return e};sortData=(n,t)=>{let e=t.active,i=t.direction;return!e||i==""?n:n.sort((o,r)=>{let m=this.sortingDataAccessor(o,e),u=this.sortingDataAccessor(r,e),M=typeof m,V=typeof u;M!==V&&(M==="number"&&(m+=""),V==="number"&&(u+=""));let z=0;return m!=null&&u!=null?m>u?z=1:m<u&&(z=-1):m!=null?z=1:u!=null&&(z=-1),z*(i=="asc"?1:-1)})};filterPredicate=(n,t)=>{let e=t.trim().toLowerCase();return Object.values(n).some(i=>`${i}`.toLowerCase().includes(e))};constructor(n=[]){super(),this._data=new K(n),this._updateChangeSubscription()}_updateChangeSubscription(){let n=this._sort?at(this._sort.sortChange,this._sort.initialized):gt(null),t=this._paginator?at(this._paginator.page,this._internalPageChanges,this._paginator.initialized):gt(null),e=this._data,i=Kt([e,this._filter]).pipe(Yt(([m])=>this._filterData(m))),o=Kt([i,n]).pipe(Yt(([m])=>this._orderData(m))),r=Kt([o,t]).pipe(Yt(([m])=>this._pageData(m)));this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=r.subscribe(m=>this._renderData.next(m))}_filterData(n){return this.filteredData=this.filter==null||this.filter===""?n:n.filter(t=>this.filterPredicate(t,this.filter)),this.paginator&&this._updatePaginator(this.filteredData.length),this.filteredData}_orderData(n){return this.sort?this.sortData(n.slice(),this.sort):n}_pageData(n){if(!this.paginator)return n;let t=this.paginator.pageIndex*this.paginator.pageSize;return n.slice(t,t+this.paginator.pageSize)}_updatePaginator(n){Promise.resolve().then(()=>{let t=this.paginator;if(t&&(t.length=n,t.pageIndex>0)){let e=Math.ceil(t.length/t.pageSize)-1||0,i=Math.min(t.pageIndex,e);i!==t.pageIndex&&(t.pageIndex=i,this._internalPageChanges.next())}})}connect(){return this._renderChangesSubscription||this._updateChangeSubscription(),this._renderData}disconnect(){this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=null}};var Uo=["tooltip"],Ci=20;var wi=new B("mat-tooltip-scroll-strategy",{providedIn:"root",factory:()=>{let a=s(de);return()=>a.scrollStrategies.reposition({scrollThrottle:Ci})}});function tn(a){return()=>a.scrollStrategies.reposition({scrollThrottle:Ci})}var en={provide:wi,deps:[de],useFactory:tn};function an(){return{showDelay:0,hideDelay:0,touchendHideDelay:1500}}var nn=new B("mat-tooltip-default-options",{providedIn:"root",factory:an});var Za="tooltip-panel",Ja=zi({passive:!0}),qo=8,Go=8,Wo=24,$o=200,Yo=(()=>{class a{_elementRef=s(E);_ngZone=s(ot);_platform=s(Rt);_ariaDescriber=s(Wi);_focusMonitor=s(Ut);_dir=s(ut);_injector=s(pt);_viewContainerRef=s(st);_defaultOptions=s(nn,{optional:!0});_overlayRef;_tooltipInstance;_portal;_position="below";_positionAtOrigin=!1;_disabled=!1;_tooltipClass;_viewInitialized=!1;_pointerExitEventsInitialized=!1;_tooltipComponent=on;_viewportMargin=8;_currentPosition;_cssClassPrefix="mat-mdc";_ariaDescriptionPending;_dirSubscribed=!1;get position(){return this._position}set position(t){t!==this._position&&(this._position=t,this._overlayRef&&(this._updatePosition(this._overlayRef),this._tooltipInstance?.show(0),this._overlayRef.updatePosition()))}get positionAtOrigin(){return this._positionAtOrigin}set positionAtOrigin(t){this._positionAtOrigin=We(t),this._detach(),this._overlayRef=null}get disabled(){return this._disabled}set disabled(t){let e=We(t);this._disabled!==e&&(this._disabled=e,e?this.hide(0):this._setupPointerEnterEventsIfNeeded(),this._syncAriaDescription(this.message))}get showDelay(){return this._showDelay}set showDelay(t){this._showDelay=Ge(t)}_showDelay;get hideDelay(){return this._hideDelay}set hideDelay(t){this._hideDelay=Ge(t),this._tooltipInstance&&(this._tooltipInstance._mouseLeaveHideDelay=this._hideDelay)}_hideDelay;touchGestures="auto";get message(){return this._message}set message(t){let e=this._message;this._message=t!=null?String(t).trim():"",!this._message&&this._isTooltipVisible()?this.hide(0):(this._setupPointerEnterEventsIfNeeded(),this._updateTooltipMessage()),this._syncAriaDescription(e)}_message="";get tooltipClass(){return this._tooltipClass}set tooltipClass(t){this._tooltipClass=t,this._tooltipInstance&&this._setTooltipClass(this._tooltipClass)}_passiveListeners=[];_touchstartTimeout=null;_destroyed=new j;_isDestroyed=!1;constructor(){let t=this._defaultOptions;t&&(this._showDelay=t.showDelay,this._hideDelay=t.hideDelay,t.position&&(this.position=t.position),t.positionAtOrigin&&(this.positionAtOrigin=t.positionAtOrigin),t.touchGestures&&(this.touchGestures=t.touchGestures),t.tooltipClass&&(this.tooltipClass=t.tooltipClass)),this._viewportMargin=qo}ngAfterViewInit(){this._viewInitialized=!0,this._setupPointerEnterEventsIfNeeded(),this._focusMonitor.monitor(this._elementRef).pipe(D(this._destroyed)).subscribe(t=>{t?t==="keyboard"&&this._ngZone.run(()=>this.show()):this._ngZone.run(()=>this.hide(0))})}ngOnDestroy(){let t=this._elementRef.nativeElement;this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this._overlayRef&&(this._overlayRef.dispose(),this._tooltipInstance=null),this._passiveListeners.forEach(([e,i])=>{t.removeEventListener(e,i,Ja)}),this._passiveListeners.length=0,this._destroyed.next(),this._destroyed.complete(),this._isDestroyed=!0,this._ariaDescriber.removeDescription(t,this.message,"tooltip"),this._focusMonitor.stopMonitoring(t)}show(t=this.showDelay,e){if(this.disabled||!this.message||this._isTooltipVisible()){this._tooltipInstance?._cancelPendingAnimations();return}let i=this._createOverlay(e);this._detach(),this._portal=this._portal||new Xi(this._tooltipComponent,this._viewContainerRef);let o=this._tooltipInstance=i.attach(this._portal).instance;o._triggerElement=this._elementRef.nativeElement,o._mouseLeaveHideDelay=this._hideDelay,o.afterHidden().pipe(D(this._destroyed)).subscribe(()=>this._detach()),this._setTooltipClass(this._tooltipClass),this._updateTooltipMessage(),o.show(t)}hide(t=this.hideDelay){let e=this._tooltipInstance;e&&(e.isVisible()?e.hide(t):(e._cancelPendingAnimations(),this._detach()))}toggle(t){this._isTooltipVisible()?this.hide():this.show(void 0,t)}_isTooltipVisible(){return!!this._tooltipInstance&&this._tooltipInstance.isVisible()}_createOverlay(t){if(this._overlayRef){let r=this._overlayRef.getConfig().positionStrategy;if((!this.positionAtOrigin||!t)&&r._origin instanceof E)return this._overlayRef;this._detach()}let e=this._injector.get(ia).getAncestorScrollContainers(this._elementRef),i=this._injector.get(de),o=i.position().flexibleConnectedTo(this.positionAtOrigin?t||this._elementRef:this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(!1).withViewportMargin(this._viewportMargin).withScrollableContainers(e);return o.positionChanges.pipe(D(this._destroyed)).subscribe(r=>{this._updateCurrentPositionClass(r.connectionPair),this._tooltipInstance&&r.scrollableViewProperties.isOverlayClipped&&this._tooltipInstance.isVisible()&&this._ngZone.run(()=>this.hide(0))}),this._overlayRef=i.create({direction:this._dir,positionStrategy:o,panelClass:`${this._cssClassPrefix}-${Za}`,scrollStrategy:this._injector.get(wi)()}),this._updatePosition(this._overlayRef),this._overlayRef.detachments().pipe(D(this._destroyed)).subscribe(()=>this._detach()),this._overlayRef.outsidePointerEvents().pipe(D(this._destroyed)).subscribe(()=>this._tooltipInstance?._handleBodyInteraction()),this._overlayRef.keydownEvents().pipe(D(this._destroyed)).subscribe(r=>{this._isTooltipVisible()&&r.keyCode===27&&!ne(r)&&(r.preventDefault(),r.stopPropagation(),this._ngZone.run(()=>this.hide(0)))}),this._defaultOptions?.disableTooltipInteractivity&&this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`),this._dirSubscribed||(this._dirSubscribed=!0,this._dir.change.pipe(D(this._destroyed)).subscribe(()=>{this._overlayRef&&this._updatePosition(this._overlayRef)})),this._overlayRef}_detach(){this._overlayRef&&this._overlayRef.hasAttached()&&this._overlayRef.detach(),this._tooltipInstance=null}_updatePosition(t){let e=t.getConfig().positionStrategy,i=this._getOrigin(),o=this._getOverlayPosition();e.withPositions([this._addOffset(wt(wt({},i.main),o.main)),this._addOffset(wt(wt({},i.fallback),o.fallback))])}_addOffset(t){let e=Go,i=!this._dir||this._dir.value=="ltr";return t.originY==="top"?t.offsetY=-e:t.originY==="bottom"?t.offsetY=e:t.originX==="start"?t.offsetX=i?-e:e:t.originX==="end"&&(t.offsetX=i?e:-e),t}_getOrigin(){let t=!this._dir||this._dir.value=="ltr",e=this.position,i;e=="above"||e=="below"?i={originX:"center",originY:e=="above"?"top":"bottom"}:e=="before"||e=="left"&&t||e=="right"&&!t?i={originX:"start",originY:"center"}:(e=="after"||e=="right"&&t||e=="left"&&!t)&&(i={originX:"end",originY:"center"});let{x:o,y:r}=this._invertPosition(i.originX,i.originY);return{main:i,fallback:{originX:o,originY:r}}}_getOverlayPosition(){let t=!this._dir||this._dir.value=="ltr",e=this.position,i;e=="above"?i={overlayX:"center",overlayY:"bottom"}:e=="below"?i={overlayX:"center",overlayY:"top"}:e=="before"||e=="left"&&t||e=="right"&&!t?i={overlayX:"end",overlayY:"center"}:(e=="after"||e=="right"&&t||e=="left"&&!t)&&(i={overlayX:"start",overlayY:"center"});let{x:o,y:r}=this._invertPosition(i.overlayX,i.overlayY);return{main:i,fallback:{overlayX:o,overlayY:r}}}_updateTooltipMessage(){this._tooltipInstance&&(this._tooltipInstance.message=this.message,this._tooltipInstance._markForCheck(),rt(()=>{this._tooltipInstance&&this._overlayRef.updatePosition()},{injector:this._injector}))}_setTooltipClass(t){this._tooltipInstance&&(this._tooltipInstance.tooltipClass=t,this._tooltipInstance._markForCheck())}_invertPosition(t,e){return this.position==="above"||this.position==="below"?e==="top"?e="bottom":e==="bottom"&&(e="top"):t==="end"?t="start":t==="start"&&(t="end"),{x:t,y:e}}_updateCurrentPositionClass(t){let{overlayY:e,originX:i,originY:o}=t,r;if(e==="center"?this._dir&&this._dir.value==="rtl"?r=i==="end"?"left":"right":r=i==="start"?"left":"right":r=e==="bottom"&&o==="top"?"above":"below",r!==this._currentPosition){let m=this._overlayRef;if(m){let u=`${this._cssClassPrefix}-${Za}-`;m.removePanelClass(u+this._currentPosition),m.addPanelClass(u+r)}this._currentPosition=r}}_setupPointerEnterEventsIfNeeded(){this._disabled||!this.message||!this._viewInitialized||this._passiveListeners.length||(this._platformSupportsMouseEvents()?this._passiveListeners.push(["mouseenter",t=>{this._setupPointerExitEventsIfNeeded();let e;t.x!==void 0&&t.y!==void 0&&(e=t),this.show(void 0,e)}]):this.touchGestures!=="off"&&(this._disableNativeGesturesIfNecessary(),this._passiveListeners.push(["touchstart",t=>{let e=t.targetTouches?.[0],i=e?{x:e.clientX,y:e.clientY}:void 0;this._setupPointerExitEventsIfNeeded(),this._touchstartTimeout&&clearTimeout(this._touchstartTimeout);let o=500;this._touchstartTimeout=setTimeout(()=>{this._touchstartTimeout=null,this.show(void 0,i)},this._defaultOptions?.touchLongPressShowDelay??o)}])),this._addListeners(this._passiveListeners))}_setupPointerExitEventsIfNeeded(){if(this._pointerExitEventsInitialized)return;this._pointerExitEventsInitialized=!0;let t=[];if(this._platformSupportsMouseEvents())t.push(["mouseleave",e=>{let i=e.relatedTarget;(!i||!this._overlayRef?.overlayElement.contains(i))&&this.hide()}],["wheel",e=>this._wheelListener(e)]);else if(this.touchGestures!=="off"){this._disableNativeGesturesIfNecessary();let e=()=>{this._touchstartTimeout&&clearTimeout(this._touchstartTimeout),this.hide(this._defaultOptions?.touchendHideDelay)};t.push(["touchend",e],["touchcancel",e])}this._addListeners(t),this._passiveListeners.push(...t)}_addListeners(t){t.forEach(([e,i])=>{this._elementRef.nativeElement.addEventListener(e,i,Ja)})}_platformSupportsMouseEvents(){return!this._platform.IOS&&!this._platform.ANDROID}_wheelListener(t){if(this._isTooltipVisible()){let e=this._injector.get(Mt).elementFromPoint(t.clientX,t.clientY),i=this._elementRef.nativeElement;e!==i&&!i.contains(e)&&this.hide()}}_disableNativeGesturesIfNecessary(){let t=this.touchGestures;if(t!=="off"){let e=this._elementRef.nativeElement,i=e.style;(t==="on"||e.nodeName!=="INPUT"&&e.nodeName!=="TEXTAREA")&&(i.userSelect=i.msUserSelect=i.webkitUserSelect=i.MozUserSelect="none"),(t==="on"||!e.draggable)&&(i.webkitUserDrag="none"),i.touchAction="none",i.webkitTapHighlightColor="transparent"}}_syncAriaDescription(t){this._ariaDescriptionPending||(this._ariaDescriptionPending=!0,this._ariaDescriber.removeDescription(this._elementRef.nativeElement,t,"tooltip"),this._isDestroyed||rt({write:()=>{this._ariaDescriptionPending=!1,this.message&&!this.disabled&&this._ariaDescriber.describe(this._elementRef.nativeElement,this.message,"tooltip")}},{injector:this._injector}))}static \u0275fac=function(e){return new(e||a)};static \u0275dir=v({type:a,selectors:[["","matTooltip",""]],hostAttrs:[1,"mat-mdc-tooltip-trigger"],hostVars:2,hostBindings:function(e,i){e&2&&P("mat-mdc-tooltip-disabled",i.disabled)},inputs:{position:[0,"matTooltipPosition","position"],positionAtOrigin:[0,"matTooltipPositionAtOrigin","positionAtOrigin"],disabled:[0,"matTooltipDisabled","disabled"],showDelay:[0,"matTooltipShowDelay","showDelay"],hideDelay:[0,"matTooltipHideDelay","hideDelay"],touchGestures:[0,"matTooltipTouchGestures","touchGestures"],message:[0,"matTooltip","message"],tooltipClass:[0,"matTooltipClass","tooltipClass"]},exportAs:["matTooltip"]})}return a})(),on=(()=>{class a{_changeDetectorRef=s(et);_elementRef=s(E);_isMultiline=!1;message;tooltipClass;_showTimeoutId;_hideTimeoutId;_triggerElement;_mouseLeaveHideDelay;_animationsDisabled;_tooltip;_closeOnInteraction=!1;_isVisible=!1;_onHide=new j;_showAnimation="mat-mdc-tooltip-show";_hideAnimation="mat-mdc-tooltip-hide";constructor(){let t=s(yt,{optional:!0});this._animationsDisabled=t==="NoopAnimations"}show(t){this._hideTimeoutId!=null&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=setTimeout(()=>{this._toggleVisibility(!0),this._showTimeoutId=void 0},t)}hide(t){this._showTimeoutId!=null&&clearTimeout(this._showTimeoutId),this._hideTimeoutId=setTimeout(()=>{this._toggleVisibility(!1),this._hideTimeoutId=void 0},t)}afterHidden(){return this._onHide}isVisible(){return this._isVisible}ngOnDestroy(){this._cancelPendingAnimations(),this._onHide.complete(),this._triggerElement=null}_handleBodyInteraction(){this._closeOnInteraction&&this.hide(0)}_markForCheck(){this._changeDetectorRef.markForCheck()}_handleMouseLeave({relatedTarget:t}){(!t||!this._triggerElement.contains(t))&&(this.isVisible()?this.hide(this._mouseLeaveHideDelay):this._finalizeAnimation(!1))}_onShow(){this._isMultiline=this._isTooltipMultiline(),this._markForCheck()}_isTooltipMultiline(){let t=this._elementRef.nativeElement.getBoundingClientRect();return t.height>Wo&&t.width>=$o}_handleAnimationEnd({animationName:t}){(t===this._showAnimation||t===this._hideAnimation)&&this._finalizeAnimation(t===this._showAnimation)}_cancelPendingAnimations(){this._showTimeoutId!=null&&clearTimeout(this._showTimeoutId),this._hideTimeoutId!=null&&clearTimeout(this._hideTimeoutId),this._showTimeoutId=this._hideTimeoutId=void 0}_finalizeAnimation(t){t?this._closeOnInteraction=!0:this.isVisible()||this._onHide.next()}_toggleVisibility(t){let e=this._tooltip.nativeElement,i=this._showAnimation,o=this._hideAnimation;if(e.classList.remove(t?o:i),e.classList.add(t?i:o),this._isVisible!==t&&(this._isVisible=t,this._changeDetectorRef.markForCheck()),t&&!this._animationsDisabled&&typeof getComputedStyle=="function"){let r=getComputedStyle(e);(r.getPropertyValue("animation-duration")==="0s"||r.getPropertyValue("animation-name")==="none")&&(this._animationsDisabled=!0)}t&&this._onShow(),this._animationsDisabled&&(e.classList.add("_mat-animation-noopable"),this._finalizeAnimation(t))}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=k({type:a,selectors:[["mat-tooltip-component"]],viewQuery:function(e,i){if(e&1&&L(Uo,7),e&2){let o;g(o=b())&&(i._tooltip=o.first)}},hostAttrs:["aria-hidden","true"],hostBindings:function(e,i){e&1&&T("mouseleave",function(r){return i._handleMouseLeave(r)})},decls:4,vars:4,consts:[["tooltip",""],[1,"mdc-tooltip","mat-mdc-tooltip",3,"animationend","ngClass"],[1,"mat-mdc-tooltip-surface","mdc-tooltip__surface"]],template:function(e,i){if(e&1){let o=W();c(0,"div",1,0),T("animationend",function(m){return w(o),x(i._handleAnimationEnd(m))}),c(2,"div",2),h(3),l()()}e&2&&(P("mdc-tooltip--multiline",i._isMultiline),p("ngClass",i.tooltipClass),d(3),N(i.message))},dependencies:[Et],styles:[`.mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:"";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}
`],encapsulation:2,changeDetection:0})}return a})(),Ko=(()=>{class a{static \u0275fac=function(e){return new(e||a)};static \u0275mod=ct({type:a});static \u0275inj=nt({providers:[en],imports:[Gi,ra,J,J,na]})}return a})();export{De as a,Se as b,ai as c,Ea as d,Pa as e,Ct as f,Ba as g,ri as h,So as i,ic as j,jc as k,Qc as l,Uc as m,qc as n,Gc as o,Wc as p,$c as q,Yc as r,Kc as s,Xc as t,Zc as u,Xa as v,Yo as w,Ko as x};
