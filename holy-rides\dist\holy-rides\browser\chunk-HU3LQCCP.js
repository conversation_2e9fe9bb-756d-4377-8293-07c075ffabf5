import{a as y}from"./chunk-6WJZ7JAD.js";import"./chunk-FMN32A34.js";import"./chunk-H3RPDCYZ.js";import{c as S}from"./chunk-XGZOMXTW.js";import{C as j,F as q,H as D,I as G,J as A,b as P,d as h,f as I,g as E,k as x,n as w,o as B,s as N,u as O,w as R,x as k}from"./chunk-UDNA73AU.js";import{Ec as _,Fd as b,Id as F,Kb as n,Kd as T,La as l,Ld as V,Mb as v,Md as U,Pa as u,Pd as H,Qd as L,Wa as g,ab as c,hb as p,qb as r,rb as t,sb as d,vc as M,yb as C}from"./chunk-L5P5FOLI.js";import"./chunk-X5YLR3NI.js";import{i as s}from"./chunk-ODN5LVDJ.js";function J(a,e){a&1&&(r(0,"mat-error"),n(1,"Full name is required"),t())}function K(a,e){a&1&&(r(0,"mat-error"),n(1,"Phone number is required"),t())}var z=class a{constructor(e,i,o,m){this.formBuilder=e;this.authService=i;this.snackBar=o;this.router=m;this.profileForm=this.formBuilder.group({full_name:["",h.required],phone:["",h.required],email:[{value:"",disabled:!0}]})}profileForm;loading=!1;userId=null;ngOnInit(){return s(this,null,function*(){let e=yield this.authService.getCurrentUser();e&&(this.userId=e.id,this.profileForm.patchValue({email:e.email,full_name:e.full_name||"",phone:e.phone||""}))})}onSubmit(){return s(this,null,function*(){if(!(this.profileForm.invalid||!this.userId)){this.loading=!0;try{if(!(yield this.authService.updateProfile(this.profileForm.getRawValue())))throw new Error("Failed to update profile");this.snackBar.open("Profile updated successfully","Close",{duration:3e3});let i=yield this.authService.getUserRole();if(i)yield this.router.navigate([this.authService.getDashboardRouteForRole(i)]);else throw new Error("User role not found")}catch(e){this.snackBar.open(e.message||"An error occurred","Close",{duration:3e3})}finally{this.loading=!1}}})}static \u0275fac=function(i){return new(i||a)(u(N),u(A),u(y),u(S))};static \u0275cmp=g({type:a,selectors:[["app-profile"]],decls:24,vars:5,consts:[[1,"profile-container"],[3,"ngSubmit","formGroup"],["appearance","outline"],["matInput","","formControlName","email","readonly",""],["matInput","","formControlName","full_name","placeholder","Enter your full name"],[4,"ngIf"],["matInput","","formControlName","phone","placeholder","Enter your phone number"],[1,"button-container"],["mat-raised-button","","color","primary","type","submit",3,"disabled"]],template:function(i,o){if(i&1&&(r(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),n(4,"Profile"),t()(),r(5,"mat-card-content")(6,"form",1),C("ngSubmit",function(){return o.onSubmit()}),r(7,"mat-form-field",2)(8,"mat-label"),n(9,"Email"),t(),d(10,"input",3),t(),r(11,"mat-form-field",2)(12,"mat-label"),n(13,"Full Name"),t(),d(14,"input",4),c(15,J,2,0,"mat-error",5),t(),r(16,"mat-form-field",2)(17,"mat-label"),n(18,"Phone Number"),t(),d(19,"input",6),c(20,K,2,0,"mat-error",5),t(),r(21,"div",7)(22,"button",8),n(23),t()()()()()()),i&2){let m,f;l(6),p("formGroup",o.profileForm),l(9),p("ngIf",(m=o.profileForm.get("full_name"))==null||m.errors==null?null:m.errors.required),l(5),p("ngIf",(f=o.profileForm.get("phone"))==null||f.errors==null?null:f.errors.required),l(2),p("disabled",o.profileForm.invalid||o.loading),l(),v(" ",o.loading?"Saving...":"Save Changes"," ")}},dependencies:[_,M,O,x,P,I,E,w,B,q,j,R,k,G,D,F,b,L,T,U,H,V],styles:[".profile-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:flex-start;min-height:100vh;padding:20px;background-color:#f5f5f5}.profile-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:20px}.profile-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.profile-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{margin-top:24px;display:flex;justify-content:center}.profile-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;padding:8px}"]})};export{z as ProfileComponent};
